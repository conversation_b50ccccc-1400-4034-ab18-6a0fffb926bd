import json
import os

from celery import shared_task

from .autogen import generate
from .models import Column, Table


@shared_task()
def generate_metadata(schema, table_names):
    """check if metadata exist
    if not generate metadata file
    from file update table and columns descriptions"""
    file_name = f"{schema}_table_descriptions.json"
    base_dir = "metadata"
    file_path = os.path.join(base_dir, file_name)

    if not os.path.exists(file_path):
        generate(schema, table_names)

    with open(file_path) as file:
        data = json.load(file)
        tables_to_update = []
        columns_to_update = []

    for table_data in data:
        # Update or create the table description
        table_name = table_data["table_name"]
        table_description = table_data.get("description", "")

        # Retrieve or create the table
        try:
            table = Table.objects.get(name=table_name)
            table.description = table_description
            tables_to_update.append(table)

            # Update each column's description
            for column_data in table_data.get("columns", []):
                column_name = column_data["column_name"]
                column_description = column_data.get("description", "")

                # Retrieve or create the column with reference to the table
                column = Column.objects.get(name=column_name, table=table)
                column.description = column_description
            columns_to_update.append(column)
        except Table.DoesNotExist:
            pass  # Skip to the next table if this table doesn't exist

    # Perform bulk updates for tables and columns
    Table.objects.bulk_update(tables_to_update, ["description"])
    Column.objects.bulk_update(columns_to_update, ["description"])

    return f"{schema} metadata successfully generated"
