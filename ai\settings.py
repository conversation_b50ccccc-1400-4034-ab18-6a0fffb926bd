import logging
import os
import sys

from decouple import config
from langchain_google_genai import ChatG<PERSON>gleGenerativeAI

# langchain
from langchain_openai import ChatOpenAI as LangchainOpenAI
from llama_index.embeddings.gemini import GeminiEmbedding
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.gemini import Gemini as <PERSON>lamaG<PERSON><PERSON>
from llama_index.llms.openai import OpenAI as <PERSON>lam<PERSON><PERSON><PERSON>x<PERSON><PERSON><PERSON><PERSON>


def get_logger():
    # Configure logging
    logger = logging.getLogger("SherlockAI")
    logger.setLevel(logging.INFO)

    # Create a stdout handler
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)

    # Add the handler to the logger
    logger.addHandler(handler)
    return logger


class SherlockSettings:
    GOOGLE_GEMINI_API_KEY = config("GOOGLE_API_KEY")
    os.environ["GOOGLE_API_KEY"] = GOOGLE_GEMINI_API_KEY
    os.environ["OPENAI_API_KEY"] = config("OPENAI_API_KEY")
    COLLECTION_NAME = config("VECTOR_DB_COLLECTION")
    SUPABASE_PASSWORD = config("SUPABASE_PASSWORD")
    SUPABASE_CONN_STRING = config("SUPABASE_CONN_STRING")
    DIMENSION = 1536

    # LLamaindex based models
    model_name = "models/embedding-001"
    embed_model_gemini = GeminiEmbedding(model_name=model_name)
    gemini = LlamaGemini(model="models/gemini-1.5-flash")
    gpt_4o_mini_llama = LlamaIndexOpenAI(model="gpt-4o-mini")
    gpt_4o_llama = LlamaIndexOpenAI(model="gpt-4o")

    # Langchain base models
    embed_model_langchain = OpenAIEmbedding()
    gpt_41_langchain = LangchainOpenAI(
        model_name="gpt-4.1", max_tokens=500, streaming=False
    )
    gpt_41_mini_langchain = LangchainOpenAI(
        model_name="gpt-4.1-mini", max_tokens=500, streaming=False
    )
    gemini_langchain = ChatGoogleGenerativeAI(model="models/gemini-2.0-flash")

    ## Specify models to use
    ragllm = gpt_4o_llama
    embed_model = embed_model_langchain
    utilty_llm = gpt_41_mini_langchain
    chatllm = gpt_41_mini_langchain
    review_llm = gpt_4o_mini_llama

    # Global logger
    logger = get_logger()
    connection_string = SUPABASE_CONN_STRING.format(SUPABASE_PASSWORD=SUPABASE_PASSWORD)
    logger.info(f"Set to use collection {COLLECTION_NAME}")
    PROJECT_NAME = "SherlockAI"
    PHOENIX_SYSTEM_KEY = config("PHOENIX_SYSTEM_KEY")
