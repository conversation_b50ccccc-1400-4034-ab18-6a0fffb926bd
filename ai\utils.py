"""Author: <PERSON>
Created At:  2024-06-27
Description: This contains utility funtions for sherlock ai core
"""

import ast
import hashlib
import io
import json
import os
import time
from datetime import datetime, timedelta

import pandas as pd
import sqlparse
from langchain.agents import Tool
from PIL import Image

from ai.settings import SherlockSettings

# Get the current working directory
current_dir = os.getcwd()

# Create the path for the 'data' directory
base_dir = os.path.join(current_dir, "ai/metadata")


# Create the 'data' directory if it doesn't exist
os.makedirs(base_dir, exist_ok=True)


def save_graph_chart(graph):
    """Saves the graph structure to an image"""

    try:
        graph_path = os.path.join(base_dir, "chat_graph.png")
        img = graph.get_graph().draw_mermaid_png()
        img_obj = io.BytesIO(img)
        image = Image.open(img_obj)
        image.save(graph_path)
    except Exception as err:
        SherlockSettings.logger.error(f"Error saving Model Flowchart: {str(err)}")


def duration(func):
    """A decorator to calculate the time taken for execution"""

    def calc_time(*args, **kwargs):
        """calculate time taken for execution"""
        start = time.time()
        res = func(*args, **kwargs)
        stop = time.time()
        SherlockSettings.logger.info(f"Execution Time: {stop-start} seconds")
        return res

    return calc_time


def make_tool(func, name, desc):
    return Tool.from_function(func=func, name=name, description=desc)


def identify_sensitive_columns(sql_query, sensitive_columns):
    parsed_query = sqlparse.parse(sql_query)[0]
    identified_columns = []
    all_cols = []

    for token in parsed_query.tokens:
        if isinstance(token, sqlparse.sql.IdentifierList):
            # Handle multiple columns in SELECT clause
            for identifier in token.get_identifiers():
                qualified_name = identifier.get_name()  # Extract full column name
                alias = identifier.get_alias()  # Check if it has an alias
                if alias in sensitive_columns or qualified_name in sensitive_columns:
                    identified_columns.append(qualified_name or alias)
                all_cols.append(qualified_name)
        elif isinstance(token, sqlparse.sql.Identifier):
            # Handle a single column in SELECT clause
            qualified_name = token.get_real_name()  # This extracts the real column name
            alias = token.get_alias()  # Get the alias if present
            if qualified_name in sensitive_columns or alias in sensitive_columns:
                identified_columns.append(qualified_name or alias)
            all_cols.append(qualified_name)
    return identified_columns


def calculate_ttl_until_midnight():
    now = datetime.now()
    midnight = datetime.combine(now + timedelta(days=1), datetime.min.time())
    ttl = (midnight - now).seconds
    return ttl


# Hash the user query to generate a unique key
def generate_query_hash(user_query):
    user_query = user_query.lower().strip()
    return hashlib.sha256(user_query.encode()).hexdigest()


def generate_sql_hash(sql_query):
    query = sql_query.strip(";")
    return hashlib.sha256(query.encode()).hexdigest()


def format_data(all_df):
    df_metadata = []
    for i, df in enumerate(all_df):
        if isinstance(df, dict):
            df = pd.DataFrame.from_dict(df)
        elif isinstance(df, pd.Series):
            df = pd.DataFrame(df).T
        else:
            df = pd.DataFrame(df)
        metadata = {
            "index": i,
            "columns": df.columns.tolist(),
            "shape": df.shape,
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "sample": df.head(3).to_dict() if not df.empty else {},
        }
        df_metadata.append(metadata)
    return df_metadata


class UnsafeCodeError(Exception):
    def __init__(self, message=None):
        if message is None:
            message = "Access to system operators and attributes is not allowed"
        self.message = message
        super().__init__(self.message)


class UnsafeCodeVisitor(ast.NodeVisitor):
    def visit_Call(self, node):
        if isinstance(node.func, ast.Name) and node.func.id in {
            "eval",
            "exec",
            "compile",
            "open",
            "input",
            "os",
            "subprocess",
        }:
            raise UnsafeCodeError(f"Use of '{node.func.id}' is not allowed")
        self.generic_visit(node)

    def visit_Attribute(self, node):
        if isinstance(node.value, ast.Name) and node.value.id in {
            "os",
            "sys",
            "subprocess",
        }:
            raise UnsafeCodeError(
                f"Access to '{node.value.id}' attributes is not allowed"
            )
        self.generic_visit(node)


def validate_code(code: str):
    try:
        tree = ast.parse(code)
        UnsafeCodeVisitor().visit(tree)
        return code
    except Exception as e:
        if isinstance(e, UnsafeCodeError):
            raise e
        else:
            return code


def consolidate(con_llm, all_df_metadata, user_msg, sql_queries):
    consolidation_code = con_llm.invoke(
        {
            "user_msg": user_msg,
            "sql_query": sql_queries,
            "dfs_metadata": json.dumps(all_df_metadata, indent=2),
        }
    )
    validated_code = validate_code(clean_code_output(consolidation_code.content))

    local_namespace = {}
    exec(validated_code, {}, local_namespace)  # nosec
    return local_namespace["result"]


def clean_code_output(raw_code: str) -> str:
    if "```" in raw_code:
        # remove all triple backticks and optional language markers
        return "\n".join(
            line for line in raw_code.splitlines() if not line.strip().startswith("```")
        )
    return raw_code
