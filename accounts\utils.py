from datetime import timedelta

from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.utils import timezone
from django.utils.http import urlsafe_base64_decode

from .models import Notification, User, UserLockOut


def construct_query_param(key: str, text: str):
    output = f"?{key}="
    for char in text:
        if char == " ":
            output += "%20"
        else:
            output += char

    return output


def lockout_user(request, user_obj, lockout_duration):
    request_details = {
        "HTTP_HOST": request.META.get("HTTP_HOST"),
        "HTTP_REFERER": request.META.get("HTTP_REFERER"),
        "HTTP_USER_AGENT": request.META.get("HTTP_USER_AGENT"),
        "QUERY_STRING": request.META.get("QUERY_STRING"),
        "REMOTE_ADDR": request.META.get("REMOTE_ADDR"),
        "REMOTE_HOST": request.META.get("REMOTE_HOST"),
        "REQUEST_METHOD": request.META.get("REQUEST_METHOD"),
    }
    user_obj.locked_out_till = timezone.now() + timedelta(seconds=lockout_duration)
    user_obj.save(update_fields=["locked_out_till"])
    UserLockOut.objects.create(
        request_data=request_details, user=user_obj, lock_out_duration=lockout_duration
    )


def unlock_user(user_obj):
    last_lockout = (
        UserLockOut.objects.only("id", "is_unlocked")
        .filter(user=user_obj, is_unlocked=False)
        .last()
    )
    if last_lockout:
        last_lockout.is_unlocked = True
        last_lockout.save(update_fields=["is_unlocked"])
    if user_obj.is_locked_out:
        user_obj.locked_out_till = None
        user_obj.is_locked_out = False
        user_obj.save(update_fields=["locked_out_till", "is_locked_out"])


def generate_reset_password_token(user):
    token_generator = PasswordResetTokenGenerator()
    return token_generator.make_token(user)


def check_reset_password_token(user, token):
    """
    Check that a password reset token is correct for a given user.
    """
    token_generator = PasswordResetTokenGenerator()
    return token_generator.check_token(user, token)


def get_user_from_encoded_id(uidb64):
    uid = urlsafe_base64_decode(uidb64).decode(encoding="utf-8")
    user = User.objects.filter(pk=uid).first()
    return user


def create_notification(message, user, category):
    Notification.objects.create(message=message, user=user, category=category)
    return


def create_bulk_notification(message, users_id, category):
    Notification.objects.bulk_create(
        [
            Notification(
                user_id=id,
                category=category,
                message=message,
            )
            for id in set(users_id)
        ]
    )
