from django.core.management.base import BaseCommand

from accounts.models import User


class Command(BaseCommand):
    help = "Resets user password"

    def handle(self, *args, **kwargs):
        email = input("Email: ")
        user = User.objects.filter(email=email).first()
        if not user:
            print("! User not found")

        password = input("New Password: ")
        user.set_password(password)
        user.save()
        print("! Password reset successfully")
