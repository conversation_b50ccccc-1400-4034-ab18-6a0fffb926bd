import uuid

from django.contrib.auth import get_user_model
from django.db import models
from django.utils.functional import cached_property
from django_celery_beat.models import PeriodicTask

from accounts.models import UserManagedModel
from ai.models import UserPrompt
from base.choices import AccessLevel, Frequency, RecipientTypes, ShareOptions
from base.models import BaseModel
from dataset.models import Column, Table

User = get_user_model()


class Collection(BaseModel):
    identifier = models.UUIDField(default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, db_index=True)
    description = models.TextField(null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.DO_NOTHING)
    is_private = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} for {self.user.email}"

    @cached_property
    def get_charts(self):
        return self.charts.all()


class Report(UserManagedModel):
    name = models.CharField(max_length=200, db_index=True)
    # to be changed when folders are implemented
    filters = models.JSONField(blank=True, null=True)
    dynamic_columns = models.JSONField(blank=True, null=True)
    folder_name = models.CharField(max_length=200, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    table = models.ForeignKey(Table, on_delete=models.DO_NOTHING)
    is_private = models.BooleanField(default=True)
    notification = models.CharField(max_length=200, blank=True)

    def __str__(self):
        return self.name

    def get_report_columns_name(self):
        return self.report_columns.values_list("column__name", flat=True)

    async def has_edit_access(self, user):
        return await self.sharedreportuser_set.filter(
            user=user, access_level=AccessLevel.CAN_EDIT
        ).aexists()


class Chart(BaseModel):
    identifier = models.UUIDField(default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=100, default="", db_index=True)
    collection = models.ForeignKey(
        Collection,
        on_delete=models.CASCADE,
        related_name="charts",
        null=True,
        blank=True,
    )
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    data_config = models.JSONField(null=True, blank=True)
    is_private = models.BooleanField(default=True)
    prompt = models.ForeignKey(
        UserPrompt, on_delete=models.DO_NOTHING, null=True, blank=True
    )
    report = models.ForeignKey(
        Report,
        on_delete=models.CASCADE,
        related_name="report_charts",
        null=True,
        blank=True,
    )

    def __str__(self):
        return self.title


class ReportColumn(BaseModel):
    report = models.ForeignKey(
        Report, on_delete=models.CASCADE, null=True, related_name="report_columns"
    )
    column = models.ForeignKey(Column, on_delete=models.SET_NULL, null=True)

    @property
    def get_report_name(self):
        return self.report.name

    @property
    def get_column_name(self):
        return self.column.name

    def __str__(self):
        return f"Columns for {self.report.name} report"


class ShareSettings(BaseModel):
    report = models.ForeignKey(Report, on_delete=models.CASCADE)
    share_option = models.IntegerField(choices=ShareOptions.choices)

    @property
    def users(self):
        return self.reports.sharedreportuser_set.all()

    def __str__(self):
        return self.report.name


class SharedReportUser(BaseModel):
    report = models.ForeignKey(Report, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    access_level = models.IntegerField(choices=AccessLevel.choices)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=["report", "user"], name="user_report")
        ]


class ScheduleReport(UserManagedModel):
    report = models.ForeignKey(Report, on_delete=models.CASCADE)
    subject = models.CharField(max_length=250)
    frequency = models.CharField(max_length=100, choices=Frequency.choices)
    day_of_week = models.CharField(max_length=50, default="*")
    day_of_month = models.CharField(max_length=50, default="*")
    month_of_year = models.CharField(max_length=50, default="*")
    start_time = models.DateTimeField(null=True, blank=True)
    scheduled_datetime = models.DateTimeField(null=True, blank=True)
    minute = models.CharField(max_length=50, default="0")
    hour = models.CharField(max_length=50, default="*")
    task = models.OneToOneField(
        PeriodicTask, null=True, blank=True, on_delete=models.SET_NULL
    )

    @property
    def is_enabled(self):
        return self.task.enabled

    def __str__(self):
        return self.subject

    @property
    def all_recipients(self):
        return self.recipients.values(
            "recipient_type",
            "user__first_name",
            "user__last_name",
            "user__id",
            "user__email",
        )

    def _filter_recipients_by_type(self, recipient_type):
        return filter(
            lambda recipients: recipients["recipient_type"] == recipient_type,
            self.all_recipients,
        )

    @property
    def to(self):
        return self._filter_recipients_by_type(RecipientTypes.to)

    @property
    def bcc(self):
        return self._filter_recipients_by_type(RecipientTypes.bcc)

    @property
    def cc(self):
        return self._filter_recipients_by_type(RecipientTypes.cc)


class Recipient(BaseModel):
    schedule_report = models.ForeignKey(
        ScheduleReport, on_delete=models.CASCADE, related_name="recipients"
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    recipient_type = models.CharField(max_length=3, choices=RecipientTypes.choices)
