from typing import Any

from django import forms
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.forms import ReadOnlyPasswordHashField
from django.core.exceptions import ValidationError
from django.http.request import HttpRequest

from accounts.models import Role, User, UserLockOut


class UserCreationForm(forms.ModelForm):
    """A form for creating new users. Includes all the required
    fields, plus a repeated password."""

    password1 = forms.CharField(label="Password", widget=forms.PasswordInput)
    password2 = forms.CharField(
        label="Password confirmation", widget=forms.PasswordInput
    )

    class Meta:
        model = User
        fields = ["email"]

    def clean_password2(self):
        # Check that the two password entries match
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise ValidationError("Passwords don't match")
        return password2

    def save(self, commit=True):
        # Save the provided password in hashed format
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password1"])
        if commit:
            user.save()
        return user


class UserChangeForm(forms.ModelForm):
    """A form for updating users. Includes all the fields on
    the user, but replaces the password field with admin's
    disabled password hash display field.
    """

    password = ReadOnlyPasswordHashField()

    class Meta:
        model = User
        fields = ["email", "password", "is_active"]


class UserAdmin(BaseUserAdmin):
    form = UserChangeForm
    add_form = UserCreationForm

    ordering = ["email"]
    list_display = [
        "id",
        "identifier",
        "email",
        "first_name",
        "last_name",
        "is_staff",
        "is_superuser",
        "date_joined",
    ]
    fieldsets = (
        (None, {"fields": ("email", "password")}),
        ("Personal Info", {"fields": ("first_name", "last_name")}),
        (
            "Permissions",
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_admin",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                )
            },
        ),
        (
            "Two Factor Auth",
            {
                "fields": (
                    "is_two_factor_auth_enabled",
                    "has_set_up_two_factor_auth",
                    "is_post_login",
                )
            },
        ),
        ("Rate Limit", {"fields": ("is_locked_out", "locked_out_till")}),
        ("Dates", {"fields": ("last_login", "last_suspended_at")}),
    )
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "email",
                    "password1",
                    "password2",
                    "first_name",
                    "last_name",
                ),
            },
        ),
    )

    def get_fieldsets(
        self, request: HttpRequest, obj: Any | None = ...
    ) -> list[tuple[str | None, dict[str, Any]]]:
        return super().get_fieldsets(request, obj)


admin.site.register(User, UserAdmin)


@admin.register(UserLockOut)
class UserLockoutAdmin(admin.ModelAdmin):
    list_display = ("user", "is_unlocked", "lock_out_duration")


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ("id", "name")
