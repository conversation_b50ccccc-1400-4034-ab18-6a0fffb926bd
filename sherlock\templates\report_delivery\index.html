<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <link
      rel="stylesheet"
      href="./styles.css" />
    <style>
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.14 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.-top-0\.5 {
  top: -0.125rem;
}

.left-1\/2 {
  left: 50%;
}

.top-1\/3 {
  top: 33.333333%;
}

.z-\[2\] {
  z-index: 2;
}

.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.flex {
  display: flex;
}

.table {
  display: table;
}

.hidden {
  display: none;
}

.h-\[3\.5rem\] {
  height: 3.5rem;
}

.h-\[72dvh\] {
  height: 72dvh;
}

.h-full {
  height: 100%;
}

.max-h-24 {
  max-height: 6rem;
}

.w-full {
  width: 100%;
}

.border-collapse {
  border-collapse: collapse;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/3 {
  --tw-translate-y: -33.333333%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-pointer {
  cursor: pointer;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: 0.5rem;
}

.overflow-auto {
  overflow: auto;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.border {
  border-width: 1px;
}

.border-\[0\.5px\] {
  border-width: 0.5px;
}

.border-divider {
  --tw-border-opacity: 1;
  border-color: hsl(var(--twc-divider) / var(--twc-divider-opacity, var(--tw-border-opacity)));
}

.bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity));
}

.bg-red-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity));
}

.\!bg-red-800 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity)) !important;
}

.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-secondary) / var(--twc-secondary-opacity, var(--tw-bg-opacity)));
}

.\!bg-secondary {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--twc-secondary) / var(--twc-secondary-opacity, var(--tw-bg-opacity))) !important;
}

.p-3 {
  padding: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.font-normal {
  font-weight: 400;
}

.uppercase {
  text-transform: uppercase;
}

.text-pale-text {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-pale-text) / var(--twc-pale-text-opacity, var(--tw-text-opacity)));
}

.text-xds-eneutral-7 {
  --tw-text-opacity: 1;
  color: rgb(143 145 152 / var(--tw-text-opacity));
}

.light,[data-theme="light"] {
  --twc-primary: 0 0% 100%;
  --twc-primary-accent: 60 4.8% 95.9%;
  --twc-secondary: 20 5.9% 90%;
  --twc-divider: 214.29999999999995 31.8% 91.4%;
  --twc-sbase: 24.600000000000023 95% 53.1%;
  --twc-modal: 0 0% 100%;
  --twc-app-text: 0 0% 6.7%;
  --twc-badge-green: 131.3 66.7% 95.3%;
  --twc-badge-red: 0 100% 98%;
  --twc-badge-blue: 205 100% 95.3%;
  --twc-pale-text: 220 8.9% 46.5%;
}

@media (prefers-color-scheme: light) {
  :root {
    --twc-primary: 0 0% 100%;
    --twc-primary-accent: 60 4.8% 95.9%;
    --twc-secondary: 20 5.9% 90%;
    --twc-divider: 214.29999999999995 31.8% 91.4%;
    --twc-sbase: 24.600000000000023 95% 53.1%;
    --twc-modal: 0 0% 100%;
    --twc-app-text: 0 0% 6.7%;
    --twc-badge-green: 131.3 66.7% 95.3%;
    --twc-badge-red: 0 100% 98%;
    --twc-badge-blue: 205 100% 95.3%;
    --twc-pale-text: 220 8.9% 46.5%;
  }
}

.default,[data-theme="default"] {
  --twc-primary: 224 65.2% 4.5%;
  --twc-primary-accent: 220.89999999999998 37.9% 11.4%;
  --twc-secondary: 217.20000000000005 31.9% 17.8%;
  --twc-divider: 217.20000000000005 31.9% 17.8%;
  --twc-sbase: 24.600000000000023 95% 53.1%;
  --twc-modal: 224 65.2% 4.5%;
  --twc-app-text: 0 0% 100%;
  --twc-badge-green: 142.10000000000002 76.2% 36.3%;
  --twc-badge-green-opacity: 0.10;
  --twc-badge-red: 0 72.2% 50.6%;
  --twc-badge-red-opacity: 0.10;
  --twc-badge-blue: 221.20000000000005 83.2% 53.3%;
  --twc-badge-blue-opacity: 0.10;
  --twc-pale-text: 220 8.9% 46.5%;
}

.dark,[data-theme="dark"] {
  --twc-primary: 240 9.1% 4.3%;
  --twc-primary-accent: 240 5.7% 10.4%;
  --twc-secondary: 240 3.6% 16.3%;
  --twc-divider: 240 3.6% 16.3%;
  --twc-sbase: 24.600000000000023 95% 53.1%;
  --twc-modal: 240 9.1% 4.3%;
  --twc-app-text: 0 0% 100%;
  --twc-badge-green: 142.10000000000002 76.2% 36.3%;
  --twc-badge-green-opacity: 0.10;
  --twc-badge-red: 0 72.2% 50.6%;
  --twc-badge-red-opacity: 0.10;
  --twc-badge-blue: 221.20000000000005 83.2% 53.3%;
  --twc-badge-blue-opacity: 0.10;
  --twc-pale-text: 220 8.9% 46.5%;
}

[data-accent="cyan"][class$="sbase"] {
  background-color: #06B6D4;
  color: #06B6D4;
  stroke: #06B6D4;
  stop-color: #06B6D4;
}

[data-accent="orange"][class$="sbase"] {
  background-color: #F97316;
  color: #F97316;
  stroke: #F97316;
  stop-color: #F97316;
}

[data-accent="fuschia"][class$="sbase"] {
  background-color: #D946EF;
  color: #D946EF;
  stroke: #D946EF;
  stop-color: #D946EF;
}

[data-accent="rose"][class$="sbase"] {
  background-color: #F43F5E;
  color: #F43F5E;
  stroke: #F43F5E;
  stop-color: #F43F5E;
}

[data-accent="green"][class$="sbase"] {
  background-color: #22C55E;
  color: #22C55E;
  stroke: #22C55E;
  stop-color: #22C55E;
}

[data-accent="red"][class$="sbase"] {
  background-color: #EF4444;
  color: #EF4444;
  stroke: #EF4444;
  stop-color: #EF4444;
}

.last\:flex:last-child {
  display: flex;
}

.child\:child\:border-0 > * > * {
  border-width: 0px;
}

.child\:border-x-0 > * {
  border-left-width: 0px;
  border-right-width: 0px;
}

.child\:child\:bg-primary > * > * {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-primary) / var(--twc-primary-opacity, var(--tw-bg-opacity)));
}

.child\:first-letter\:uppercase > *::first-letter {
  text-transform: uppercase;
}
.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}


.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}


    </style>
    <title>Report</title>
  </head>
  <body class="light">
    <div>
      <h2 class="text-xl py-1 text-app-text">{{name}}</h2>
      <p class="text-sm text-pale-text">{{description}}</p>
    </div>
    <section class="relative pb-20 h-[72dvh]">
      <div class="relative h-full overflow-auto">
        <table class="overflow-auto w-full align-top">
          <thead
            class="sticky -top-0.5 z-[2] text-pale-text child:child:border-0">
            <tr>
              {% for key in keys %}
              <th
                class="text-sm text-left p-3 px-4 font-normal border border-collapse border-divider child:first-letter:uppercase gap-2 items-center cursor-pointer whitespace-nowrap uppercase bg-secondary"
                colspan="1"
                style="width: 150px">
                <div class="flex items-center gap-2">{{key|upper}}</div>
                {%endfor%}
              </th>

            </tr>
          </thead>
          <tbody>
            {% for item in result %}
            <tr class="text-pale-text child:border-x-0">
              {% for value in item.values %}
              <td
                class="px-4 h-[3.5rem] max-h-24 text-left text-sm align-middle border-[0.5px] border-collapse border-divider">
                <span class="line-clamp-2">{{value}}</span>
              </td>
              {% endfor %}
            </tr>
              {% endfor %}
          </tbody>
        </table>
      </div>
    </section>
  </body>
</html>
