import asyncio
from collections.abc import Callable
from functools import wraps

from asgiref.sync import sync_to_async
from ninja.errors import AuthenticationError

from accounts.exceptions import AuthorizationError


def has_permissions(perms: str | list[str]) -> Callable:
    """
    Expects a list of permissions

    raises exception:
    - if user is unauthenticated
    - if does not have any of permissions
    - Permissions is not a list or string

    # Usage

    ```
    @router.method("/", auth=AuthBearer())
                        ^^^^^^^^^^^^^^^^^ required
    @has_permissions([PERMISSIONS...])
    def view(request):
        ...
    ```
    """

    def _method_wrapper(func: Callable) -> Callable:
        @wraps(func)
        def _decorator(request, *args, **kwargs):
            if hasattr(request, "auth"):
                user = request.auth
            else:
                user = None

            if not user:
                raise AuthenticationError

            if not user.is_admin:
                permissions = perms

                if isinstance(permissions, str):
                    permissions = [perms]

                if not isinstance(permissions, list):
                    raise ValueError("Permissions must be of type list or str")

                if not user.has_perms(permissions):
                    raise AuthorizationError

            return func(request, *args, **kwargs)

        @wraps(func)
        async def _async_decorator(request, *args, **kwargs):
            if hasattr(request, "auth"):
                user = request.auth
            else:
                user = None

            if not user:
                raise AuthenticationError

            if not user.is_admin:
                permissions = perms

                if isinstance(permissions, str):
                    permissions = [perms]

                if not isinstance(permissions, list):
                    raise ValueError("Permissions must be of type list or str")

                if not await sync_to_async(user.has_perms)(permissions):
                    raise AuthorizationError

            return await func(request, *args, **kwargs)

        if asyncio.iscoroutinefunction(func):
            return _async_decorator

        return _decorator

    return _method_wrapper
