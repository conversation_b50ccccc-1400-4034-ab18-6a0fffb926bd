from base64 import b64decode, b64encode
from copy import deepcopy
from decimal import Decimal

from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad, unpad
from django.conf import settings


class AESCipher:
    """
    Usage:
        c = AESCipher('password').encrypt('message')
        m = AESCipher('password').decrypt(c)
    """

    def __init__(self, key, vector):
        self.key = bytes(key, "ascii")
        self.vector = bytes(vector, "ascii")

    def encrypt(self, raw):
        if raw == "" or raw is None:
            return raw
        raw = bytes(raw, "utf8")
        cipher = AES.new(self.key, AES.MODE_CBC, iv=self.vector)
        return b64encode(cipher.encrypt(pad(raw, AES.block_size))).decode("utf8")

    def decrypt(self, enc):
        if enc == "" or enc is None or enc == "null" or enc == "None":
            return enc
        text = b64decode(enc)
        cipher = AES.new(self.key, AES.MODE_CBC, self.vector)
        return unpad(cipher.decrypt(text), AES.block_size).decode("utf8")

    def encrypt_nested(self, obj_to_encrypt):
        if isinstance(obj_to_encrypt, dict):
            for k, v in obj_to_encrypt.items():
                if (
                    isinstance(v, str)
                    or isinstance(v, int)
                    or isinstance(v, Decimal)
                    or isinstance(v, float)
                ):
                    obj_to_encrypt[k] = self.encrypt(str(v))
                else:
                    obj_to_encrypt[k] = self.encrypt_nested(v)
        elif isinstance(obj_to_encrypt, list):
            for ind, v in enumerate(obj_to_encrypt):
                obj_to_encrypt[ind] = self.encrypt_nested(v)
        elif obj_to_encrypt is None:
            obj_to_encrypt = self.encrypt(obj_to_encrypt)
        else:
            obj_to_encrypt = self.encrypt(str(obj_to_encrypt))
        return obj_to_encrypt

    def decrypt_nested(self, obj_to_decrypt):
        if isinstance(obj_to_decrypt, dict):
            for k, v in obj_to_decrypt.items():
                if isinstance(v, str):
                    obj_to_decrypt[k] = self.decrypt(v)
                else:
                    obj_to_decrypt[k] = self.decrypt_nested(v)
        elif isinstance(obj_to_decrypt, list):
            for ind, v in enumerate(obj_to_decrypt):
                obj_to_decrypt[ind] = self.decrypt_nested(v)
        elif obj_to_decrypt is None:
            obj_to_decrypt = self.decrypt(obj_to_decrypt)
        else:
            obj_to_decrypt = self.decrypt(str(obj_to_decrypt))
            if obj_to_decrypt == "None":
                obj_to_decrypt = None
        return obj_to_decrypt

    # For use in websocket consumers

    def encrypt_nested_orig(self, ob):
        if isinstance(ob, dict):
            for k, v in ob.items():
                if (
                    isinstance(v, str)
                    or isinstance(v, int)
                    or isinstance(v, Decimal)
                    or isinstance(v, float)
                ):
                    ob[k] = self.encrypt(str(v))
                else:
                    self.encrypt_nested_orig(v)
        elif isinstance(ob, list):
            for ind, v in enumerate(ob):
                ob[ind] = self.encrypt_nested_orig(v)
        else:
            ob = self.encrypt(str(ob))
        return ob

    def decrypt_nested_orig(self, ob):
        if isinstance(ob, dict):
            for k, v in ob.items():
                if isinstance(v, str):
                    ob[k] = self.decrypt(v)
                else:
                    self.decrypt_nested_orig(v)
        elif isinstance(ob, list):
            for ind, v in enumerate(ob):
                ob[ind] = self.decrypt_nested_orig(v)
        else:
            ob = self.decrypt(str(ob))
        return ob

    def encrypt_body(self, body):
        try:
            body_copy = deepcopy(body)
            return self.encrypt_nested(body_copy)
        except Exception as e:
            print(e)
            return None

    def decrypt_body(self, body):
        try:
            body_copy = deepcopy(body)
            return self.decrypt_nested(body_copy)
        except Exception as e:
            print(e)
            return None


aes_cipher = AESCipher(settings.ENCRYPTION_KEY, settings.ENCRYPTION_VECTOR)


class AESGCMCipher:
    """
    this class is for encrypting using AES GCM mode

    """

    def __init__(self, key):
        self.key = bytes(key, "ascii")

    def encrypt(self, raw):
        cipher = AES.new(self.key, mode=AES.MODE_GCM)  # nonce is created by default
        ciphertext, tag = cipher.encrypt_and_digest(raw)
        result = [b64encode(x).decode("utf-8") for x in (ciphertext, tag, cipher.nonce)]

        return {"ciphertext": result[0], "tag": result[1], "nonce": result[2]}

    def decrypt(self, enc: dict):
        enc = {
            key: b64decode(value) for key, value in enc.items()
        }  # decoding encrypted response
        cipher = AES.new(self.key, AES.MODE_GCM, nonce=enc["nonce"])
        raw_password = cipher.decrypt_and_verify(enc["ciphertext"], enc["tag"])
        return raw_password.decode("utf-8")


aes_gcm_cipher = AESGCMCipher(settings.SECRET_KEY[:16])  # scecret key as encryption key
