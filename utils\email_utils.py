from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.utils.http import urlsafe_base64_encode

from accounts.utils import generate_reset_password_token
from base.constants import RESET_PASSWORD_SUBJECT, SIGN_UP_SUBJECT


class EmailSender:
    @classmethod
    def _send_email(
        cls,
        subject,
        mail_content,
        from_email=settings.EMAIL_HOST_USER,
        to=None,
        bcc=None,
        cc=None,
        file_path=None,
        file_name=None,
    ):
        msg = EmailMultiAlternatives(
            subject=subject,
            body=mail_content,
            from_email=from_email,
            to=to,
            bcc=bcc,
            cc=cc,
        )
        if file_path:
            with open(file_path, "rb") as f:
                data = f.read()
            msg.attach(file_path, content=data, mimetype="application/pdf")
        msg.attach_alternative(mail_content, "text/html")
        msg.send()

    @classmethod
    def reset_password_mail(cls, recipient):
        subject = RESET_PASSWORD_SUBJECT
        # user_id_base_64
        uidb64 = urlsafe_base64_encode(str(recipient.pk).encode("utf-8"))
        token = generate_reset_password_token(recipient)
        link = f"{settings.FRONTEND_SERVER_URL}/reset-password/{uidb64}/{token}"
        message = f"Click on the following link to reset your password\n{link}"
        return cls._send_email(subject, mail_content=message, to=[recipient.email])

    @classmethod
    def confirmation_mail(cls, recipient):
        subject = SIGN_UP_SUBJECT
        uidb64 = urlsafe_base64_encode(str(recipient.pk).encode("utf-8"))
        token = generate_reset_password_token(recipient)
        link = f"{settings.FRONTEND_SET_PASSWORD_URL}/{uidb64}/{token}"
        message = f"Click on the following link to set your password\n{link}"
        return cls._send_email(
            subject=subject, mail_content=message, to=[recipient.email]
        )

    @classmethod
    def report_delivery_mail(
        cls, subject, mail_body, to, bcc, cc, file_path=None, file_name=None
    ):
        return cls._send_email(
            subject=subject,
            mail_content=mail_body,
            to=to,
            bcc=bcc,
            cc=cc,
            file_path=file_path,
            file_name=file_name,
        )
