from psycopg2 import sql

ALL_COLUMNS = "*"
ALL_COLUMNS_LIST = [ALL_COLUMNS]


class SQL:
    @staticmethod
    def select(
        table_name,
        schema,
        limit,
        offset,
        columns,
        filters=None,
        derived_columns: list | None = None,
        order_by=None,
        group_by=None,
    ):
        # schema, table_name_ = transform_table_name(table_name)
        base_query_string = (
            "SELECT * FROM {schema_table}"
            if columns == ALL_COLUMNS_LIST
            else "SELECT {columns} FROM {schema_table}"
        )
        if derived_columns:
            base_query_string = (
                "SELECT *, {derived_columns} FROM {schema_table}"
                if columns == ALL_COLUMNS_LIST
                else "SELECT {columns}, {derived_columns} FROM {schema_table}"
            )
            # base_query_string = "SELECT {derived_columns} FROM {schema_table}" if len(columns) == 0 else base_query_string

        columns = sql.SQL(", ").join([sql.Identifier(n) for n in columns])

        base_query = sql.SQL(base_query_string).format(
            columns=columns if columns else None,
            derived_columns=derived_columns if derived_columns else None,
            schema_table=sql.Identifier(schema, table_name),
        )

        filtered_query = base_query + filters if filters else base_query
        limit_offset = (
            sql.SQL(" LIMIT {limit} OFFSET {offset}").format(
                limit=sql.Literal(limit), offset=sql.Literal(offset)
            )
            if limit
            else sql.SQL("")
        )
        limited_query = filtered_query + limit_offset
        order_by_query = limited_query + order_by if order_by else limited_query
        query = order_by_query + group_by if group_by else order_by_query

        return query

    @staticmethod
    def count(table_name, schema, column_name="*", alias=None, filters=None):
        # schema, table_name_ = transform_table_name(table_name)

        count_column = (
            sql.Literal(column_name)
            if column_name == ALL_COLUMNS
            else sql.Identifier(column_name)
        )

        base = sql.SQL("SELECT COUNT({column_name}) FROM {schema_table}").format(
            column_name=count_column, schema_table=sql.Identifier(schema, table_name)
        )
        base_query = (
            sql.SQL(
                "SELECT COUNT({column_name}) as {alias} FROM {schema_table}"
            ).format(
                column_name=count_column,
                alias=sql.Identifier(alias),
                schema_table=sql.Identifier(schema, table_name),
            )
            if alias
            else base
        )
        query = base_query + filters if filters else base_query

        return query


def transform_table_name(table_name: str):
    """
    Transform table
    Args:
            table_name (str): A dot-concatenated string of schema and table name i.e {schema}.{table_name}
    """
    return table_name.split(".")
