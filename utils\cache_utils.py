from django.core.cache import cache

"""
Common time periods as constants
"""
ONE_MINUTE = 60
ONE_HOUR = 60 * ONE_MINUTE
ONE_DAY = 24 * ONE_HOUR


def get_from_cache(cache_key, default=None):
    return cache.get(cache_key, default)


def add_to_cache(cache_key: str, value, timeout=None):
    return cache.add(cache_key, value, timeout=timeout)


def set_cache(cache_key: str, value, timeout=None):
    cache.set(cache_key, value, timeout=timeout)


def delete_cache(cache_key: str):
    return cache.delete(cache_key)
