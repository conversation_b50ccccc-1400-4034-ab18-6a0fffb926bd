from secrets import compare_digest
from uuid import UUID

import psycopg2
from django.db.models import Prefetch, Q
from ninja import Query, Router
from ninja.responses import codes_4xx

from accounts.auth import AuthBearer, async_auth_bearer
from accounts.helpers import validate_ids
from accounts.models import Role, User
from accounts.schemas import UserRoleSearchSchema, UserSearchRespSchema
from base.constants import get_updated_fields
from base.perm_constants import permissions
from dataset.helpers import (
    ConnectDB as connect_db,
)
from dataset.helpers import (
    get_columns_async,
    get_datasets_async,
    get_rules_async,
    get_tables_and_columns,
    get_tables_async,
    get_tags_async,
    get_warehouses_data,
)
from dataset.models import (
    Column,
    Dataset,
    DataSource,
    Rule,
    RuleFilter,
    RuleUser,
    Table,
    Tag,
)
from dataset.schemas import (
    ColumnSchema,
    ColumnUpdateSchema,
    DatasetCreateSchema,
    DatasetDetailSchema,
    DatasetListSchema,
    DatasetSchema,
    DatasetSearchSchema,
    DatasetUpdateSchema,
    DataSourceSchema,
    DataSourceSchemaResp,
    DataSourceUpdateSchema,
    EditTagSchema,
    RuleModelSchema,
    RuleRespSchema,
    RuleSchema,
    RuleUpdateSchema,
    TableDetailSchema,
    TableFilterSchema,
    TableListSchema,
    TableUpdateSchema,
    TagSchema,
    TagsListSchema,
    WarehouseSchema,
)
from self_service.utils import async_serialize_queryset, serialize_single_obj
from sherlock.permissions import has_permissions
from sherlock.schemas import Error, ResponseWithData, Success
from utils.aes import aes_gcm_cipher

router = Router(tags=["Dataset Management"], auth=async_auth_bearer)


@router.post("/data-source", response={200: DataSourceSchemaResp, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.connect])
async def connect_datasource(request, payload: DataSourceSchema):
    """endpoint to connect a datasource.
    returns datasource schemas
    """
    data = payload.dict()
    db_name = data.get("database_name")
    hostname = data.get("hostname")
    port = data.get("port")
    username = data.get("username")
    password = data.get("password")
    alias = data.get("alias")

    if not alias:
        alias = db_name  # default alias name to db_name if not set

    datasource = await DataSource.objects.acreate(
        name=db_name,
        address=hostname,
        port=port,
        username=username,
        password=aes_gcm_cipher.encrypt(password.encode()),
        alias=alias,
    )

    try:
        db_info = connect_db(db_name, hostname, username, password, port)
        schemas = db_info.get_schemas()
    except psycopg2.Error as e:
        return 400, {"error": str(e)}

    return 200, {"datasource_id": datasource.id, "schemas": schemas}


@router.patch("/data-source/{id}", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.connect])
async def edit_datasource(request, id: int, payload: DataSourceUpdateSchema):
    """endpoint to update datasource credentials"""
    datasource = await DataSource.active_objects.filter(id=id).afirst()
    if not datasource:
        return {"error": "Datasource does not exist"}
    payload = payload.dict(exclude_unset=True)
    field_names = get_updated_fields()
    if "new_password" and "old_password" in payload:
        old_password = payload.pop("old_password")
        datasource_password = aes_gcm_cipher.decrypt(datasource.password)
        # check if old password matches
        if not compare_digest(old_password, datasource_password):
            return 400, {"error": "Old password does not match"}

        new_password = payload.pop("new_password")
        datasource.password = aes_gcm_cipher.encrypt(new_password.encode())
        field_names.append("password")

    for field_name, value in payload.items():
        setattr(datasource, field_name, value)
        field_names.append(field_name)
    await datasource.asave(update_fields=field_names)
    return {"message": "Datasource updated successfully"}


# DATASETS AND COLUMNS
@router.post("/datasets", response={200: list[DatasetSchema], codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.connect])
def create_datasets(request, payload: DatasetCreateSchema):
    """endpoint for saving database schemas and tables.
    returns a list of database tables
    """
    db_schemas = payload.schemas

    datasource = DataSource.objects.filter(id=payload.datasource_id).first()
    if not datasource:
        return 400, {"error": "Datasource does not exist"}

    result, error = get_tables_and_columns(db_schemas, datasource)
    if error:
        return 400, {"error": str(error)}

    return result


@router.get("/datasets", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.connect])
async def get_datasets(request):
    """get selected db schemas for a datasource"""
    dataset = await get_datasets_async()
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=DatasetListSchema, qs=dataset),
    }


@router.patch(
    "/datasets/{identifier}", response={200: ResponseWithData, codes_4xx: Error}
)
@has_permissions([permissions.dataset_mgt.dataset.edit])
async def edit_dataset(request, identifier: UUID, payload: DatasetUpdateSchema):
    """edit dataset"""
    dataset = await Dataset.active_objects.filter(identifier=identifier).afirst()

    if not dataset:
        return 404, {"error": "Dataset not found"}

    dataset.name = payload.name
    await dataset.asave(update_fields=["name", "updated_at"])
    return {
        "message": "Dataset updated successfully",
        "data": serialize_single_obj(DatasetDetailSchema, qs=dataset),
    }


@router.delete("/datasets/{identifier}", response={200: Success, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.delete])
async def delete_dataset(request, identifier: UUID):
    dataset = await Dataset.active_objects.filter(identifier=identifier).afirst()
    if dataset is None:
        return 404, {"error": "Dataset not found"}

    await dataset.asoft_delete()
    return {"message": "Dataset deleted successfully"}


@router.get(
    "/datasets/{identifier}/tables", response={200: ResponseWithData, codes_4xx: Error}
)
@has_permissions([permissions.dataset_mgt.dataset.view])
async def get_dataset_tables(
    request, identifier: UUID, search: TableFilterSchema = Query(...)
):
    tables = await get_tables_async(
        filters={"dataset__identifier": identifier}, filter_query=search
    )
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=TableListSchema, qs=tables),
    }


@router.get("/tables", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.view])
async def get_all_tables(request, search: TableFilterSchema = Query(...)):
    tables = await get_tables_async(filter_query=search)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=TableListSchema, qs=tables),
    }


@router.get("/tables/{id}/", response={200: TableDetailSchema, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.view])
async def get_table_details(request, id: int):
    """this endpoint returns table name, description"""
    table = await Table.active_objects.filter(id=id).select_related("dataset").afirst()
    if not table:
        return 400, {"error": "Table does not exist"}
    return table


@router.patch(
    "/tables/{id}/update", response={200: TableDetailSchema, codes_4xx: Error}
)
@has_permissions([permissions.dataset_mgt.dataset.edit])
async def edit_table(request, id: int, payload: TableUpdateSchema):
    """edit table name, description or list of column"""
    table = await Table.active_objects.filter(id=id).select_related("dataset").afirst()
    if not table:
        return 400, {"error": "Table does not exist"}

    payload = payload.dict(exclude_unset=True)
    columns = payload.pop("columns", [])
    for col_dict in columns:
        col_id = col_dict.pop("id")
        col_obj = await table.columns.filter(id=col_id).afirst()
        if "tags" in col_dict:
            ids = col_dict.pop("tags")
            tags_id = await get_tags_async(id__in=ids)
            await col_obj.tags.aset(tags_id)

        field_names = get_updated_fields()
        for field_name, value in col_dict.items():
            setattr(col_obj, field_name, value)
            field_names.append(field_name)
        await col_obj.asave(update_fields=field_names)

    field_names = get_updated_fields()
    for field_name, value in payload.items():
        setattr(table, field_name, value)
        field_names.append(field_name)
    await table.asave(update_fields=field_names)
    return table


@router.delete("/tables/{id}/delete", response={200: Success, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.delete])
async def delete_table(request, id: int):
    """delete table endpoint"""
    table = await Table.active_objects.filter(id=id).afirst()
    if not table:
        return 400, {"error": "Table does not exist"}

    await table.asoft_delete()
    return {"message": "Table deleted successfully"}


@router.get(
    "/tables/{table_id}/columns", response={200: ResponseWithData, codes_4xx: Error}
)
@has_permissions([permissions.dataset_mgt.dataset.view])
async def get_columns(request, table_id: int):
    """get list of all columns in a table.
    this endpoint is for access config
    """
    table = await Table.active_objects.only("id").filter(id=table_id).afirst()
    if not table:
        return 400, {"error": "Table does not exist"}
        # get all columns in a table order by name filter by table_id, exclude PK (id)
    columns = await get_columns_async(
        order_by_fields=["name"], filters={"table__id": table_id}
    )
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=ColumnSchema, qs=columns),
    }


@router.patch(
    "/tables/{table_id}/columns/{id}", response={200: ColumnSchema, codes_4xx: Error}
)
@has_permissions([permissions.dataset_mgt.dataset.edit])
async def update_column(request, id: int, table_id: int, payload: ColumnUpdateSchema):
    """update table column"""
    column = await Column.active_objects.filter(table_id=table_id, id=id).afirst()
    if not column:
        return 400, {"error": "Column does not exist"}

    payload = payload.dict(exclude_unset=True)
    if "tags" in payload:
        ids = payload.pop("tags")
        tags_id = await get_tags_async(id__in=ids)
        await column.tags.aset(tags_id)

    field_names = get_updated_fields()

    for field_name, value in payload.items():
        setattr(column, field_name, value)
        field_names.append(field_name)
    await column.asave(update_fields=field_names)
    return column


@router.delete("/columns/{id}/delete", response={200: Success, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.delete])
async def delete_column(request, id: int):
    """delete column endpoint"""
    if not await Column.active_objects.filter(id=id).aexists():
        return 400, {"error": "Column does not exist"}

    await Column.active_objects.filter(id=id).asoft_delete()
    return {"message": "Column deleted successfully"}


# TAGS
@router.post("/tags", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.connect])
async def create_tag(request, payload: TagSchema):
    tag = await Tag.objects.acreate(**payload.dict(), created_by=request.auth)
    return {
        "message": "Tag created successfully",
        "data": serialize_single_obj(schema=TagSchema, qs=tag),
    }


@router.get("/tags", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.connect])
async def get_all_tags(request):
    tags = await get_tags_async()
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=TagsListSchema, qs=tags),
    }


@router.patch("/tags/{id}")
@has_permissions([permissions.dataset_mgt.dataset.edit])
async def edit_tag(request, id: int, payload: EditTagSchema):
    tag = await Tag.active_objects.filter(id=id).afirst()
    if not tag:
        return 400, {"error": "Tag does not exist"}

    payload = payload.dict(exclude_unset=True)
    field_names = get_updated_fields()
    for field_name, value in payload.items():
        setattr(tag, field_name, value)
        field_names.append(field_name)
    tag.last_updated_by = request.auth
    field_names.append("last_updated_by")
    await tag.asave(update_fields=field_names)
    return {
        "message": "Tag updated successfully",
        "data": serialize_single_obj(schema=TagSchema, qs=tag),
    }


@router.delete("/tags/{id}/delete", response={200: Success, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.dataset.delete])
async def delete_tag(request, id: int):
    """delete tags"""
    if not await Tag.active_objects.filter(id=id).aexists():
        return 400, {"error": "Tag does not exist"}

    await Tag.active_objects.filter(id=id).asoft_delete()
    return {"message": "Tag deleted successfully."}


# RULES
@router.post(
    "/rules", response={200: ResponseWithData, codes_4xx: Error}, auth=AuthBearer()
)
@has_permissions([permissions.dataset_mgt.rules.create])
def create_rules(request, payload: RuleSchema):
    """Create Access config for tables"""
    table_query = Table.active_objects.filter(id=payload.table_id)
    if not table_query.exists():
        return 400, {"error": "Table does not exist"}

    payload = payload.dict()
    # check if rule with everyone already exists
    if (
        table_query.first().has_everyone_applied_rule()
        and payload["is_applied_to_everyone"]
    ):
        return 400, {"error": "Rule applied to everyone already exists on this table"}

    group = payload.pop("group", [])
    user_ids = payload.pop("assigned_users")

    assignee_ids = User.active_objects.filter(id__in=user_ids).values_list(
        "id", flat=True
    )
    roles_ids = validate_ids(Role, payload.pop("assigned_roles"))

    # check if columns dict is empty
    if len(payload.get("columns")) == 0:
        return 400, {"error": "Rule has no column"}

    rule = Rule.objects.create(**payload, created_by=request.auth)
    # create rule groups
    data = [
        RuleFilter(
            filter=condition.get("filter"),
            connector=condition.get("connector"),
            rule=rule,
        )
        for condition in group
    ]
    RuleFilter.objects.bulk_create(data)

    # associate users to assigned rule
    assigned_users = [RuleUser(rule=rule, user_id=id) for id in assignee_ids]
    RuleUser.objects.bulk_create(assigned_users)

    # add assigned roles to rule
    rule.roles.add(*roles_ids)

    return {
        "message": "Rules created successfully",
        "data": {"id": rule.id, "name": rule.name},
    }


@router.get("tables/{table_id}/rules", response={200: ResponseWithData})
@has_permissions([permissions.dataset_mgt.rules.view])
async def get_all_table_rules(request, table_id: int):
    """get rules for a table using the table_id"""
    rules = await get_rules_async(table_id=table_id)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=RuleModelSchema, qs=rules),
    }


@router.get(
    "rules/{id}",
    response={200: RuleRespSchema, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions([permissions.dataset_mgt.rules.view])
def get_rule(request, id: int):
    """get a single rule in a table"""
    rules = (
        Rule.active_objects.filter(id=id)
        .prefetch_related(
            Prefetch(
                "rule_users", queryset=RuleUser.active_objects.select_related("user")
            ),
            Prefetch("rule_filters"),
            Prefetch("roles", queryset=Role.active_objects.only("id", "name")),
        )
        .first()
    )
    if not rules:
        return 400, {"error": "Rule does not exist"}

    group = list(rules.filters)

    users_detail = [
        {"first_name": row[0], "last_name": row[1], "id": row[2]}
        for row in rules.users_names
    ]

    # users = [rule.user.get_full_name() for rule in rule.users]
    return {"rule": rules, "group": group, "assigned_to": users_detail}


@router.patch(
    "/rules/{id}/update",
    response={200: RuleModelSchema, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions([permissions.dataset_mgt.rules.edit])
def edit_rule(request, id: int, payload: RuleUpdateSchema):
    """edit rule"""
    rule = Rule.active_objects.filter(id=id).first()
    if not rule:
        return 400, {"error": "Rule does not exist"}

    payload = payload.dict()
    if "group" in payload:
        group = payload.pop("group")

        data = RuleFilter.objects.bulk_create(
            [
                RuleFilter(
                    filter=condition.get("filter"),
                    connector=condition.get("connector"),
                )
                for condition in group
            ]
        )  # create new ones

        rule.rule_filters.set(data)

    if "assigned_users" in payload:
        user_ids = payload.pop("assigned_users")
        assignee_ids = User.active_objects.filter(id__in=user_ids).values_list(
            "id", flat=True
        )
        assigned_users = RuleUser.objects.bulk_create(
            [RuleUser(user_id=id) for id in assignee_ids]
        )
        rule.rule_users.set(assigned_users)

    # assign roles
    roles_ids = validate_ids(Role, payload.pop("assigned_roles"))
    rule.roles.set(roles_ids)

    field_names = get_updated_fields()
    for field_name, value in payload.items():
        setattr(rule, field_name, value)
        field_names.append(field_name)

    rule.last_updated_by = request.auth
    field_names.append("last_updated_by")
    rule.save(update_fields=field_names)

    return rule


@router.delete("/rules/{id}/delete", response={200: Success, codes_4xx: Error})
@has_permissions([permissions.dataset_mgt.rules.delete])
async def delete_rule(request, id: int):
    """delete rules"""
    if not await Rule.active_objects.filter(id=id, created_by=request.auth).aexists():
        return 400, {"error": "Rule does not exist"}

    await Rule.active_objects.filter(id=id).asoft_delete()
    return {"message": "Rule deleted successfully."}


@router.get(
    "/users/search", response={200: list[UserSearchRespSchema]}, auth=AuthBearer()
)
def search_users(request, query: str):
    return User.active_objects.filter(
        Q(first_name__icontains=query) | Q(last_name__icontains=query)
    )


@router.get("/users_roles/search", response={200: UserRoleSearchSchema})
def search_users_and_roles(request, query: str):
    """return users and roles that matches the keyword"""
    users = User.active_objects.only("id", "first_name", "last_name").filter(
        Q(first_name__icontains=query) | Q(last_name__icontains=query)
    )
    roles = Role.active_objects.only("id", "name").filter(name__icontains=query)

    return {"users": users, "roles": roles}


@router.get(
    "/tables/search", response={200: list[DatasetSearchSchema], codes_4xx: Error}
)
def search_tables(request, query: str):
    """search tables with name or alias or meta data or column_name in table"""
    search = Table.active_objects.filter(
        Q(name__icontains=query)
        | Q(alias__icontains=query)
        | Q(columns__name__icontains=query)
        | Q(description__icontains=query)
    ).distinct()
    return search


# warehouse api
@router.get("/warehouses", response={200: WarehouseSchema, codes_4xx: Error}, auth=None)
def get_warehouses(request):
    """endpoint to get warehouse info"""
    result, error = get_warehouses_data()
    if error:
        return 400, {"error": str(error)}
    return {"result": result}
