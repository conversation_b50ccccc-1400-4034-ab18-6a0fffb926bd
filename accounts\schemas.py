from datetime import datetime

from ninja import FilterSchema as Filter
from ninja import ModelSchema, Schema
from pydantic import EmailStr, Field

from accounts.models import Notification, Role, User


class UserSchema(Schema):
    email: str
    password: str


class TokenSchema(Schema):
    access: str
    refresh: str | None = None


class TwoFactorSetUpSchema(Schema):
    email: str
    set_up_2fa: str | None = None
    two_factor_enabled: bool


class NotificationSchema(ModelSchema):
    class Meta:
        model = Notification
        exclude = ["user"]


class UserCreateSchema(ModelSchema):
    roles: list[int] = []
    user_permissions: list[int] = []

    class Meta:
        model = User
        fields = [
            "email",
            "first_name",
            "last_name",
            "designation",
            "phone_number",
            "user_permissions",
        ]


class UserUpdateSchema(Schema):
    first_name: str
    email: str
    last_name: str
    phone_number: str | None = None
    roles: list[int] = []
    user_permissions: list[int] = []


class MakeUserAdminSchema(Schema):
    make_admin: bool


class UserListSchema(Schema):
    id: int
    full_name: str | None = None
    email: str
    is_active: bool
    photo: str | None = None
    roles: list[str] = []
    permissions: list[str] = []
    phone_number: str | None = None
    last_login: datetime | None = None
    date_joined: datetime | None = None
    is_two_factor_auth_enabled: bool
    is_admin: bool
    is_afex_staff: bool

    @staticmethod
    def resolve_full_name(obj):
        return obj.get_full_name().title()

    @staticmethod
    def resolve_permissions(obj):
        if obj.is_superuser:
            return ["sys_admin"]

        return obj.get_all_permissions()

    @staticmethod
    def resolve_roles(obj):
        return obj.groups.values_list("name", flat=True)


class UserDisplaySchema(Schema):
    id: int
    email: str
    is_admin: bool
    is_active: bool
    first_name: str | None = None
    last_name: str | None = None
    username: str | None = None
    designation: str | None = None
    photo: str | None = None
    phone_number: str | None = None
    permissions: list[str]
    roles: list[str] = []
    is_afex_staff: bool
    last_suspended_at: datetime | None = None

    @staticmethod
    def resolve_username(obj):
        if not obj.email:
            return None
        return obj.email.split("@")[0]

    @staticmethod
    def resolve_permissions(obj):
        if obj.is_superuser:
            return ["sys_admin"]
        return obj.get_all_permissions()

    @staticmethod
    def resolve_roles(obj):
        return obj.groups.values_list("name", flat=True)


class UserFilterSchema(Filter):
    search: str = Field(
        None, q=["email__icontains", "first_name__icontains", "last_name__icontains"]
    )


class UserSearchRespSchema(Schema):
    id: int
    first_name: str | None = None
    last_name: str | None = None


class RoleSearchSchema(Schema):
    id: int
    name: str


class UserRoleSearchSchema(Schema):
    users: list[UserSearchRespSchema]
    roles: list[RoleSearchSchema]


class RoleUserSchema(Schema):
    id: int
    first_name: str | None = None
    last_name: str | None = None
    email: str
    last_login: datetime | None = None
    created_at: datetime | None = None


class RoleSchema(ModelSchema):
    get_created_by: str
    users: list[RoleUserSchema] | None = None
    permissions: list[str] | None = None

    class Meta:
        model = Role
        fields = "__all__"

    @staticmethod
    def resolve_users(obj):
        return obj.user_set.all()

    @staticmethod
    def resolve_permissions(obj):
        return obj.permissions.values_list("name", flat=True)


class RoleCreateSchema(Schema):
    name: str
    permissions: list[int]
    user_ids: list[int] = []


class RoleUpdateSchema(Schema):
    name: str | None = None
    permissions: list[int] = []
    user_ids: list[int] | None = None


class RoleDisplaySchema(Schema):
    id: int
    name: str
    permissions: list[str] | None = None

    @staticmethod
    def resolve_permissions(obj):
        return obj.permissions.values_list("name", flat=True)


class RoleListSchema(Schema):
    id: int
    name: str


class PermissionListSchema(Schema):
    id: int
    code_name: str

    @staticmethod
    def resolve_code_name(obj):
        return f"{obj.content_type.app_label}.{obj.codename}"


class ValidateEmailSchema(Schema):
    email: EmailStr


class SetPasswordSchema(Schema):
    password: str
    repeat_password: str


class VerifyOTPSchema(Schema):
    email: EmailStr
    otp: str


class ValidLinkRespSchema(Schema):
    password_requirement_text: str
