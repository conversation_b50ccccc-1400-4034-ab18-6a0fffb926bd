from django.contrib import admin

from .models import (
    Chart,
    Collection,
    Report,
    ReportColumn,
    ScheduleReport,
    SharedReportUser,
    ShareSettings,
)

# Register your models here.


@admin.register(Chart)
class ChartAdmin(admin.ModelAdmin):
    list_display = (
        "identifier",
        "title",
        "user",
        "is_private",
        "created_at",
        "updated_at",
    )


@admin.register(Collection)
class CollectionAdmin(admin.ModelAdmin):
    list_display = ("name", "user", "created_at", "updated_at")


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "table", "created_by")


@admin.register(ReportColumn)
class ReportColumnAdmin(admin.ModelAdmin):
    list_display = ("id", "get_report_name", "get_column_name")


@admin.register(ShareSettings)
class ShareSettingsAdmin(admin.ModelAdmin):
    list_display = ("report", "share_option")


@admin.register(SharedReportUser)
class SharedReportUserAdmin(admin.ModelAdmin):
    list_display = ("report", "user", "access_level")


@admin.register(ScheduleReport)
class ScheduleReportUserAdmin(admin.ModelAdmin):
    list_display = ("id", "report", "subject", "frequency", "task")
