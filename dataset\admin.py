from django.contrib import admin

from .models import Column, Dataset, DataSource, Rule, RuleFilter, RuleUser, Table, Tag


# Register your models here.
@admin.register(DataSource)
class DataSourceAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "alias",
    )


@admin.register(Dataset)
class DatasetAdmin(admin.ModelAdmin):
    list_display = ("identifier", "name", "datasource_id")


@admin.register(Table)
class TableAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "dataset")


@admin.register(Column)
class ColumnAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "data_type", "table")


@admin.register(Rule)
class RuleAdmin(admin.ModelAdmin):
    list_display = ("id", "name")


@admin.register(RuleFilter)
class RuleFilterAdmin(admin.ModelAdmin):
    list_display = ("id", "filter", "connector", "rule")


@admin.register(RuleUser)
class RuleUserAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "rule",
        "user",
    )


@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "alias", "created_by", "last_updated_by")
