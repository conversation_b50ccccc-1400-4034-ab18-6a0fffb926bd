import random
import string

from django.core.serializers.json import DjangoJSONEncoder
from django.db import models

from accounts.models import User
from base.models import BaseModel


class Conversation(BaseModel):
    id = models.SlugField(unique=True, editable=False, primary_key=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.DO_NOTHING)

    def __str__(self):
        return f"({self.id}){self.name} "

    def save(self, *args, **kwargs):
        while not self.id:
            id = self.generate_slug()

            if not Conversation.objects.filter(id=id).exists():
                self.id = id

        super().save(*args, **kwargs)

    def generate_slug(self):
        return "".join(
            random.sample(string.ascii_letters, 2)
            + random.sample(string.ascii_letters, 2)
            + random.sample(string.digits, 2)
            + random.sample(string.digits, 2)
            + random.sample(string.ascii_letters, 2)
            + random.sample(string.ascii_letters, 2)
        )


class UserPrompt(BaseModel):
    class RatingChoices(models.TextChoices):
        INACCURATE = 1
        WRONG_CONTEXT = 2
        INCOMPLETE = 3
        GOOD = 4
        EXCELLENT = 5

    sql = models.TextField(null=True, blank=True)
    data = models.JSONField(null=True, blank=True, encoder=DjangoJSONEncoder)
    prompt = models.TextField()
    conversation = models.ForeignKey(
        Conversation, on_delete=models.CASCADE, related_name="prompts"
    )
    model_version = models.CharField(max_length=255, null=True, blank=True)
    # comment = models.TextField(null=True, blank=True)
    comment = models.TextField(default="")
    # User feedback
    rating = models.IntegerField(choices=RatingChoices.choices, null=True, blank=True)
    user_comment = models.TextField(null=True, blank=True)
    fetched_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.prompt

    @property
    def user(self):
        return self.conversation.user

    @property
    def conversation_name(self):
        return self.conversation.name


class PromptLog(BaseModel):
    prompt = models.OneToOneField(UserPrompt, on_delete=models.DO_NOTHING)
    tokens = models.IntegerField()
    cost = models.FloatField()


class AlertTypes(models.TextChoices):
    PERIODIC = "PERIODIC"
    CONDITIONAL = "CONDITIONAL"


class Alert(BaseModel):
    """
    - Celery Cronjob that runs every 5 minutes will check for active (is_active) alerts
    """

    user_prompt = models.OneToOneField(
        UserPrompt, on_delete=models.CASCADE, related_name="alert"
    )
    user = models.ForeignKey(User, on_delete=models.DO_NOTHING)
    alert_type = models.CharField(max_length=20, choices=AlertTypes.choices)
    description = models.TextField(null=True, blank=True)

    # count.0 > 50000 (conditional)
    #     data["count"][0] > 50000

    # weeks=0 days=0 hours=0 (periodic)
    #     Cronjob will run every hour and send alert if time specified has passed since "last_triggered"
    config = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    hour = models.PositiveSmallIntegerField(null=True, blank=True)
    day = models.PositiveSmallIntegerField(null=True, blank=True)
    last_triggered = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.alert_type.capitalize()} alert for {self.user.email}"

    def save(self, *args, **kwargs) -> str | None:
        """
        - sets last_triggered to created_at if not set already
        - updates last_triggered hour and day with self.hour and self.day

        E.g alert every day by 6pm
        Alert {
            config = "days=1"
            day = 1
            hour = 18
        }

        NOTE: This has not been exhaustively tested use with care
        """
        if self.alert_type != AlertTypes.PERIODIC:
            return super().save(*args, **kwargs)

        if not self.last_triggered:
            self.last_triggered = self.created_at

        try:
            if self.hour:
                self.last_triggered = self.last_triggered.replace(
                    hour=self.hour, minute=0
                )

            if self.day:
                self.last_triggered = self.last_triggered.replace(day=self.day)
        except Exception as exc:
            return str(exc)

        return super().save(*args, **kwargs)


class Feedback(BaseModel):
    user = models.ForeignKey(User, on_delete=models.DO_NOTHING)
    text = models.TextField()
