import psycopg2
from asgiref.sync import sync_to_async
from django.db.models import F, Q

from accounts.helpers import validate_ids
from accounts.models import User
from accounts.utils import create_bulk_notification
from base.choices import AccessLevel, Category, ShareOptions
from base.constants import CAN_EDIT
from base.messages import ResponseMessages
from dataset.helpers import ConnectDB, get_report_filters
from dataset.models import DataSource
from utils.aes import aes_gcm_cipher

from .models import (
    Chart,
    Collection,
    Report,
    ReportColumn,
    SharedReportUser,
    ShareSettings,
)


def get_collections(**kwargs):
    return Collection.active_objects.filter(**kwargs)


@sync_to_async
def get_collections_async(**kwargs):
    return get_collections(**kwargs)


def get_charts(**kwargs):
    return Chart.active_objects.filter(**kwargs)


@sync_to_async
def get_charts_async(**kwargs):
    return get_charts(**kwargs)


def get_all_reports(user, **kwargs):
    """return public reports or user created report or user shared report"""
    return Report.active_objects.filter(
        Q(**kwargs) | Q(created_by=user) | Q(sharedreportuser__user=user)
    ).annotate(access_level=F("sharedreportuser__access_level"))


def get_reports(user, **kwargs):
    return Report.active_objects.filter(**kwargs, created_by=user)


def get_report_columns(**kwargs):
    return ReportColumn.active_objects.filter(**kwargs)


def get_user_or_public_reports(**kwargs):
    return Report.active_objects.filter(**kwargs)


def get_report_details(report, user, limit, offset):
    result = {}
    error = None
    connect_db = ConnectDB
    table = report.table
    schema_name = table.schema_name

    datasource = DataSource.active_objects.filter(dataset__name=schema_name).first()

    try:
        db_info = connect_db(
            db_name=datasource.name,
            hostname=datasource.address,
            username=datasource.username,
            password=aes_gcm_cipher.decrypt(datasource.password),
            port=datasource.port,
        )

        columns_list = report.get_report_columns_name()

        filters = report.filters
        dynamic_columns = report.dynamic_columns
        report_filters = get_report_filters(
            table_id=table.id, user=user, filters=filters
        )

        record_count_query = db_info.construct_count_query(
            table_name=table.name,
            schema=schema_name,
            alias="total_records",
            filters=report_filters,
        )
        record_count = db_info.execute_query(record_count_query)

        result_query = db_info.construct_query(
            table_name=table.name,
            schema=schema_name,
            limit=limit,
            offset=offset,
            column_list=columns_list,
            filters=report_filters,
            dynamic_columns=dynamic_columns,
        )
        response = db_info.execute_query(result_query)
        keys = list(response[0].keys())
        result = {
            "total_records": record_count,
            "result": response,
            "name": report.name,
            "description": report.description,
            "keys": keys,
        }
    except psycopg2.Error as e:
        err = e.get("error").split("\n")[0]
        error = {"error": str(err)}
    finally:
        db_info.disconnect()

    return result, error


def create_shared_report(report, share_option, users_details):
    ShareSettings.objects.get_or_create(report=report, share_option=share_option)

    notify_user_list = []
    if share_option == ShareOptions.EVERYONE:
        # create a new objects only when they don't exists
        # user_details: [user_ids]
        create_list = [
            SharedReportUser(
                report=report, user_id=id, access_level=AccessLevel.CAN_VIEW
            )
            for id in users_details
            if not SharedReportUser.active_objects.filter(
                report=report, user_id=id
            ).exists()
        ]
        notify_user_list.extend(users_details)
    else:
        create_list = []
        update_list = []

        for detail in users_details:
            # user_details: [dicts]
            # check if obj already exist, update access_level if it does
            if SharedReportUser.active_objects.filter(
                report=report, user_id=detail["user_id"]
            ).exists():
                shared_report = SharedReportUser.active_objects.get(
                    report=report, user_id=detail["user_id"]
                )
                shared_report.access_level = (
                    AccessLevel.CAN_EDIT
                    if detail["access_level"] == CAN_EDIT
                    else AccessLevel.CAN_VIEW
                )
                update_list.append(shared_report)
            else:
                # create new obj if obj doesn't exist
                create_list.append(
                    SharedReportUser(
                        report=report,
                        user_id=detail["user_id"],
                        access_level=AccessLevel.CAN_EDIT
                        if detail["access_level"] == CAN_EDIT
                        else AccessLevel.CAN_VIEW,
                    )
                )
        user_ids = [
            x["user_id"] for x in users_details
        ]  # get user_id from user_details
        notify_user_list.extend(user_ids)
        SharedReportUser.objects.bulk_update(update_list, ["access_level"])

    SharedReportUser.objects.bulk_create(create_list)
    # create bulk notification
    create_bulk_notification(
        message=ResponseMessages.SHARED_REPORT_MSG
        % (report.name, report.created_by.get_full_name()),
        users_id=notify_user_list,
        category=Category.shared_report,
    )


def validate_recipients(payload):
    recipient_types = ["to", "bcc", "cc"]
    validated_recipients = {
        recipient: list(validate_ids(User, getattr(payload, recipient, [])))
        for recipient in recipient_types
    }
    payload = payload.dict(exclude_unset=True)
    payload["to"] = validated_recipients["to"]
    payload["bcc"] = validated_recipients["bcc"]
    payload["cc"] = validated_recipients["cc"]

    return payload


def get_recipients_email(recipient_type):
    return [detail["user__email"] for detail in recipient_type]


get_user_or_public_reports_async = sync_to_async(get_user_or_public_reports)
get_reports_async = sync_to_async(get_reports)
get_report_columns_async = sync_to_async(get_report_columns)
get_all_reports_async = sync_to_async(get_all_reports)
