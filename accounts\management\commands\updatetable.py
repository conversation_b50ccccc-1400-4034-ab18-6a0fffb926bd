from django.core.management.base import BaseCommand

from dataset.models import Table


class Command(BaseCommand):
    help = "Update table name"

    def handle(self, *args, **kwargs):
        # get all tables
        tables = Table.objects.all()
        tables_obj = []
        for table in tables:
            if "." in table.name:
                table.name = table.name.split(".")[1]
                tables_obj.append(table)

        # Bulk update
        Table.objects.bulk_update(tables_obj, ["name"])
        self.stdout.write("table name updated successfully")
