from typing import Any

from django.conf import settings
from django.db.models.query import QuerySet
from ninja import Field, Schema
from ninja.errors import HttpError
from ninja.pagination import PaginationBase

from sherlock.exceptions import PaginationError


class Pagination(PaginationBase):
    """
    Paginates successful responses

    - expects a queryset
    else:
        - checks if queryset is a tuple
        - assumes tuple contains (status_code, {"error": error_message, ...})
        - raises PaginationError with status_code and error_message
            - This is because the paginate decorator expects a success response from <PERSON><PERSON><PERSON>
              so the only was to return an error is to raise an exception
              https://github.com/vitalik/django-ninja/issues/502

    returns: Output(Schema)
    """

    # items_per_page = settings.PAGINATION_ITEMS_PER_PAGE

    class Input(Schema):
        page: int = Field(1, ge=1)
        page_size: int = settings.PAGINATION_ITEMS_PER_PAGE

    class Output(Schema):
        items: list[Any]
        total: int
        items_per_page: int
        next: int | None

    def paginate_queryset(self, queryset, pagination: Input, **params):
        # Only paginate success responses
        try:
            if isinstance(queryset, tuple):
                # queryset (status_code: int, error: dict)
                error = queryset[1]
                if isinstance(error, dict):
                    raise PaginationError(queryset[0], error.get("error", None))

                print("! Pagination error", queryset)
                raise HttpError(500, "Something went wrong")

            if not isinstance(queryset, QuerySet):
                raise HttpError(
                    500, "Pagination object not of type QuerySet %s" % type(queryset)
                )

            page = pagination.page
            page_size = pagination.page_size
            if page < 1:
                raise PaginationError(500, "Page number must be greater than 1")

            # offset = (page - 1) * self.page_size
            start = (page - 1) * page_size
            count = queryset.count()
            next = None

            if start + page_size < count:
                next = page + 1

            return {
                "items": queryset[start : start + page_size],
                "total": count,
                "items_per_page": page_size,
                "next": next,
            }
        except (PaginationError, HttpError):
            # return a null schema in case of pagination error (empty response)
            return {
                "items": [],
                "total": 0,
                "items_per_page": page_size,
                "next": None,
            }
