import os

import pdfkit
from django.template.loader import get_template, render_to_string

from sherlock.celery import app
from utils.email_utils import EmailSender

from .helpers import get_recipients_email, get_report_details
from .models import ScheduleReport


@app.task
def send_scheduled_report(schedule_report_id):
    schedule = (
        ScheduleReport.objects.filter(id=schedule_report_id)
        .select_related("created_by")
        .first()
    )
    report = schedule.report
    to = get_recipients_email(schedule.to)
    bcc = get_recipients_email(schedule.bcc)
    cc = get_recipients_email(schedule.cc)
    subject = schedule.subject
    result, error = get_report_details(
        report, user=schedule.created_by, limit=10, offset=0
    )
    template = get_template("report_delivery/index.html")
    html = template.render({**result})
    options = {
        "page-size": "A4",
    }

    file_name = f"scheduled_report_{schedule.id}.pdf"
    file_dir = "media/scheduled_reports/"
    file_path = file_dir + file_name
    if not os.path.exists(file_dir):
        os.makedirs(file_dir)
    _ = pdfkit.from_string(html, file_path, options, verbose=True)

    mail_body = render_to_string(
        template_name="report_delivery/index.html", context={**result}
    )
    EmailSender.report_delivery_mail(
        subject, mail_body, to, bcc, cc, file_path, file_name
    )
