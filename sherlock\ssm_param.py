import os

try:
    import boto3
except ImportError:
    raise "boto3 AWS client is not installed. Run 'pip install boto3'"

try:
    from ssm_cache import SSMParameter, SSMParameterGroup
except ImportError:
    raise "python ssm cache is not installed. Run 'pip install ssm-cache'"


class SSMParameterStoreManager:
    _service_name = "ssm"
    _region_name = "eu-central-1"

    def __init__(self, base_path, max_age=1440, **connection_params):
        """
        Connection params are those extra parameters that can be used to initialize the boto3 client e.g.
        aws_access_key_id, aws_secret_access_key etc.
        """
        self._base_path = base_path
        self._max_age = max_age
        if connection_params:
            self._client = self.__create_client(**connection_params)
            self.__set_client()

    def __set_client(self):
        SSMParameter.set_ssm_client(self._client)
        SSMParameterGroup.set_ssm_client(self._client)

    def __create_client(self, **connection_params):
        return boto3.client(
            self._service_name, region_name=self._region_name, **connection_params
        )

    def _get_parameters_group(self) -> SSMParameterGroup:
        group = SSMParameterGroup(base_path=self._base_path, max_age=self._max_age)
        return group

    def _strip_path(self, parameter_path):
        return parameter_path.split("/")[-1]

    def _get_parameter(self, param_name) -> SSMParameter:
        group = SSMParameter(param_name, max_age=self._max_age, with_decryption=True)
        return group

    def get_parameter(self, param_name=None) -> SSMParameter:
        return self._get_parameter(param_name)

    def get_parameters(self, path) -> list[SSMParameter]:
        group = self._get_parameters_group()
        return group.parameters(path)

    def get_parameters_as_dict(self, path) -> dict[str, str]:
        parameters = self.get_parameters(path)
        return {
            self._strip_path(parameter.name): parameter.value
            for parameter in parameters
        }

    def set_env(self, path) -> None:
        parameters = self.get_parameters(path)
        for parameter in parameters:
            # Strip away the path to leave the parameter name
            name = self._strip_path(parameter.name)
            if name in os.environ:
                os.environ["name"] = parameter.value
            else:
                os.environ.setdefault(name, parameter.value)


def load_parameter_store(base_path, path):
    store_manager = SSMParameterStoreManager(base_path)
    store_manager.set_env(path)
