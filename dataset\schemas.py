from typing import Any
from uuid import UUID

from ninja import FilterSchema as Filter
from ninja import ModelSchema, Schema
from pydantic import Field, model_validator

from accounts.schemas import RoleListSchema

from .models import Dataset, Rule, RuleFilter


class DataSourceSchema(Schema):
    database_name: str
    hostname: str = Field(description="The database address")
    port: int
    username: str
    password: str
    alias: str = ""


class DataSourceSchemaResp(Schema):
    datasource_id: int
    schemas: list[str] | None = None


class DataSourceUpdateSchema(Schema):
    old_password: str | None = None
    new_password: str | None = None
    alias: str | None = None


class DatasetCreateSchema(Schema):
    datasource_id: int
    password: str | None = None
    schemas: list[str]


class DatasetSchema(Schema):
    dataset_identifier: UUID
    name: str
    tables: list[str]


class DatasetListSchema(Schema):
    identifier: UUID
    name: str


class DatasetDetailSchema(ModelSchema):
    class Meta:
        model = Dataset
        fields = ["identifier", "name", "created_at", "updated_at"]


class DatasetUpdateSchema(Schema):
    name: str | None = None


class TableSchema(Schema):
    dataset_identifier: UUID
    tables: list[str]


class TableListSchema(Schema):
    id: int
    name: str
    alias: str | None = None
    schema_name: str | None = None


class ColumnUpdateSchema(Schema):
    id: int
    description: str | None = None
    alias: str | None = None
    sensitivity: str | None = None
    tags: list[int] = []


class TableUpdateSchema(Schema):
    # name: str | None = None
    alias: str | None = None
    description: str | None = None
    columns: list[ColumnUpdateSchema] | None = None


class TagSchema(Schema):
    name: str
    alias: str


class TagsListSchema(Schema):
    id: int
    name: str
    alias: str


class EditTagSchema(Schema):
    name: str | None = None
    alias: str | None = None


class ColumnSchema(Schema):
    id: int
    name: str
    data_type: str
    description: str | None = None
    alias: str | None = None
    sensitivity: str | None = None
    tags: list[TagsListSchema] | None = None


class TableDetailSchema(Schema):
    id: int
    name: str
    alias: str | None = None
    description: str | None = None
    schema_name: str | None = None
    # columns: List[ColumnSchema]=None


class TableFilterSchema(Filter):
    search: str = Field(
        None,
        q=[
            "name__icontains",
            "alias__icontains",
            "columns__name__icontains",
            "description__icontains",
        ],
    )


class FilterSchema(Schema):
    column_name: str
    operator: str
    value: Any  # value can be numeric or non mumeric
    connector: str = "AND"  # default to AND if a connector is not passed

    @model_validator(mode="after")  # model_validator to validate multiple class fields
    def validate_attrs(self):
        # operator = self.operator
        operator = self.operator.lower() if self.operator.isalpha() else self.operator
        valid_operators = (">", ">=", "=", "<", "<=", "!=", "and", "like", "ilike")

        value = self.value
        if operator not in valid_operators:
            raise ValueError("Invalid operator.")

        if operator == "like" or operator == "ilike":
            value = f"%{value}%"

        elif isinstance(value, str):
            value = f"{value}"

        return self


class ConditionSchema(Schema):
    """schema for condition:
    condition contains one or more filter object with connector.
    several conditions make a group
    """

    filter: list[FilterSchema]
    connector: str | None = None


class RuleSchema(Schema):
    name: str
    table_id: int
    group: list[ConditionSchema] | None = []
    columns: dict
    assigned_users: list[int] | None = []
    assigned_roles: list[int] | None = []
    is_applied_to_everyone: bool
    is_general: bool


class RuleModelSchema(ModelSchema):
    get_created_by: str
    roles: list[RoleListSchema]

    class Meta:
        model = Rule
        exclude = ["created_by"]


class RuleFilterSchema(ModelSchema):
    class Meta:
        model = RuleFilter
        fields = ["filter", "connector"]


class RuleRespSchema(Schema):
    rule: RuleModelSchema
    group: list[RuleFilterSchema] | None = None
    assigned_to: list[dict]


class RuleUpdateSchema(Schema):
    name: str
    group: list[ConditionSchema]
    columns: dict
    assigned_users: list[int] = []
    assigned_roles: list[int] = []
    is_applied_to_everyone: bool
    is_general: bool


class WarehouseSchema(Schema):
    result: Any | None = None


class DatasetSearchSchema(Schema):
    id: int
    name: str
    alias: str
