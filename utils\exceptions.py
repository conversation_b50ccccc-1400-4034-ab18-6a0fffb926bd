class MissingHeaderException(Exception):
    pass


class WrongQueryError(Exception):
    def __init__(self, message=None):
        if message is None:
            message = "Incorrect query detected"
        self.message = message
        super().__init__(self.message)


class CorrectionVariablesNotSetError(Exception):
    def __init__(
        self, message="Correction variables not found when trying to correct SQL query"
    ):
        self.message = message
        super().__init__(self.message)


class MaximumRetriesError(Exception):
    def __init__(self, message="Maximum retries reached"):
        self.message = message
        super().__init__(self.message)


class TTSQLPipelineConnectError(Exception):
    def __init__(self, message=None):
        if message is None:
            message = "Failed to Build TTSQL Pipeline"
        else:
            message = "Failed to Build TTSQL Pipeline!!!\n" + message
        self.message = message
        super().__init__(self.message)


class MaximumContextError(Exception):
    def __init__(self, message="Maximum retries reached"):
        self.message = message
        super().__init__(self.message)


class DatabaseConnectionError(Exception):
    def __init__(self, message=None):
        if message is None:
            message = "Failed to connect to database"
        else:
            message = "Failed to connect to database!!!\n" + message
        self.message = message
        super().__init__(self.message)


class KnowledgebaseConnectionError(Exception):
    def __init__(self, message=None):
        if message is None:
            message = "Failed to connect to vector database"
        else:
            message = "Failed to connect to vector database!!!\n" + message
        self.message = message
        super().__init__(self.message)


class SupabaseConnectError(Exception):
    pass


class UnsafeCodeError(Exception):
    def __init__(self, message=None):
        if message is None:
            message = "Access to system operators and attributes is not allowed"
        self.message = message
        super().__init__(self.message)
