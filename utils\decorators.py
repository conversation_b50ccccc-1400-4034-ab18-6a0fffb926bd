import asyncio
from collections.abc import Callable
from functools import wraps


def decrypt_encrypt(func: Callable) -> Callable:
    @wraps(func)
    def _decorator(*args, **kwargs):
        return func(*args, **kwargs)

    @wraps(func)
    async def _async_decorator(*args, **kwargs):
        return await func(*args, **kwargs)

    if asyncio.iscoroutinefunction(func):
        return _async_decorator
    return _decorator
