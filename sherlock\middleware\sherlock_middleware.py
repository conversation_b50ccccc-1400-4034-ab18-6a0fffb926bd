import json
import traceback
from json import JSONDecodeError

from django.conf import settings
from django.utils import timezone

from sherlock.celery import save_request_log

successful_status = {200, 201, 302, 301}


class SherlockMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.request_time = timezone.now()

    def __call__(self, request):
        response = self.process_response(request)
        return response

    def process_response(self, request):
        response_time = timezone.now()
        raw_body = request.body
        response = self.get_response(request)

        static_url = getattr(settings, "STATIC_URL", None)
        if static_url:
            if static_url in request.path_info:
                return response

        if request.method in ("HEAD", "OPTIONS", "TRACE"):
            return response

        if hasattr(request, "user") and request.user.is_authenticated:
            user = request.user
        elif hasattr(request, "auth"):
            user = request.auth
        else:
            user = None

        if user:
            user = {
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
            }

        path = request.path_info
        log_type = "success" if response.status_code in successful_status else "failed"

        if response.streaming:
            response_body = "Streamed Content"
        else:
            response_body = response.content.decode("utf-8")

        exception = getattr(self, "exception", None)
        data = {}

        try:
            raw_body = raw_body.decode("utf-8")
            if raw_body:
                data = json.loads(raw_body)
        except (UnicodeDecodeError, JSONDecodeError):  # nosec
            pass  # nosec

        if not data:
            data = {}

        # remove password, csrf tokens
        keys = [
            "password",
            "csrfmiddlewaretoken",
        ]

        for key in keys:
            if key in data:
                _ = data.pop(key)

        # remove jwt tokens
        if "accounts" in path:
            response_body = {}

        log_data = {
            "user_agent": request.META.get("HTTP_USER_AGENT", ""),
            "ip_address": request.META.get("REMOTE_ADDR", ""),
            "host_name": request.META.get("REMOTE_HOST", ""),
            "user": user,
            "content_type": request.META.get("CONTENT_TYPE", ""),
            "query_string": request.META.get("QUERY_STRING", ""),
            "http_method": request.method,
            "http_referer": request.META.get("HTTP_REFERER", ""),
            "path_info": path,
            "post_data": data,
            "response_status_code": response.status_code,
            "log_status": log_type,
            "response_reason_phrase": response.reason_phrase,
            "response_body": response_body
            if response_body
            else self.traceback
            if exception
            else None,
            "attempt_time": self.request_time,
            "response_time": response_time,
        }

        save_request_log.delay(log_data)
        return response

    def process_exception(self, request, exception):
        self.exception = str(exception)
        self.traceback = traceback.format_exc()
