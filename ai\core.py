"""
Script Name: Sherlock-Core
Author: <PERSON><PERSON><PERSON><PERSON>
Created At: 2023-09-26

Modified By: <PERSON>
Last Modified At: 2024-08-27
Description: This script is the core of Sherlock 🤣 IFYKYK.
"""

import os
import sqlite3
from pathlib import Path

import numpy as np
import openai
import pandas as pd
import sqlalchemy
from asgiref.sync import sync_to_async
from dotenv import find_dotenv, load_dotenv
from langchain.prompts import PromptTemplate
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, ToolMessage
from langgraph.checkpoint.sqlite import SqliteSaver

# langgraph
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode, tools_condition

from ai.credentials import db_connect
from ai.observe import start_instrument
from ai.prompts import (
    CONSOLIDATOR_PROMPT,
    EXPLAINER_PROMPT,
    SYSTEM_PROMPT_V4,
    TXT_TO_SQL_TOOL_PROMPT,
)
from ai.settings import SherlockSettings
from ai.utils import (
    calculate_ttl_until_midnight,
    consolidate,
    duration,
    format_data,
    generate_sql_hash,
    identify_sensitive_columns,
    make_tool,
)
from ai.workflow import Explain, State, Text2SQL_V3
from dataset.helpers import get_sensitive_columns
from utils.cache_utils import get_from_cache, set_cache
from utils.exceptions import (
    MaximumContextError,
    MaximumRetriesError,
    TTSQLPipelineConnectError,
)

load_dotenv(find_dotenv())
dir_path = os.path.dirname(os.path.realpath("__file__"))
source_engine, conn_source = db_connect()

db_path = Path("ai/db")
if not db_path.exists():
    os.mkdir(db_path)
db_file = os.path.join(db_path, "memory.sqlite3")
memconn = sqlite3.connect(db_file, check_same_thread=False)


class Sherlock:
    """
    - runs the generated sql against the workbench datawarehouse

    - filters out personal information from data like names, phone numbers and addresses
    """

    # current_date = datetime.now()
    model_version = (
        "2024-08-27"  # current_date.strftime("%d %B %Y")  # Date the model was released
    )

    max_query_length = 500
    core_exception_message = (
        "Your question appears to be unrelated to the knowledge I've been trained on."
    )
    filtered_fields_message = "The following fields ```{}``` have been masked from this response for privacy reasons."

    def __init__(self):
        self.source_engine, self.conn_source = source_engine, conn_source

    def filter_restricted_columns(self, df: pd.DataFrame, sql_query: str):
        """Filtes restricted column from extracted data"""

        if isinstance(df, pd.DataFrame):
            all_sensitive_columns = set(get_sensitive_columns())
            filter_message = ""
            is_filtered = False
            is_sensitive = len(all_sensitive_columns) > 0
            df.columns = [col.strip() for col in df.columns]
            # df = df[all_columns]
            if is_sensitive:
                sensitive_columns = identify_sensitive_columns(
                    sql_query, all_sensitive_columns
                )
                sensitive_columns = list({col.strip() for col in sensitive_columns})
                is_filtered = len(sensitive_columns) > 0
                filter_message = self.filtered_fields_message.format(
                    ", ".join(sensitive_columns)
                )

                df[sensitive_columns] = "*****"

            return df, is_filtered, filter_message

        SherlockSettings.logger.warning(f"Got data type {type(df)}")
        raise ValueError("Invalid Input Data")

    def replace_missing_values(self, df: pd.DataFrame):
        """Replace missing values from data"""
        if isinstance(df, pd.DataFrame):
            df = df.fillna("Not Avaialable")
            return df

    def df_to_dict(self, df: pd.DataFrame):
        """Converts dataframe to dictionary"""
        if isinstance(df, pd.DataFrame):
            return df.to_dict()
        else:
            return df

    def dfs_to_dicts(self, dfs: list[pd.DataFrame]):
        """Converts dataframe to dictionary"""
        ls_dfs = [self.df_to_dict(df) for df in dfs]
        return ls_dfs
        # return df.to_dict()

    def clear_chat_memory(self, thread_id):
        """clears a conversation history
        Parameters:
            thread_id: conversation id
        """
        # clear memory
        try:
            q = (
                "DELETE FROM checkpoints WHERE thread_id = %(thread_id)s",
                {"thread_id": thread_id},
            )
            memconn.execute(q)
            memconn.close()
            SherlockSettings.logger.info(f"Memory: memory history cleared {thread_id}")
            return True
        except Exception as err:
            SherlockSettings.logger.error(
                f"Error Clearing memory for thread {thread_id}: {str(err)}"
            )
            memconn.close()
            raise ValueError("Failed to clear memory") from err


sherlock = Sherlock()
start_instrument()
# new changes and classes are below


class AICore:
    """This the Core class of the sherlock model
    Attributes:
    ----------
    model_chain: AIChain
    chat_graph: conversation graph workflow build from the AIChain
    source_engine: sqlalchemy.engine: connection engine to the source database
    utility_llm: This the llm used for other less intensive tasks such as explain

    Methods:
    -------
    explainer(user_query, sql_query, result):
        explains the result of the sql query as well as the sql query
    build_workflow()

    """

    def __init__(self) -> None:
        self.source_engine = source_engine
        self.memory = SqliteSaver(conn=memconn)
        self.system_message = SystemMessage(SYSTEM_PROMPT_V4)
        self.cons_prompt = PromptTemplate.from_template(CONSOLIDATOR_PROMPT)

    def initialize_system_prompt(self, state):
        """Initializes the system message. The system message gives the Sherlock it's Identity and how it should behave with users"""
        if isinstance(state["messages"][0], SystemMessage):
            pass
        else:
            state["messages"].insert(0, self.system_message)

        return state

    def build_text2sql_pipeline(self):
        """Builds the text to SQL (TTSQL) pipeline"""
        SherlockSettings.logger.info("Initiating Text-to-SQL Piepline...")
        try:
            sherlockqp = Text2SQL_V3(dbengine=source_engine)

            # Build the TTSQL pipiline
            sherlockqp.build_pipeline()

            # Create a Correction Pipeline
            sherlockqp.build_correction_pipeline()

            self.query_engine = sherlockqp

            return sherlockqp

        except Exception as err:
            SherlockSettings.logger.error(f"Error occured {str(err)}")
            raise TTSQLPipelineConnectError from err

    def build_workflow(self, llm=None):
        """Builds the conversation workflow API"""

        ttsqlqp = self.build_text2sql_pipeline()
        ttsql_gen_sql = ttsqlqp.make_tool_func()

        name = "txt2sql"
        desc = TXT_TO_SQL_TOOL_PROMPT
        ttsqltool = make_tool(func=ttsql_gen_sql, name=name, desc=desc)
        # planner_tool = make_tool(func=planner, name="query_planner", desc="Use this tool to draft a plan before writing a SQL query")
        tools = [ttsqltool]
        # tool_map = {tool.name: tool for tool in tools}
        if not llm:
            llm = SherlockSettings.chatllm

        llm_with_tools = llm.bind_tools(tools)

        def sherbot(state: State):
            self.initialize_system_prompt(state=state)
            try:
                messages = state.get("messages", [])
                if not messages:
                    raise ValueError("No messages found in the state.")
                current_message = messages[-1]
                if not isinstance(current_message, HumanMessage):
                    raise ValueError(
                        "Input message is not a HumanMessage. Expected a HumanMessage."
                    )
                response = llm_with_tools.invoke(messages)
                if hasattr(response, "tool_calls"):
                    tool_calls = response.tool_calls

                return {
                    "messages": response,
                    "current_message": current_message,
                    "sql_queries": None,
                    "data": None,
                    "further_question": None,
                    "tool_calls": tool_calls,
                }

            except openai.BadRequestError as error:
                error.add_note("Conversation has reached maximum context")
                SherlockSettings.logger.error(str(error))
                raise MaximumContextError from error

        def run_query(state: State):
            tool_calls = state["tool_calls"]

            tool_messages = state["messages"][-len(tool_calls) :]

            query_results = []
            queries = []
            for tool_message, tool_call in zip(tool_messages, tool_calls):
                if not isinstance(tool_message, ToolMessage):
                    raise ValueError("Not a ToolMessage.")
                query = tool_message.content
                df, query = self.execute_query(query=query)

                df, is_filtered, filter_message = sherlock.filter_restricted_columns(
                    df, query
                )
                tool_call_msg = tool_call.get("args", {}).get("__arg1")
                query_results.append(
                    {"query": query, "data": df.to_dict(), "sub_prompt": tool_call_msg}
                )

                queries.append(query)
                metadata = {
                    "is_filtered": is_filtered,
                    "filter_message": filter_message,
                }

            return {
                "data": query_results,  # df.to_dict(),
                "current_message": state["current_message"],
                "sql_queries": tuple(queries),
                "metadata": metadata,
            }

        def explain(state: State):
            if len(state["messages"]) == 0:
                raise ValueError("No messages found in the state.")

            state_queries = state["sql_queries"]
            results = state["data"]
            user_msg = state["current_message"].content

            all_data = []  # TO BE OPTIMIZED. THIS IS A TEMPORARY FIX
            if not state_queries:
                raise ValueError("Query not found in state.")

            try:
                if results is None or len(results) == 0:
                    # log the error but do not raise an exception
                    SherlockSettings.logger.error("No data found in the state.")
                    insight = "The query did not return any result."
                    follow_up = " "
                    return {
                        "messages": AIMessage(insight),
                        "further_question": follow_up,
                        "sql_queries": state_queries,
                        "data": results,
                    }

                elif len(results) == 1:
                    df_metadata = format_data([results[0]["data"]])
                    explanation = self.explainer(
                        state_queries[0], data=df_metadata, user_msg=user_msg
                    )
                    final_result = pd.DataFrame.from_dict(results[0]["data"])

                elif len(results) > 1:
                    sub_queries = "\n\n".join(
                        [f"SubQuery {i+1}:\n{q}" for i, q in enumerate(state_queries)]
                    )
                    all_data = [d["data"] for d in results]
                    dfs_metadata = format_data(all_data)
                    cons_llm = (
                        PromptTemplate.from_template(CONSOLIDATOR_PROMPT)
                        | SherlockSettings.utilty_llm
                    )
                    final_result = consolidate(
                        cons_llm, dfs_metadata, user_msg, sub_queries
                    )
                    final_df_metadata = format_data([final_result])
                    explanation = self.explainer(
                        sub_queries, data=final_df_metadata, user_msg=user_msg
                    )
                else:
                    final_result = pd.DataFrame()
                    explanation = "Sorry, I couldn't provide an explanation at the moment because the query did not return any result."
                    follow_up = " "
                insight = explanation.insight
                follow_up = explanation.follow_up

            except Exception as err:
                SherlockSettings.logger.error(f"Error in explanation: {str(err)}")
                final_result = pd.DataFrame()
                insight = "Sorry, I couldn't provide an explanation at the moment"
                follow_up = None

            # explanation = self.explainer(sql_query, data=df, user_msg=user_msg)
            is_filtered = state["metadata"]["is_filtered"]
            filter_message = state["metadata"]["filter_message"]
            if is_filtered:
                insight += f"\n\nNote: {filter_message}"
                SherlockSettings.logger.info(filter_message)  # FILTERED MESSAGE HERE

            final_result = final_result.replace({np.nan: None})
            return {
                "messages": AIMessage(insight),
                "further_question": follow_up,
                "sql_queries": state_queries,
                "data": final_result.to_dict(),
            }

        tool_node = ToolNode(tools=tools)

        graph_builder = StateGraph(State)

        graph_builder.add_node("sherlock", sherbot)
        graph_builder.add_node("tools", tool_node)
        graph_builder.add_conditional_edges("sherlock", tools_condition)
        graph_builder.add_node("executor", run_query)
        graph_builder.add_node("explainer", explain)

        graph_builder.set_entry_point("sherlock")

        graph_builder.add_edge("tools", "executor")
        graph_builder.add_edge("executor", "explainer")

        graph_builder.set_finish_point("explainer")

        graph = graph_builder.compile(checkpointer=self.memory)

        # save_graph_chart(graph=graph)
        self.chat_graph = graph
        return self

    @duration
    def explainer(self, sql_query, data, user_msg):
        """Explains the result returned from the database
        Parameters:
        -----------
        user_query: The question the user asks
        sql_query: The sql_query generated
        result: The result of the sql_query
        """
        template = EXPLAINER_PROMPT
        structured_llm = SherlockSettings.utilty_llm.with_structured_output(
            Explain, include_raw=True
        )
        input_temp = PromptTemplate(
            template=template, input_variables=["user_question", "sql_query", "data"]
        )

        explain_llm = input_temp | structured_llm
        try:
            exp_message = explain_llm.invoke(
                input={"user_question": user_msg, "sql_query": sql_query, "data": data}
            )
            if exp_message["parsed"] is not None:
                return exp_message["parsed"]
            else:
                raw_message = (
                    exp_message["raw"]
                    .content.strip("`")
                    .lstrip("json")
                    .lstrip("{")
                    .rstrip("}")
                )
                return Explain(insight=raw_message.strip(), follow_up=" ")
        except openai.BadRequestError as oaierr:
            SherlockSettings.logger.error(str(oaierr))
            return Explain(
                insight="Sorry, I couldn't provide an explantion at the moment",
                follow_up=" ",
            )

    @duration
    def execute_query(self, query, retries=2):
        """Execuutes the sql query
        Parameters:
        ----------
        query: sql query
        return_query: bool: if true, returns the result and the sql query
        """

        # check if cache exist

        try:
            sqlhash = generate_sql_hash(query)
            data = get_from_cache(sqlhash)
            if data is not None and not data.empty:
                return data, query
            with self.source_engine.connect() as con:
                res = con.execute(sqlalchemy.text(query))
                data = res.fetchall()
            df = pd.DataFrame(data)
            # sqlhash = generate_sql_hash(query)
            ttl = calculate_ttl_until_midnight()
            set_cache(sqlhash, df, timeout=ttl)
            return df, query

        except sqlalchemy.exc.ProgrammingError as wqerr:
            e = str(wqerr)

            sql_query = self.query_engine.correct(incorrect_sql=query, error=e)

            if sql_query and retries > 0:
                retries -= 1
                return self.execute_query(sql_query, retries=retries)
            SherlockSettings.logger.error(e)
            raise MaximumRetriesError()

    def format_message(self, explain):
        """
        This function is used to format the messages.
        Takes t

        Parameters:
        ai_response (str): Response from the AI.
        explain (str):
        """
        try:
            insight = explain.insight
            further_question = explain.follow_up

            condesned_response = insight

        except AttributeError as serr:
            SherlockSettings.logger.error(str(serr))
            condesned_response = "Sorry, I couldn't provide an explantion at the moment"
            further_question = " "

        return f"{condesned_response}<exp>{further_question}"

    def chat(self, user_prompt, converse_id="x01"):
        """This is the chat method of the AICore Class
        prompt: (str) The prompt from the user. This should be single prompt input from the user
        converse_id: (str) This a thread_id that is used by the model to track a conversation (thread)


        The output of this function is dcitionary with the follwing keys:
        - response: The actual response from the model (Answer to the question)
        - explain: The explanation of the response: This formatted in json and contains three values
            * Insight of the response
            * Futher question to be asked by the user
        - sql_query: The sql query that was generated
        - The user prompt recieved by the model
        - The returned data from the database

        Parameters:
        -----------
        user_prompt: The user message
        converse_id: The conversational ID to track the conversation thread
        """
        ai_message = None
        sql_query = None
        data = None
        further_question = None
        try:
            SherlockSettings.logger.info(
                f"================={converse_id}================="
            )
            config = {"configurable": {"thread_id": converse_id}}
            res = self.chat_graph.invoke({"messages": user_prompt}, config)
            ai_message = res["messages"][-1].content
            sql_query = res["sql_queries"]
            if sql_query:
                sql_query = "<Q>\n".join(res["sql_queries"])
            data = res["data"]

            if data is not None:
                data = sherlock.df_to_dict(data)

        except MaximumRetriesError:
            ai_message = "Sorry 😞, I am not able to answer this question. Try rephrasing your question then ask again"

        except sqlalchemy.exc.OperationalError as err:
            SherlockSettings.logger.error(str(err))
            ai_message = "Oops 😞 !! An error occured. Please try again"

        except MaximumContextError as err:
            SherlockSettings.logger.error(str(err))
            ai_message = """Oh 😓, you have reached the maximum context length for this conversation.
            Start a new chat to continue"""

        except openai.APIConnectionError as err:
            SherlockSettings.logger.error(str(err))
            ai_message = """Sorry, I am not to respond at this time. Please try again after some time"""
        except Exception as err:
            SherlockSettings.logger.error(str(err))
            ai_message = """Unexpected error has occured, please contact the administrator for complaint"""
            data = None

        return {
            "response": ai_message,
            "sql_query": sql_query,
            "next_question": further_question,
            "data": data,
        }

    async def a_chat(self, **kwargs):
        """Converts the AICore.chat method to asynchronous function"""
        return await sync_to_async(self.chat)(**kwargs)

    @duration
    def generate_prompt_prediction(self, question):
        """Names a conversation
        Parameters:
        ----------
        question: (str)
            The first user input
        """
        prompt_template = """
        Given a user message a predict precise and short meaningful title  for the conversation:

        You should return only the title

        e.g
        User: What is total number of farmers?
        AI: Number of Farmers

        User:{question}
        AI:

        """

        prompt = PromptTemplate.from_template(template=prompt_template)
        title_llm = prompt | SherlockSettings.utilty_llm
        prediction = title_llm.invoke(input={"question": question})
        title = prediction.content
        SherlockSettings.logger.info(f"New Conversation {question}: Title: {title}")
        return title.split(":")[-1]

    async def a_generate_prompt_prediction(self, **kwargs):
        """Converts the AICore.chat method to asynchronous function"""
        return await sync_to_async(self.generate_prompt_prediction)(**kwargs)

    async def new_chat(self, user_prompt, converse_id):
        """Executes title prediction and generate response asynchronously"""

        title = await self.a_generate_prompt_prediction(question=user_prompt)
        response = await self.a_chat(user_prompt=user_prompt, converse_id=converse_id)

        return title, response


# if __name__ == "__main__":
#     questions = [
#         "who are you",
#         "how many farmers do i have",
#         "how many of them are in nigeria",
#         "in what states do i have warehouses in nigeria",
#         "what of kenya",
#     ]
#     model_core = AICore()
#     while True:
#         user_input = input("\nquestion : ")
#         # user_prompt = i
#         response = model_core.chat(user_prompt=user_input)
#         ai_res = response["response"]
#         # print(ai_res)

#         EXP = None
#         if response["explain"] is not None:
#             while EXP not in ["y", "n"]:
#                 exp = input("Explain? (y/n): ").strip()
#             if exp == "y":
#                 print("Explanation: ", response["explain"])
#                 print("SQL QUERY:", response["sql_query"])
#             else:
#                 pass
