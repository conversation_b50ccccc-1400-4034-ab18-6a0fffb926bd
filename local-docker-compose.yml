version: "3"

services:
  web:
    restart: on-failure
    build: .
    env_file:
      - .env
    ports:
      - "4000:4000"
    command: bash -c "mkdir -p staticfiles && python manage.py makemigrations && python manage.py migrate && gunicorn -b 0.0.0.0:4000 -w 4 sherlock.wsgi:application"
    volumes:
      - ./staticfiles/:/home/<USER>/staticfiles/
      - ./ai/migrations:/home/<USER>/ai/migrations
      - ./accounts/migrations:/home/<USER>/accounts/migrations
    network_mode: "host"

  influxdb:
    image: influxdb:2.0.7
    env_file:
      - .env
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: ${INFLUXDB_USERNAME}
      DOCKER_INFLUXDB_INIT_PASSWORD: ${INFLUXDB_PASSWORD}
      DOCKER_INFLUXDB_INIT_ORG: ${INFLUXDB_ORG}
      DOCKER_INFLUXDB_INIT_BUCKET: ${INFLUXDB_BUCKET}
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: ${INFLUXDB_TOKEN}
    ports:
      - "8086:8086"
    volumes:
      - sherlock_influxdb:/var/lib/influxdb2

  redis:
    restart: unless-stopped
    image: redis:7.0.5-alpine
    ports:
      - "6379:6379"

  celery_worker:
    restart: on-failure
    build:
      context: .
    command: bash -c "celery -A sherlock.celery worker -l info"
    depends_on:
      - web
    network_mode: "host"

  celery_beat:
    restart: on-failure
    build:
      context: .
    command: bash -c "celery -A sherlock.celery beat -l info"
    depends_on:
      - web
    network_mode: "host"

  phoenix:
    image: arizephoenix/phoenix:latest
    ports:
      - "6006:6006"  # UI and OTLP HTTP collector
      - "4317:4317"  # OTLP gRPC collector
    environment:
      - COLLECTOR_ENDPOINT=http://localhost:6006/v1/traces
      - PHOENIX_WORKING_DIR:/mnt/phoenix_volume
      - PHOENIX_PROJECT_NAME:SherlockAI
    volumes:
      - phoenix_data:/mnt/phoenix_volume

volumes:
  sherlock_influxdb:
  phoenix_data:
