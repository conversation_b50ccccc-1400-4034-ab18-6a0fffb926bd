import http
import traceback

from django.conf import settings
from ninja import NinjaAP<PERSON>
from ninja.errors import AuthenticationError

from accounts.api import router as accounts_router
from accounts.auth import InvalidToken
from accounts.exceptions import (
    AuthorizationError,
    EmptyAuthorizationHeader,
    ExpiredTokenError,
)
from ai.api import router as ai_router
from dataset.api import router as dataset_router
from event_logging.api import router as event_logging_router
from self_service.api import router as self_service_router
from sherlock.exceptions import PaginationError

from .parsers import ORJ<PERSON>NParser
from .renderers import ORJ<PERSON>NRenderer

api = NinjaAPI(
    title="Sherlock Backend",
    description="List of APIs that powers Sherlock",
    renderer=ORJSONRenderer(),
    parser=ORJSONParser(),
    urls_namespace="sherlock",
)

api.add_router("/ai/", ai_router)
api.add_router("/accounts/", accounts_router)
api.add_router("/events/", event_logging_router)
api.add_router("/self-service", self_service_router)
api.add_router("/", dataset_router)


def exception_handler_base(
    request, exc, msg=None, status=http.HTTPStatus.INTERNAL_SERVER_ERROR
):
    return api.create_response(
        request,
        {
            "message": "An error has occurred",
            #   'error':  traceback.format_exc()
            "error": msg
            if msg
            else traceback.format_exc()
            if settings.DEBUG
            else "Please contact admin",
        },
        status=status,
    )


@api.exception_handler(InvalidToken)
def invalid_token(request, exc):
    return exception_handler_base(
        request,
        exc,
        msg="Username or password is incorrect. Please try again",
        status=401,
    )


@api.exception_handler(EmptyAuthorizationHeader)
def on_invalid_token(request, exc):
    return exception_handler_base(
        request, exc, msg="Authorization token is missing", status=401
    )


@api.exception_handler(AuthenticationError)
def unauthenticated_exception_handler(request, exc):
    return exception_handler_base(
        request, exc, msg="Unauthenticated", status=http.HTTPStatus.UNAUTHORIZED
    )


@api.exception_handler(AuthorizationError)
def unauthorized_exception_handler(request, exc):
    return exception_handler_base(
        request,
        exc,
        msg="You do not have permission to perform this action",
        status=http.HTTPStatus.FORBIDDEN,
    )


@api.exception_handler(PaginationError)
def http_error_exception_handler(request, exc: PaginationError):
    return exception_handler_base(request, exc.error, status=exc.status_code)


@api.exception_handler(ExpiredTokenError)
def expired_token_exception_handler(request, exc):
    return exception_handler_base(
        request, exc, "Session has expired. Please log in again", status=401
    )


@api.exception_handler(Exception)
def exception_handler(request, exc):
    return exception_handler_base(request, exc)
