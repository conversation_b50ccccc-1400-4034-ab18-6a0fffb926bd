"""
Django settings for sherlock project.

Generated by 'django-admin startproject' using Django 4.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path

import openai
from corsheaders.defaults import default_headers
from decouple import Csv, config

from utils.cache_utils import ONE_DAY

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY", cast=str)

DEBUG = config("DEBUG", default=False, cast=bool)

ALLOWED_HOSTS = config("ALLOWED_HOSTS", default=[], cast=Csv())

ADMINS = config("ADMINS", default=[], cast=Csv())
# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_celery_beat",
    "djcelery_email",
    "corsheaders",
    "django_auth_adfs",
    "django_prometheus",
    "ai",
    "accounts",
    "event_logging",
    "self_service",
    "dataset",
]


MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django_prometheus.middleware.PrometheusBeforeMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "sherlock.middleware.sherlock_middleware.SherlockMiddleware",
    "ninja_put_patch_file_upload_middleware.middlewares.process_put_patch",
    "django_prometheus.middleware.PrometheusAfterMiddleware",
]

ROOT_URLCONF = "sherlock.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "sherlock/templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

ASGI_APPLICATION = "sherlock.asgi.application"

WSGI_APPLICATION = "sherlock.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": config("DB_NAME", default="localhost"),
        "USER": config("DB_USERNAME", default=""),
        "PASSWORD": config("DB_PASSWORD", default=""),
        "HOST": config("DB_HOST", default="localhost"),
        "PORT": config("DB_PORT", default=""),
    }
}

AUTHENTICATION_BACKENDS = (
    "django.contrib.auth.backends.ModelBackend",
    "django_auth_adfs.backend.AdfsAuthCodeBackend",
)

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
    {
        "NAME": "sherlock.password_validation.MinimumCharacterTypeValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Africa/Lagos"

USE_I18N = True

USE_TZ = True

USE_THOUSAND_SEPARATOR = True

AUTH_USER_MODEL = "accounts.User"


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

USE_S3 = config("USE_S3", default=False, cast=bool)

if USE_S3:
    # aws settings
    AWS_ACCESS_KEY_ID = config("AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY = config("AWS_SECRET_ACCESS_KEY")
    AWS_STORAGE_BUCKET_NAME = config("AWS_STORAGE_BUCKET_NAME")
    AWS_S3_CUSTOM_DOMAIN = f"{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com"
    # s3 static settings
    STATIC_LOCATION = "static"
    STATIC_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{STATIC_LOCATION}/"
    STATICFILES_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

    # s3 public media settings
    MEDIA_LOCATION = "media"
    MEDIA_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{MEDIA_LOCATION}/"
else:
    STATIC_URL = "/static/"
    STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
    MEDIA_URL = "/media/"
    MEDIA_ROOT = BASE_DIR / "media"

STATICFILES_DIRS = []  # type: ignore

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


# CORS

CSRF_TRUSTED_ORIGINS = config("CSRF_TRUSTED_ORIGINS", default=[], cast=Csv())

CORS_ALLOWED_ORIGINS = config("CORS_ALLOWED_ORIGINS", default=[], cast=Csv())

CORS_ORIGIN_ALLOW_ALL = False

CORS_ALLOWS_CREDENTIALS = True

CORS_ALLOW_HEADERS = list(default_headers)

# Open ai
OPENAI_API_KEY = config("OPENAI_API_KEY", default="")  # load openai key
openai.api_key = OPENAI_API_KEY

JWT_SALT = config("JWT_SALT", cast=str)

JWT = {
    "ISSUER": "sherlock",
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=30),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
    "ALGORITHM": "HS256",
    "SIGNING_KEY": f"{SECRET_KEY}:{JWT_SALT}",
    "AUDIENCE": None,
}

INFLUXDB = {
    "TOKEN": config("INFLUXDB_TOKEN", default=""),
    "ORG": config("INFLUXDB_ORG"),
    "BUCKET": config("INFLUXDB_BUCKET"),
    "HOST": config("DB_HOST", default="localhost"),
}

CELERY_BROKER_URL = config("CELERY_BROKER", default="redis://redis:6379")
CELERY_RESULT_BACKEND = config("CELERY_BACKEND", default="redis://redis:6379")
if CELERY_RESULT_BACKEND == "django-db":
    INSTALLED_APPS += [
        "django_celery_results",
    ]

CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_RESULT_SERIALIZER = "json"
CELERY_TASK_SERIALIZER = "json"
CELERY_TIMEZONE = "Africa/Lagos"
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"

SSO_URL = config("SSO_URL")
SSO_API_KEY = config("SSO_API_KEY")
SSO_SECRET_KEY = config("SSO_SECRET_KEY")

PAGINATION_ITEMS_PER_PAGE = 20
MAX_USER_ALERT_COUNT = 5

AZURE_CLIENT_ID = config("AZURE_CLIENT_ID")
AZURE_CLIENT_SECRET = config("AZURE_CLIENT_SECRET")
AZURE_TENANT_ID = config("AZURE_TENANT_ID")

AUTH_ADFS = {
    "AUDIENCE": AZURE_CLIENT_ID,
    "CLIENT_ID": AZURE_CLIENT_ID,
    "CLIENT_SECRET": AZURE_CLIENT_SECRET,
    "CLAIM_MAPPING": {
        "first_name": "given_name",
        "last_name": "family_name",
    },
    "GROUPS_CLAIM": None,  # set group_claim to none to disable automatic group handling
    "USERNAME_CLAIM": "upn",
    "TENANT_ID": AZURE_TENANT_ID,
    "RELYING_PARTY_ID": AZURE_CLIENT_ID,
}

# Configure django to redirect users to the right URL for login
LOGIN_URL = "django_auth_adfs:login"
LOGIN_REDIRECT_URL = "/api/accounts/azure-sso"
FRONTEND_SERVER_URL = config("FRONTEND_SERVER_URL", default="http://localhost:4000")
FRONTEND_SET_PASSWORD_URL = FRONTEND_SERVER_URL + "/auth/set-password"
FRONTEND_SERVER_AZURE_CALLBACK = FRONTEND_SERVER_URL + "/api/auth/callback/azure-ad"

PASSWORD_RESET_TIMEOUT = 900

USE_X_FORWARDED_HOST = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# logging
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": os.path.join(BASE_DIR, "logs"),
        },
    },
    "loggers": {
        "django": {
            "handlers": ["file"],
            "level": "ERROR",
            "propagate": True,
        },
    },
}

# Email Credentials
SERVER_EMAIL = config("SERVER_EMAIL", default="")
EMAIL_USE_TLS = True
EMAIL_HOST = config("EMAIL_HOST", default=587)
EMAIL_PORT = 587
EMAIL_HOST_USER = SERVER_EMAIL
EMAIL_HOST_PASSWORD = config("EMAIL_HOST_PASSWORD")
DEFAULT_FROM_EMAIL = config("DEFAULT_FROM_EMAIL")
EMAIL_BACKEND = "djcelery_email.backends.CeleryEmailBackend"
# EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"


# 2FA settings
AUTHENTICATOR_ISSUER_NAME = "Sherlock"
LOGO_URL = f"{FRONTEND_SERVER_URL}/sherlock-logo.svg"


# Rate limit & Caching
PASSWORD_RETRY_MAX_COUNT = 5
INCORRECT_PASSWORD_LOCKOUT_TIME = 900
INCORRECT_PASSWORD_LOCKOUT_TIME_STEP = 900
IS_LOCKOUT_PERMANENT = True

# Warehouse caching
WAREHOUSE_DATA_CACHE_TIMEOUT = config(
    "WAREHOUSE_DATA_CACHE_TIMEOUT", cast=int, default=ONE_DAY
)

ENCRYPTION_ENABLED = config("ENCRYPTION_ENABLED", cast=bool, default=False)
ENCRYPTION_KEY = config("ENCRYPTION_KEY", default="")
ENCRYPTION_VECTOR = config("ENCRYPTION_VECTOR", default="")
PASSWORD_ENCRYPTION_KEY = config("PASSWORD_ENCRYPTION_KEY", default="")

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": [config("CELERY_BROKER", default="redis://redis:6379")],
        "KEY_PREFIX": "sherlock",
    }
}

PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
    "django.contrib.auth.hashers.ScryptPasswordHasher",
]


# warehouse api

WH_ADDRESS = config("WH_ADDRESS")
WH_USERNAME = config("WH_USERNAME")
WH_PASSWORD = config("WH_PASSWORD")
WH_PORT = config("WH_PORT")
WH_DBNAME = config("WH_DBNAME")


ENABLE_PROMPT = config("ENABLE_PROMPT", cast=bool, default=True)
