from django.db import models

from base.constants import (
    ACCOUNT_UPDATE,
    BCC,
    CAN_EDIT,
    CAN_VIEW,
    CC,
    CUSTOM,
    DAILY,
    DATA_ACCESS,
    DNR,
    EVERYONE,
    HIGH,
    INDIVIDUALS,
    LOW,
    MEDIUM,
    MONTHLY,
    OWNER,
    SCHEDULED_DELIVERY,
    SHARED_REPORT,
    TO,
    WEEKLY,
)


class Sensitivity(models.TextChoices):
    high = HIGH, HIGH
    medium = MEDIUM, MEDIUM
    low = LOW, LOW


class ShareOptions(models.IntegerChoices):
    EVERYONE = 0, EVERYONE
    INDIVIDUALS = 1, INDIVIDUALS


class AccessLevel(models.IntegerChoices):
    OWNER = 0, OWNER
    CAN_VIEW = 1, CAN_VIEW
    CAN_EDIT = 2, CAN_EDIT


class Frequency(models.TextChoices):
    daily = DAILY, DAILY
    weekly = WEEKLY, WEEKLY
    monthly = MONTHLY, MONTHLY
    custom = CUSTOM, CUSTOM
    dnr = DNR, DNR


class RecipientTypes(models.TextChoices):
    to = TO, TO
    cc = CC, CC
    bcc = BCC, BCC


class Category(models.TextChoices):
    shared_report = SHARED_REPORT, SHARED_REPORT
    schedule = SCHEDULED_DELIVERY, SCHEDULED_DELIVERY
    data_access = DATA_ACCESS, DATA_ACCESS
    account_update = ACCOUNT_UPDATE, ACCOUNT_UPDATE
