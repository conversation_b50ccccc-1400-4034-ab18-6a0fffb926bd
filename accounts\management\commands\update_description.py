import json
import os

from django.conf import settings
from django.core.management.base import BaseCommand

from dataset.models import Column, Dataset, Table


class Command(BaseCommand):
    help = "Update Table and column description"

    def handle(self, *args, **kwargs):
        # Load the JSON file
        file_name = "sherlock_table_descriptions.json"
        base_dir = settings.BASE_DIR / "dataset/metadata"
        file_path = os.path.join(base_dir, file_name)

        with open(file_path) as file:
            data = json.load(file)

        def update_descriptions(data):
            tables_to_update = []
            columns_to_update = []
            dataset = Dataset.active_objects.only("id").get(name__iexact="sherlock")

            for table_data in data:
                # Update or create the table description
                table_name = table_data["table_name"]
                table_description = table_data.get("description", "")

                # Retrieve or create the table
                table, created = Table.objects.get_or_create(
                    name=table_name, defaults={"dataset": dataset, "alias": table_name}
                )
                table.description = table_description
                if created:
                    table.save()

                tables_to_update.append(table)

                # Update each column's description
                for column_data in table_data.get("columns", []):
                    column_name = column_data["column_name"]
                    column_description = column_data.get("description", "")

                    # Retrieve or create the column with reference to the table
                    column, created = Column.objects.get_or_create(
                        name=column_name, table=table
                    )
                    column.description = column_description
                    if created:
                        table.save()

                    columns_to_update.append(column)

            # Perform bulk updates for tables and columns
            Table.objects.bulk_update(tables_to_update, ["description"])
            Column.objects.bulk_update(columns_to_update, ["description"])

        update_descriptions(data)
        self.stdout.write("Descriptions updated successfully.")
