from django.contrib import admin

from .models import Conversation, PromptLog, UserPrompt


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ("id", "user", "created_at", "updated_at")


@admin.register(UserPrompt)
class UserPromptAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "prompt",
        "sql",
        "user",
        "conversation",
        "model_version",
        "created_at",
        "updated_at",
    )


@admin.register(PromptLog)
class PromptLogAdmin(admin.ModelAdmin):
    list_display = ("get_prompt", "tokens", "cost", "created_at")

    @admin.display(description="Prompt")
    def get_prompt(self, obj):
        return obj.prompt.prompt
