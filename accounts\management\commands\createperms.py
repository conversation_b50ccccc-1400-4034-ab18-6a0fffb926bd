from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.core.management.base import BaseCommand

from accounts.models import User
from base.perm_constants import PERMISSION_GROUPS


class Command(BaseCommand):
    help = "Creates permission"

    def handle(self, *args, **kwargs):
        # remove all perms first

        # Create new permissions
        content_type = ContentType.objects.get_for_model(User)
        # Permission.objects.filter(content_type=content_type).delete()
        for _, perms in PERMISSION_GROUPS.items():
            for _, values in perms.items():
                for _, permission in values.items():
                    Permission.objects.get_or_create(
                        codename=permission,
                        content_type=content_type,
                        defaults={
                            "codename": permission,
                            "name": permission.replace("_", " "),
                        },
                    )

        self.stdout.write("Permissions created successfully.")
