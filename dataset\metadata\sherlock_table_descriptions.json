[{"table_name": "cx_dim_security", "columns": [{"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':**  \nThe 'id' column is a nullable bigint that serves as a unique identifier for each record in the `cx_dim_security` table. It is used to reference specific securities, facilitating the organization and retrieval of detailed information about their attributes and trading parameters."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'created'**  \nThis column records the timestamp indicating when the security entry was created, stored with time zone information. It is nullable, meaning that there may be instances where this timestamp is not recorded."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\n**Updated**: A timestamp with time zone indicating the last time the record was modified. This column is nullable and does not have a default value, allowing for the possibility that some records may not have an associated update time."}, {"column_name": "security_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_code':** \n\nThe `security_code` column contains a text identifier for each security in the `cx_dim_security` table. This column is nullable and does not have a default value. It serves to uniquely reference individual securities, facilitating their identification and tracking within the financial system."}, {"column_name": "security_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_name':** \n\nThe `security_name` column stores the names of various securities represented in the `cx_dim_security` table. This text field is nullable and can hold names that identify specific securities, providing a clear reference for users seeking to understand the attributes and characteristics of each security within the financial system."}, {"column_name": "board_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'board_code':** \n\nThe `board_code` column contains text values representing the specific trading board or market segment associated with a security. It is nullable, indicating that not all records are required to have an associated board code. This column helps identify the trading venue or classification of the security within the financial system."}, {"column_name": "board_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'board_name':**  \nThe 'board_name' column holds the name of the trading board or exchange associated with a security. This text field is nullable and does not have a default value, allowing for the representation of securities that may not be linked to a specific board. It provides additional context for the trading parameters and market visibility of securities in the `cx_dim_security` table."}, {"column_name": "can_be_sold", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'can_be_sold':** \n\nIndicates whether the security can be sold. This boolean column is nullable and does not have a default value, allowing for flexibility in representing the sellability status of different securities in the cx_dim_security table."}, {"column_name": "commodity_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`commodity_code`: A text field that optionally stores a unique identifier for different commodities within the `cx_dim_security` table, which contains detailed information about various securities. This code aids in distinguishing commodities and their associated attributes in the financial system."}, {"column_name": "commodity_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'commodity_name':**  \nThe 'commodity_name' column stores the names of various commodities associated with the securities in the `cx_dim_security` table. This text field is nullable, allowing for the possibility that some records may not have an associated commodity name. It does not have a default value, ensuring that any entry must be explicitly defined when present."}, {"column_name": "security_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_type':**  \nThe `security_type` column specifies the classification of the security represented in the `cx_dim_security` table. This text field allows for the identification of different types of securities, such as stocks, bonds, options, or derivatives. It is nullable, meaning that not all records are required to have a specified security type."}, {"column_name": "volume_per_unit", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'volume_per_unit':** \n\nRepresents the quantity of securities traded per unit, expressed as a bigint. This column may contain null values and does not have a default value. It is used to analyze trading volumes in relation to individual securities within the cx_dim_security table, which provides comprehensive details about various financial instruments."}, {"column_name": "currency_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'currency_code':**  \nThe `currency_code` column stores the currency in which the security is denominated. It is of text data type, allowing for flexibility in representing various currency formats. This column is nullable, meaning it can contain no value, and does not have a default value assigned."}, {"column_name": "currnecy_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'currnecy_name':**  \nThe `currnecy_name` column stores the name of the currency associated with the security. It is of type text, allows null values, and has no default value. This information aids in identifying the currency in which the security is denominated, contributing to the overall understanding of the security's attributes within the financial system."}, {"column_name": "lower_price_band", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `lower_price_band`**  \nThe `lower_price_band` column represents the lower boundary of the price range for a security, stored as a double precision value. This column is nullable, indicating that it may not always contain a value, and does not have a default value assigned. It is used in conjunction with other pricing attributes to define the pricing structure of securities within the `cx_dim_security` table."}, {"column_name": "upper_price_band", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `upper_price_band`**  \nThe `upper_price_band` column represents the upper limit of the price range for a security, defined as a double precision value. This column is nullable, indicating that it may not always contain a value, and it does not have a default value. It is used to help assess the pricing parameters and market behavior of the security within the `cx_dim_security` table."}, {"column_name": "is_virtual_security", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`is_virtual_security`: A boolean flag indicating whether the security is virtual (true) or not (false). This column is nullable and does not have a default value, allowing for the possibility that the virtual status of a security may be unknown."}, {"column_name": "show_on_live_market", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'show_on_live_market'**  \nIndicates whether the security is visible on the live market. This boolean column can be set to true or false and is nullable, allowing for the possibility that the visibility status is not defined."}, {"column_name": "show_on_ticker_tape", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'show_on_ticker_tape':**  \nIndicates whether the security should be displayed on the ticker tape. This boolean field is nullable and does not have a default value, allowing for flexibility in specifying visibility preferences for individual securities in the cx_dim_security table."}, {"column_name": "price_bound_percentage", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`price_bound_percentage`: This column represents the percentage bound for the pricing of a security, expressed as a double precision value. It is nullable, indicating that it may contain no value for some records. There is no default value assigned. This information is crucial for assessing the pricing limits and trading parameters of securities within the financial system."}], "description": "The `cx_dim_security` table contains detailed information about various securities, including their attributes, pricing bands, and market visibility options. It serves as a reference for understanding the characteristics and trading parameters of different securities within a financial system."}, {"table_name": "cx_dim_client", "columns": [{"column_name": "_airbyte_unique_key", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':**  \nThe 'id' column is a nullable bigint that serves as a unique identifier for each client in the cx_dim_client table. It facilitates the tracking and management of client records, supporting data integrity and auditability within the context of client information and compliance processes."}, {"column_name": "cid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'cid':** \n\nThe 'cid' column stores a unique identifier for each client in the `cx_dim_client` table. This text field is nullable, allowing for the possibility of missing values, and does not have a default value. It plays a crucial role in tracking client information and may be utilized in KYC processes and client relationship management."}, {"column_name": "rnb", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'rnb'**  \nThe 'rnb' column stores text data related to the client's records within the `cx_dim_client` table. This column is nullable and does not have a default value, indicating that it may not always contain information. It may be used to capture specific client attributes or notes relevant to their account status, KYC processes, or other compliance measures."}, {"column_name": "email", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'email':**  \nThe 'email' column stores the client's email address as a text value. It is nullable, allowing for the absence of an email address, and does not have a default value. This column is essential for client communication and may be used for verification purposes within the KYC processes."}, {"column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'phone':**  \nThe 'phone' column stores the contact phone number of the client as a text value. It is nullable, allowing for the possibility that a client may not have a phone number recorded. There is no default value assigned to this column. This information is essential for client communication and may assist in KYC processes and relationship management."}, {"column_name": "address", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'address':** \n\nThe 'address' column stores the client's residential or business address as a text field. It is nullable, allowing for instances where address information may not be provided. There is no default value assigned to this column. This data is essential for client identification and communication within the context of the cx_dim_client table, which tracks comprehensive client details and compliance-related attributes."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created':** \n\nThe 'created' column stores the timestamp of when the client record was created, including the associated time zone. This column is nullable and does not have a default value, allowing for flexibility in the data entry process. It plays a crucial role in maintaining data integrity and supporting audit trails within the `cx_dim_client` table."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'updated':**  \nThe 'updated' column records the timestamp of the last modification made to the client record in the `cx_dim_client` table. This column is of type timestamp with time zone, allowing for accurate tracking of updates across different time zones. It is nullable, meaning that it may not always have a value, and has no default value assigned. This column is essential for maintaining data integrity and auditability, reflecting changes in client information and ensuring compliance with KYC processes."}, {"column_name": "last_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'last_name':**  \nThe 'last_name' column stores the surname of the client, allowing for identification and differentiation among clients. It is of type text and can be left empty (nullable), without a default value, which accommodates clients who may not provide their last name. This information is essential for personal identification within the broader context of client data tracked in the `cx_dim_client` table."}, {"column_name": "first_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'first_name':** \n\nThe 'first_name' column stores the first name of clients as text. It is nullable, meaning that it may not always contain a value, and has no default value assigned. This column is part of the `cx_dim_client` table, which captures comprehensive client information for identification and compliance purposes."}, {"column_name": "share_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'share_code'**: A text field that optionally stores a unique code associated with the client's sharing or referral activities. It may be used for tracking purposes related to client referrals or promotions, and it can be left empty if not applicable."}, {"column_name": "is_approved", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_approved':**  \nIndicates whether the client has been approved (true) or not approved (false) in the context of account status and verification processes. This boolean field is nullable and does not have a default value, allowing for flexibility in representing clients whose approval status is yet to be determined."}, {"column_name": "account_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'account_type':**  \nThe `account_type` column specifies the type of account associated with a client in the `cx_dim_client` table. It is a text field that can be left empty (nullable) and does not have a default value. This column helps categorize clients based on their account classifications, which may be relevant for compliance, KYC processes, and client relationship management."}, {"column_name": "country_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'country_code':** \n\nThe 'country_code' column stores the text representation of the client's country code, which may be null. It is used to identify the geographical location of clients and supports compliance with KYC processes and client relationship management."}, {"column_name": "is_certified", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_certified':**  \nIndicates whether the client has been certified, reflecting their verification status in the KYC process. This boolean column is nullable and does not have a default value, allowing for flexibility in tracking clients who may not yet have a certification status."}, {"column_name": "is_synced_wb", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `is_synced_wb`**: A boolean flag indicating whether the client's information has been synchronized with the associated web platform. This column is nullable and has no default value, allowing for the possibility that synchronization status may be unknown."}, {"column_name": "referral_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `referral_code`**  \nA text field that stores a unique identifier used for tracking client referrals. This column is nullable, allowing for the absence of a referral code, and does not have a default value. It helps in analyzing client acquisition sources and relationships."}, {"column_name": "verify_me_dob", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`verify_me_dob`: This column stores the date of birth for client verification purposes. It is of text data type and is nullable, meaning it can contain no value. The absence of a default value indicates that it must be explicitly provided when applicable, playing a crucial role in the KYC processes and client identification within the `cx_dim_client` table."}, {"column_name": "is_afex_broker", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'is_afex_broker'**: A boolean flag indicating whether the client is associated with AFEX as a broker. This column is nullable and does not have a default value, allowing for flexibility in representing clients' broker status within the client data."}, {"column_name": "is_id_verified", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `is_id_verified`**  \nIndicates whether the client's identification has been verified. This boolean column can be NULL and does not have a default value, allowing for the representation of clients whose verification status is unknown. It plays a crucial role in evaluating compliance with KYC processes and overall client account status in the `cx_dim_client` table."}, {"column_name": "mismatch_value", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `mismatch_value`**: This column stores textual representations of discrepancies or inconsistencies identified during the client verification process. It may contain details about specific issues related to the client's KYC compliance or other attributes, aiding in the assessment and resolution of client-related discrepancies. The column is nullable, allowing for cases where no mismatch is detected."}, {"column_name": "is_bvn_verified", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `is_bvn_verified`**  \nIndicates whether the client's Bank Verification Number (BVN) has been verified. This boolean field can be null and does not have a default value, reflecting the verification status as part of the client's KYC processes in the `cx_dim_client` table."}, {"column_name": "is_kyc_complete", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `is_kyc_complete`**: A boolean indicating whether the Know Your Customer (KYC) verification process has been completed for the client. This column is nullable and has no default value, allowing for flexibility in representing clients at various stages of the KYC process."}, {"column_name": "is_kyc_rejected", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_kyc_rejected':** \n\nIndicates whether the client's KYC (Know Your Customer) verification has been rejected. This boolean field can be either true or false and is nullable, meaning it may not have a value assigned."}, {"column_name": "is_kyc_verified", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `is_kyc_verified`**: A boolean flag indicating whether the client has completed the Know Your Customer (KYC) verification process. This column is nullable and does not have a default value, allowing for the possibility that the KYC status may not be determined at the time of record creation."}, {"column_name": "client_broker_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `client_broker_id`**  \nThis column stores a bigint identifier representing the broker associated with the client. It is nullable, indicating that not all clients may have an associated broker. This information aids in tracking client-broker relationships and supports compliance and reporting efforts within the client management framework."}, {"column_name": "is_kyc_submitted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_kyc_submitted':**  \nIndicates whether the client has submitted the necessary documentation for the Know Your Customer (KYC) verification process. This boolean field is nullable, meaning it may not always have a value, and it does not have a default value."}, {"column_name": "is_update_pending", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_update_pending':** \n\nIndicates whether an update is pending for the client record. This boolean column can be null, allowing for cases where the update status is unknown."}, {"column_name": "user_account_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\nThe `user_account_type` column specifies the type of account associated with the client. It is of text data type and can be left null, indicating that not all clients may have an assigned account type. This column aids in categorizing clients for better management and compliance with KYC processes."}, {"column_name": "alternative_emails", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'alternative_emails':**  \nThis column stores additional email addresses associated with the client, allowing for alternative contact methods. It is of type text, can be left empty (nullable), and has no default value."}, {"column_name": "bvn_error_response", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`bvn_error_response`: This column stores text responses related to errors encountered during the Bank Verification Number (BVN) validation process for clients. It is nullable, allowing for the absence of data, and does not have a default value. This information is crucial for identifying and resolving issues in client verification, supporting compliance and KYC efforts within the `cx_dim_client` table."}, {"column_name": "used_referral_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'used_referral_code':**  \nThis column stores the text representation of any referral code that a client has utilized. It is nullable, allowing for instances where no referral code has been applied. This information aids in tracking client acquisition methods and evaluating referral program effectiveness within the broader context of client data management."}, {"column_name": "verify_me_lastname", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`verify_me_lastname`: This column stores the last name of clients as part of the verification process. It is of text data type and can be left empty (nullable). The information captured here aids in KYC compliance and client identification within the cx_dim_client table."}, {"column_name": "verify_me_firstname", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'verify_me_firstname'**: This column stores the first name of clients as part of the verification process. It is of text data type, allows NULL values, and does not have a default value. It contributes to the identification and validation of clients within the KYC framework and overall client relationship management in the `cx_dim_client` table."}, {"column_name": "verify_me_middlename", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`verify_me_middlename`: A text field that stores the middle name of the client for verification purposes. This column is nullable and does not have a default value, allowing for flexibility in client records where a middle name may not be provided."}, {"column_name": "id_verification_message", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`id_verification_message`: This text column optionally stores messages related to the client's identity verification process. It may contain notes, feedback, or status updates regarding the client's KYC compliance and verification efforts, aiding in the management of client relationships and ensuring thorough documentation of verification activities."}, {"column_name": "is_kyc_pending_approval", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** \n\n`is_kyc_pending_approval`: A boolean flag indicating whether the client's KYC (Know Your Customer) verification is pending approval. This column may contain null values and does not have a default value. It helps track the status of KYC processes within the client management framework."}, {"column_name": "bvn_verification_message", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\nThe `bvn_verification_message` column stores text messages related to the verification status of a client's Bank Verification Number (BVN). This column is nullable and does not have a default value, allowing for flexibility in recording verification outcomes or notes. It aids in tracking the client's compliance with KYC processes within the `cx_dim_client` table."}, {"column_name": "bank_verification_message", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `bank_verification_message`**: A text field that contains messages related to the bank verification status of a client. This column is optional and can be left empty, allowing for flexibility in recording verification communication or issues encountered during the KYC process."}, {"column_name": "kyc_verification_failed_date", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`kyc_verification_failed_date`: This column records the timestamp (with time zone) of the most recent failure in the KYC (Know Your Customer) verification process for a client. It is nullable, indicating that not all clients may have experienced a verification failure. This information aids in tracking compliance issues and understanding client status in the verification process."}, {"column_name": "is_brokerage_bank_account_verified", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `is_brokerage_bank_account_verified`**: Indicates whether the client's brokerage bank account has been verified. This boolean value can be either true or false and may be null if the verification status is not applicable or has not been determined."}, {"column_name": "_airbyte_ab_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "_airbyte_emitted_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "_airbyte_normalized_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "_airbyte_crm_client_hashid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "country", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'country':**  \nThe 'country' column stores the name of the country associated with the client. It is of type text, can contain null values, and has no default value. This information is essential for understanding the geographical context of the client and may be relevant for compliance, KYC processes, and demographic analysis."}, {"column_name": "region", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'region':** \n\nThe 'region' column stores the geographical region associated with the client. It is of text data type and can be left empty (nullable), allowing for flexibility in cases where a region is not specified. This information aids in understanding the client's location and demographic distribution, which can be crucial for compliance and marketing strategies."}, {"column_name": "subregion", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'subregion':**  \n\nThe 'subregion' column stores text-based information indicating the specific subregion associated with a client. This field is nullable, allowing for the absence of data, and does not have a default value. It aids in classifying clients by geographical area, enhancing client relationship insights and compliance measures within the cx_dim_client table."}], "description": "The `cx_dim_client` table stores detailed information about clients, including personal identification, contact details, account status, and verification statuses. It tracks various attributes related to client approval, KYC (Know Your Customer) processes, and referral codes, providing insights into client relationships and compliance measures. Additionally, it includes timestamps for record creation and updates, ensuring data integrity and auditability."}, {"table_name": "wb_fact_warehouse_dailyinventorybalance", "columns": [{"column_name": "inventory_date", "data_type": "date", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`inventory_date` - This column represents the date for which the daily inventory balance is recorded in the `wb_fact_warehouse_dailyinventorybalance` table. It captures the specific date of inventory data, allowing for tracking and analysis of inventory levels over time. This column is nullable and does not have a default value."}, {"column_name": "warehouse_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'warehouse_id':**  \nThe `warehouse_id` column represents the unique identifier for each warehouse in the `wb_fact_warehouse_dailyinventorybalance` table. It is a character varying data type, which allows for flexible formatting of warehouse identifiers. This column is nullable, indicating that it may not contain a value for every record, and it has no default value assigned. This identifier is crucial for linking inventory data to the respective warehouse, facilitating the analysis of inventory levels and statuses across different locations."}, {"column_name": "warehouse_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`warehouse_name`: Represents the name of the warehouse associated with the daily inventory balance records. This column is of text type, allows null values, and does not have a default value. It is used to identify the specific warehouse from which the inventory data is sourced."}, {"column_name": "item_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'item_id':**  \nThe `item_id` column is a character varying field that represents a unique identifier for each item in the daily inventory balance. It allows for the identification of specific items within the `wb_fact_warehouse_dailyinventorybalance` table and may contain null values, indicating that some records may not have an associated item identifier."}, {"column_name": "item_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'item_name':**  \nThe 'item_name' column stores the name of the item associated with the inventory balance in the `wb_fact_warehouse_dailyinventorybalance` table. This text field is nullable and does not have a default value, allowing for flexibility in documenting various items tracked within the warehouse inventory."}, {"column_name": "item_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'item_code':** \n\nThe 'item_code' column contains a textual identifier for each inventory item, allowing for the unique classification and tracking of items within the warehouse. This column is nullable and does not have a default value, reflecting the possibility that some inventory records may not have a specified item code."}, {"column_name": "item_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'item_type':**  \nThe 'item_type' column represents the classification of inventory items within the warehouse, allowing for differentiation between various categories of items. This column is of type character varying, is nullable, and does not have a default value, enabling flexibility in item categorization."}, {"column_name": "grade", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'grade':**  \nThe 'grade' column represents the quality classification of inventory items within the `wb_fact_warehouse_dailyinventorybalance` table. It is of varying character type, allows null values, and does not have a default value. This column provides insights into the grading of items, which may affect inventory valuation and management decisions."}, {"column_name": "tenant_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'tenant_id':**  \nThe 'tenant_id' column is a character varying field that identifies the tenant associated with each inventory record in the `wb_fact_warehouse_dailyinventorybalance` table. This column is nullable and does not have a default value, allowing for flexibility in representing inventory data for different tenants within the warehouse context."}, {"column_name": "latest_lien_bags", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'latest_lien_bags'**: An integer representing the most recent count of bags with active liens associated with the inventory for a specific date in the `wb_fact_warehouse_dailyinventorybalance` table. This column is nullable and does not have a default value, indicating that it may not always contain data for every record."}, {"column_name": "latest_available_bags", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `latest_available_bags`**  \nAn integer representing the most recent count of available bags in the warehouse inventory. This column may contain null values and does not have a default value. It provides insights into the current inventory levels of bags, as part of the daily inventory balance data captured in the `wb_fact_warehouse_dailyinventorybalance` table."}, {"column_name": "latest_total_bags", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `latest_total_bags`**  \nAn integer representing the most recent total count of bags available in the warehouse inventory. This column is nullable and does not have a default value, indicating that it may not always contain a value for every record."}, {"column_name": "latest_lien_net_weight", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `latest_lien_net_weight`**: This column represents the most recent recorded net weight of items subject to lien status in the warehouse inventory. It is a double precision floating-point value that can be null, indicating that there may be instances where the net weight is not recorded. This metric is essential for analyzing the weight of inventory items associated with liens on a given day."}, {"column_name": "latest_available_net_weight", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`latest_available_net_weight`: This column represents the most recent recorded net weight of inventory items in the warehouse, measured in double precision. It is nullable, indicating that there may be instances where the weight is not available. This metric is crucial for understanding the current inventory levels and weight status of items on a given date."}, {"column_name": "latest_total_net_weight", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`latest_total_net_weight`: This column represents the most recent total net weight of items in the warehouse, recorded as a double precision value. It is nullable, indicating that there may be instances where the weight is not available. This metric is crucial for assessing the overall inventory levels and weight distribution within the warehouse as of the latest inventory check."}, {"column_name": "latest_lien_gross_weight", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'latest_lien_gross_weight'**: This column represents the most recent gross weight of items associated with lien status in the warehouse inventory. It is stored as a double precision value and may contain null entries, indicating cases where weight data is not available."}, {"column_name": "latest_available_gross_weight", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`latest_available_gross_weight`: Represents the most recent recorded gross weight of items in the warehouse inventory. This value is stored as a double precision number and may be null if not applicable. It aids in assessing the weight metrics of inventory items on a specific date, contributing to overall inventory management and tracking."}, {"column_name": "latest_total_gross_weight", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`latest_total_gross_weight` (double precision, nullable) represents the most recent total gross weight of inventory items in the warehouse. This value is recorded to provide insights into the overall weight metrics of the daily inventory balance, aiding in assessments of inventory levels and management of warehouse operations."}, {"column_name": "warehouse_inventory_account_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`warehouse_inventory_account_id`: A character varying field that optionally identifies the specific inventory account associated with the warehouse's daily inventory balance. This column links to inventory account details, aiding in the tracking and management of warehouse inventory metrics."}], "description": "The `wb_fact_warehouse_dailyinventorybalance` table captures daily inventory balance data for various warehouses, detailing item specifics such as identification, type, and weight metrics. It tracks the availability and lien status of bags and weights, providing insights into inventory levels on a specific date for each warehouse."}, {"table_name": "ovs_fact_prices_securities_prices", "columns": [{"column_name": "date", "data_type": "date", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'date':**  \nThe 'date' column stores the specific date associated with each recorded price entry in the `ovs_fact_prices_securities_prices` table. It is of data type date, allowing for the representation of historical pricing data. This column is nullable, meaning it may contain no value for some records, and has no default value. It plays a crucial role in facilitating the analysis of price trends and fluctuations over time for various securities."}, {"column_name": "dow", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "description": "Column Description for 'dow':\n\nThe 'dow' column represents the day of the week (numeric format) associated with historical pricing data for securities in the `ovs_fact_prices_securities_prices` table. This column is nullable and does not have a default value, allowing for flexibility in recording daily price metrics and facilitating the analysis of price trends and fluctuations by day."}, {"column_name": "woy", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'woy':**  \nThe 'woy' column represents the week of the year associated with the pricing data for various securities. It is a text field that can be nullable, allowing for flexibility in data entry. This column aids in the analysis of weekly price trends and fluctuations, contributing to a comprehensive understanding of historical pricing patterns in the `ovs_fact_prices_securities_prices` table."}, {"column_name": "season", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'season':**  \nThe 'season' column contains text-based information that indicates the seasonal period relevant to the pricing data recorded in the `ovs_fact_prices_securities_prices` table. This column is nullable and does not have a default value, allowing for the possibility of missing seasonal data."}, {"column_name": "security_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `security_code`**: A text field representing the unique identifier for each security in the `ovs_fact_prices_securities_prices` table. This column is nullable and does not have a default value, allowing for flexibility in data entry. The `security_code` is crucial for linking pricing data to specific securities, facilitating the analysis of price trends and fluctuations."}, {"column_name": "security_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_name':**  \nThe `security_name` column stores the name of the security associated with the pricing data. It is of type text, allows null values, and does not have a default value. This information is crucial for identifying and analyzing specific securities within the historical pricing records stored in the `ovs_fact_prices_securities_prices` table."}, {"column_name": "security_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'security_type'**  \nThis column indicates the type of security, such as stock, bond, or derivative, associated with the pricing data in the `ovs_fact_prices_securities_prices` table. It is of text data type, allows null values, and has no default value, providing flexibility for various security classifications."}, {"column_name": "location", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'location':**  \nThe 'location' column stores textual information about the geographical or market location associated with each security's pricing data. It is nullable, allowing for the absence of a specified location, and does not have a default value. This data aids in contextualizing price trends and fluctuations relative to different markets or regions."}, {"column_name": "closing_price_kg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'closing_price_kg':** \n\nThe 'closing_price_kg' column represents the closing price of securities in kilograms, stored as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in recording historical pricing data in the context of the `ovs_fact_prices_securities_prices` table, which tracks various price metrics and trends for different securities over time."}, {"column_name": "closing_price_per_unit", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`closing_price_per_unit`: This column represents the closing price per unit of a security, stored as a double precision value. It is nullable, indicating that not all records may have a closing price available. The data is essential for analyzing historical pricing trends and fluctuations in the `ovs_fact_prices_securities_prices` table."}, {"column_name": "opening_price_kg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`opening_price_kg`: This column represents the opening price of a security, measured in kilograms, for a specific time period. It is stored as a double precision value and can be null, indicating that there may be instances where the opening price is not available. This data is crucial for analyzing the initial market value of securities at the beginning of a trading session."}, {"column_name": "opening_price_per_unit", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`opening_price_per_unit`: Represents the opening price per unit of a security for a given time period, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of missing data in the historical pricing records."}, {"column_name": "max_price_kg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `max_price_kg`**  \nThe `max_price_kg` column represents the maximum price per kilogram for a security, stored as a double precision value. This column is nullable, indicating that not all records may have a maximum price specified. It plays a crucial role in analyzing pricing trends and fluctuations over time within the context of historical pricing data for various securities."}, {"column_name": "max_price_per_unit", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\n`max_price_per_unit` - This column represents the maximum price per unit of a security recorded in the `ovs_fact_prices_securities_prices` table. It is of type double precision and may contain null values, indicating that the maximum price may not be available for all records. This metric is essential for analyzing price peaks and trends in the securities' historical pricing data."}, {"column_name": "min_price_kg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "Column Description: `min_price_kg` - This column represents the minimum price per kilogram for the associated security, recorded as a double precision value. It is nullable, indicating that there may be instances where this data is not available. This metric is part of the historical pricing data captured in the `ovs_fact_prices_securities_prices` table, facilitating analysis of pricing trends and fluctuations over time."}, {"column_name": "min_price_per_unit", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`min_price_per_unit` - This column represents the minimum price per unit for a security, recorded as a double precision value. It is nullable and does not have a default value, allowing for the possibility of missing data. This metric is part of the historical pricing data stored in the `ovs_fact_prices_securities_prices` table, which facilitates the analysis of price trends and fluctuations over time."}, {"column_name": "dod_price_diff", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `dod_price_diff`**  \nRepresents the difference in price for a security over a day-on-day (DoD) basis, expressed as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in recording price changes when data is unavailable."}, {"column_name": "dod_price_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'dod_price_change'**: Represents the change in price of a security from the previous day to the current day, measured as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of missing data in cases where price changes are not applicable or available."}, {"column_name": "week_start_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** \n\n`week_start_price` - Represents the price of a security at the beginning of the week, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of missing data in the historical pricing records."}, {"column_name": "week_end_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'week_end_price'**  \nRepresents the closing price of a security at the end of the week, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of missing data in weekly price records."}, {"column_name": "previous_week_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `previous_week_price`**: This column stores the price of a security from the previous week, represented as a double precision value. It is nullable, indicating that it may not have a value for all records, and it does not have a default value. This field is essential for analyzing weekly price changes and trends in the context of historical pricing data for various securities."}, {"column_name": "wow_price_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `wow_price_change`**  \nRepresents the change in price for a security over the course of a week, measured in double precision. This column is nullable and does not have a default value, allowing for the possibility of missing data in cases where no weekly price change is recorded."}, {"column_name": "season_start_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `season_start_price`**  \nThe `season_start_price` column represents the starting price of a security at the beginning of a specified season. It is stored as a double precision value and can be nullable, indicating that there may be instances where this information is not available. This column is crucial for analyzing seasonal price trends and fluctuations for securities recorded in the `ovs_fact_prices_securities_prices` table."}, {"column_name": "std_price_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `std_price_change`**  \nThe `std_price_change` column represents the standard deviation of price changes for a security over a specified period. It is a double precision value that indicates the volatility of the security's price movements, helping analysts assess the stability and risk associated with the security's price trends. This column is nullable, meaning that it may not always contain a value."}, {"column_name": "year_start_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `year_start_price`**: This column represents the starting price of a security at the beginning of a given year, recorded as a double precision value. It is nullable, meaning it may not contain a value for every record. This metric is essential for analyzing annual price trends and fluctuations in the context of historical pricing data."}, {"column_name": "ytd_price_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `ytd_price_change`**  \nThe `ytd_price_change` column represents the year-to-date price change of a security, measured as a double precision value. This column is nullable and does not have a default value. It provides insight into the security's price performance from the beginning of the current year to the present, facilitating the analysis of its price trends and fluctuations within that timeframe."}], "description": "The `ovs_fact_prices_securities_prices` table stores historical pricing data for various securities, including details such as daily, weekly, and seasonal price metrics, as well as price changes over different periods. It captures essential information related to each security, including its code, name, type, and location, enabling analysis of price trends and fluctuations over time."}, {"table_name": "ovs_fact_prices_aci", "columns": [{"column_name": "date", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \nThe 'date' column stores the timestamp of each recorded price entry in the `ovs_fact_prices_aci` table. This column is nullable and does not have a default value, allowing for flexibility in data entry. It serves as a key temporal reference for analyzing time-series trends and fluctuations in commodity prices."}, {"column_name": "commodity_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`commodity_code`: A text field that optionally stores a unique identifier for the specific commodity associated with the price data in the `ovs_fact_prices_aci` table. This column helps in categorizing and differentiating between various commodities for analysis of their price trends and fluctuations."}, {"column_name": "commodity", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'commodity':**  \nThe 'commodity' column stores the name or type of the commodity associated with the price data. It is of text data type, allows null values, and does not have a default value. This column is essential for identifying the specific commodity for which the time-series price metrics are recorded in the `ovs_fact_prices_aci` table."}, {"column_name": "closing_price_index_mt", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`closing_price_index_mt`: This column represents the closing price index for commodities in metric tons, stored as a double precision value. It may contain null values and does not have a default value, reflecting the closing price at the end of a specified period in the `ovs_fact_prices_aci` table, which captures time-series data on commodity prices and trends."}, {"column_name": "points", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'points'**  \nRepresents a numerical value in double precision that captures specific metrics related to commodity prices, allowing for detailed analysis of price trends and variations. This column is nullable and does not have a default value, indicating that it may not always contain data for every record."}, {"column_name": "prev_day_point", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `prev_day_point`**  \nRepresents the price point of the commodity from the previous day, stored as a double precision value. This column may contain null values and does not have a default value, allowing for flexibility in recording historical price data."}, {"column_name": "dod_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `dod_change`**: A nullable column of type double precision that represents the day-over-day change in commodity prices within the `ovs_fact_prices_aci` table. This metric indicates the variation in prices from one day to the next, providing insights into short-term price fluctuations."}, {"column_name": "prev_week_point", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'prev_week_point':**  \nThe 'prev_week_point' column represents the commodity price at the end of the previous week, stored as a double precision value. This column is nullable and does not have a default value, providing insights into week-over-week price changes in the context of the `ovs_fact_prices_aci` table's time-series data."}, {"column_name": "wow_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'wow_change':**  \nThe `wow_change` column represents the week-over-week change in commodity prices, stored as a double precision value. This column is nullable and does not have a default value, indicating that it may not always contain data for every entry in the `ovs_fact_prices_aci` table."}, {"column_name": "week_start", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'week_start':**  \nThe 'week_start' column stores the starting point of the week for each record in double precision format. It represents the date or timestamp indicating the beginning of the week associated with the commodity prices recorded in the `ovs_fact_prices_aci` table. This column is nullable and does not have a default value, allowing for flexibility in data entry for weeks without a defined start."}, {"column_name": "week_end", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'week_end':**  \nThe 'week_end' column represents the end date of the week associated with the recorded commodity prices. It is stored as a double precision value, allowing for the representation of dates in numerical form. This column is nullable and does not have a default value, indicating that it may not always be populated. Its purpose is to facilitate time-based analyses of price trends over weekly intervals within the `ovs_fact_prices_aci` table."}, {"column_name": "season_start", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'season_start':**  \nThe 'season_start' column represents the starting point of a season in a time-series format, stored as a double precision value. This column is nullable and does not have a default value. It is used to indicate the beginning of a specific pricing season for commodities, aiding in the analysis of price trends and fluctuations within the `ovs_fact_prices_aci` table."}, {"column_name": "std_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'std_change':** \n\nThe 'std_change' column represents the standard change in commodity prices over a specified period, measured as a double precision value. This column is nullable and does not have a default value, allowing for the potential absence of data in cases where the standard change is not applicable or available."}, {"column_name": "year_start", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'year_start':** \n\nThe 'year_start' column represents the starting year of the time period for the recorded commodity prices. It is stored as a double precision value, allowing for flexibility in representing year data. This column is nullable, indicating that it may not always contain a value."}, {"column_name": "ytd_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'ytd_change':**  \nThe 'ytd_change' column represents the year-to-date percentage change in commodity prices, stored as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in capturing price fluctuations throughout the year."}], "description": "The `ovs_fact_prices_aci` table stores time-series data related to commodity prices, capturing key metrics such as closing prices, daily and weekly changes, and various benchmarks over specified periods. It provides insights into price fluctuations and trends for different commodities."}, {"table_name": "ovs_fact_prices_aei", "columns": [{"column_name": "date", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'date':** \n\nThe 'date' column stores the timestamp of the recorded price data without time zone information. It is optional (nullable) and is used to track the specific date associated with each price entry in the `ovs_fact_prices_aei` table, facilitating analysis of temporal price trends and changes."}, {"column_name": "commodity_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'commodity_code':**  \nThe `commodity_code` column stores the unique identifier for each commodity represented in the `ovs_fact_prices_aei` table. This text field is nullable and does not have a default value. It is essential for linking price data to specific commodities, facilitating detailed analysis of market trends and price movements."}, {"column_name": "commodity", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'commodity'**  \nThis column stores the names or identifiers of various commodities associated with price-related data in the `ovs_fact_prices_aei` table. It is of text data type, nullable, and has no default value, allowing for the inclusion of diverse commodity types for comprehensive market analysis."}, {"column_name": "closing_price_index_mt", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `closing_price_index_mt`**\n\nThis column represents the closing price index for a given commodity, measured in double precision. It is nullable and does not have a default value. The data captured in this column contributes to the overall analysis of price trends and movements within the `ovs_fact_prices_aei` table, which tracks price-related data over time."}, {"column_name": "points", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'points':**  \nThe `points` column represents a numerical value stored as double precision, which may contain decimal values. It is nullable, meaning it can have no value, and does not have a default value assigned. This column is likely used to quantify specific metrics or indicators related to price changes and trends within the `ovs_fact_prices_aei` table, contributing to the overall analysis of commodity pricing dynamics."}, {"column_name": "prev_day_point", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'prev_day_point':** \n\nThe `prev_day_point` column stores the price value (as a double precision number) of a commodity from the previous day. This column is nullable and does not have a default value, allowing for the possibility of missing data in cases where the prior day's price is not available. It contributes to the analysis of daily price changes and trends within the `ovs_fact_prices_aei` table."}, {"column_name": "dod_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'dod_change':** \n\nThe 'dod_change' column represents the change in price for a commodity on a day-over-day basis, stored as a double precision value. This column is nullable, indicating that it may not always have a value, and it does not have a default value assigned. It is utilized to analyze short-term price fluctuations in the context of the broader price-related data captured in the 'ovs_fact_prices_aei' table."}, {"column_name": "prev_week_point", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `prev_week_point`**  \nRepresents the price point of a commodity from the previous week, stored as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in data entry when previous week's data is unavailable."}, {"column_name": "wow_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'wow_change':** \n\nThe 'wow_change' column represents the week-over-week change in price for the commodities recorded in the `ovs_fact_prices_aei` table. It is of type double precision, allowing for precise numerical values, and can be null if no change is recorded. This metric is essential for analyzing short-term price fluctuations and trends."}, {"column_name": "week_start", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`week_start`: A nullable double precision column in the `ovs_fact_prices_aei` table that represents the starting point of the week for price data aggregation. This column facilitates the analysis of weekly price trends and comparisons within the context of commodity pricing over time."}, {"column_name": "week_end", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'week_end':** \n\nThe 'week_end' column stores the ending date of the week as a double precision value, representing the weekly time frame for which price data is recorded. This column is nullable and does not have a default value, allowing for flexibility in indicating weeks without associated price data in the `ovs_fact_prices_aei` table."}, {"column_name": "season_start", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'season_start':**  \nThe 'season_start' column represents the starting point of a specific season, stored as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in capturing seasonal pricing data within the `ovs_fact_prices_aei` table. It aids in the analysis of price trends and changes related to different seasons for various commodities."}, {"column_name": "std_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `std_change`**  \nRepresents the standard change in price for commodities, measured as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in data entry. It is used to analyze volatility and variations in pricing over specified periods within the `ovs_fact_prices_aei` table."}, {"column_name": "year_start", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** \n\n`year_start`: Represents the starting year for the price data captured in the `ovs_fact_prices_aei` table. This column is of double precision type, allowing for the storage of numerical values that may include decimal points. It is nullable, indicating that it may contain null values if the starting year is not applicable or available for certain records."}, {"column_name": "ytd_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'ytd_change':**  \nThe 'ytd_change' column represents the year-to-date percentage change in the price of commodities, measured as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of missing data. It provides crucial insights into the performance of commodity prices over the current year, facilitating trend analysis and informed decision-making in market evaluations."}], "description": "The `ovs_fact_prices_aei` table captures price-related data for various commodities over time, including daily and weekly price changes, as well as seasonal and year-to-date metrics. It provides insights into market trends and price movements for effective analysis and decision-making."}, {"table_name": "ovs_fact_prices_commodities_prices", "columns": [{"column_name": "date", "data_type": "date", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'date':**  \nThe 'date' column stores the date for which the pricing data is recorded. This date is crucial for tracking historical price metrics and analyzing trends over time. It is of type date, can be nullable, and does not have a default value, allowing for flexibility in data entry while capturing specific price records for various commodities."}, {"column_name": "dow", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "description": "Column 'dow': Numeric representation of the day of the week (0-6), where 0 typically represents Sunday and 6 represents Saturday. This column is nullable and does not have a default value. It is used to analyze daily pricing trends and fluctuations within the historical pricing data of commodities."}, {"column_name": "woy", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\n**'woy'**: This column represents the \"week of the year\" (WOY) for the corresponding pricing data entry. It is stored as text and can be nullable, indicating that it may not always have a value. The 'woy' column is used to categorize and analyze historical commodity prices on a weekly basis, assisting in the evaluation of price trends and fluctuations over the course of the year."}, {"column_name": "season", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'season':** \n\nThe 'season' column stores the seasonal classification of commodity pricing data, allowing for the identification of price trends and fluctuations associated with specific seasons. This text field is nullable and does not have a default value, enabling flexibility in data entry for commodities that may not have a defined seasonal period."}, {"column_name": "commodity_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'commodity_code'**  \nThis column contains a text identifier for each commodity, allowing for the categorization and differentiation of various commodities within the historical pricing data. It is nullable and does not have a default value, enabling flexibility in data entry for commodities not specifically coded."}, {"column_name": "commodity", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'commodity'**  \nThis column stores the name or identifier of the commodity associated with the pricing data. It is of text data type, allows null values, and does not have a default value. This column is essential for distinguishing between different commodities in the historical pricing records maintained in the `ovs_fact_prices_commodities_prices` table."}, {"column_name": "closing_price_kg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'closing_price_kg':**  \nThe 'closing_price_kg' column represents the closing price per kilogram of various commodities, recorded as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in representing missing data. It is part of the `ovs_fact_prices_commodities_prices` table, which tracks historical pricing metrics and trends for commodities over time."}, {"column_name": "opening_price_kg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\nThe `opening_price_kg` column represents the price per kilogram of a commodity at the start of a given time period. This value is stored as a double precision number and may contain null entries, indicating that the opening price may not be available for certain records. It is part of the `ovs_fact_prices_commodities_prices` table, which captures historical pricing data for comprehensive analysis of commodity price trends and fluctuations."}, {"column_name": "max_price_kg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`max_price_kg`: This column represents the maximum price per kilogram recorded for a commodity, stored as a double precision value. It is nullable and does not have a default value. This metric is essential for analyzing the highest pricing trends of commodities over specified periods, contributing to the overall understanding of market fluctuations."}, {"column_name": "min_price_kg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'min_price_kg':**  \nRepresents the minimum price per kilogram for a commodity, recorded in double precision format. This column is nullable and does not have a default value, allowing for the possibility that minimum price data may not be available for certain records. It contributes to the analysis of historical price trends and fluctuations within the `ovs_fact_prices_commodities_prices` table."}, {"column_name": "dod_price_diff", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `dod_price_diff`**  \nThe `dod_price_diff` column represents the difference in price for a commodity from one day to the next. This double precision field is nullable, allowing for the absence of data when no prior day's price is available. It is used to analyze daily price fluctuations and trends within the historical pricing data of commodities stored in the `ovs_fact_prices_commodities_prices` table."}, {"column_name": "dod_price_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`dod_price_change`: Represents the change in price per kilogram of commodities on a day-over-day basis. This column is of double precision type and can contain null values. It does not have a default value, allowing for the recording of fluctuations in commodity prices over time for analysis of trends and price movements."}, {"column_name": "week_start_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`week_start_price`: Represents the price of a commodity at the beginning of the week, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of missing data. It is used to analyze weekly price trends and fluctuations in the context of historical pricing data for various commodities."}, {"column_name": "week_end_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `week_end_price`**: This column stores the closing price of a commodity at the end of the week, represented as a double precision value. It is nullable and does not have a default value. This data point is essential for analyzing weekly price trends and fluctuations in the commodities market."}, {"column_name": "previous_week_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `previous_week_price`**: This column represents the price of a commodity from the previous week, stored as a double precision value. It is nullable, meaning it may contain no value, and does not have a default value assigned. This data point is crucial for analyzing weekly price trends and fluctuations in the `ovs_fact_prices_commodities_prices` table."}, {"column_name": "wow_price_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `wow_price_change`**  \nThe `wow_price_change` column represents the change in commodity prices on a week-over-week basis. It is stored as a double precision value, allowing for decimal representation of price fluctuations. This column is nullable, indicating that there may be instances where week-over-week price change data is not available. It plays a crucial role in analyzing trends in commodity pricing over time."}, {"column_name": "season_start_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`season_start_price`: Represents the starting price of a commodity at the beginning of a specific season, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of missing data in the context of historical pricing analysis within the `ovs_fact_prices_commodities_prices` table."}, {"column_name": "std_price_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `std_price_change`**  \nRepresents the standard deviation of price changes for a commodity, measured in double precision. This column can be null and does not have a default value. It is used to analyze the volatility and variability of price fluctuations over a specified period, providing insights into the stability of commodity prices."}, {"column_name": "year_start_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'year_start_price'**: This column represents the starting price of a commodity for a given year, stored as a double precision value. It is nullable, indicating that it may not have a value for every entry. This data point is essential for analyzing annual pricing trends and fluctuations in the commodity market."}, {"column_name": "ytd_price_change", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`ytd_price_change`: A nullable double precision column that represents the year-to-date change in price for a commodity. It reflects the difference in price from the beginning of the year to the current date, providing insight into the overall price trend and fluctuations within the year. This column is part of the `ovs_fact_prices_commodities_prices` table, which captures comprehensive historical pricing data for commodities."}], "description": "The `ovs_fact_prices_commodities_prices` table stores historical pricing data for various commodities, tracking daily and weekly price metrics, seasonal trends, and price changes over time. It includes information on opening, closing, maximum, and minimum prices per kilogram, as well as differences and changes in prices from day to day and week to week. The table is designed to facilitate analysis of commodity price fluctuations and trends."}, {"table_name": "ovs_dim_security", "columns": [{"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':** A nullable bigint that serves as a unique identifier for each security in the `ovs_dim_security` table, enabling the organization and classification of financial instruments. No default value is assigned."}, {"column_name": "security_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_code':**  \nThe `security_code` column contains text values representing unique identifiers for securities. This column is nullable and has no default value, allowing for the possibility of missing data in the context of financial instruments stored in the `ovs_dim_security` table."}, {"column_name": "security_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_name':**  \nThe `security_name` column contains the names of various securities, represented as text. This column is nullable and does not have a default value, allowing for the possibility of missing entries. It plays a crucial role in identifying and distinguishing financial instruments within the `ovs_dim_security` table."}, {"column_name": "sec_security_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'sec_security_type':**  \nThis column represents the type of security associated with each entry in the `ovs_dim_security` table. It is a text field that can be null, allowing for flexibility in the classification of securities."}, {"column_name": "board_type_sec", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'board_type_sec':** \n\nThe `board_type_sec` column contains text information about the specific type of board associated with a security. This column is nullable and does not have a default value, allowing for flexibility in capturing various board types relevant to the classification of financial instruments within the `ovs_dim_security` table."}, {"column_name": "security_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_type':**  \nThe 'security_type' column stores the classification of the security, indicating the specific type of financial instrument (e.g., stock, bond, option) it represents. This text field is nullable and does not have a default value, allowing for the flexibility of unspecified types."}], "description": "The `ovs_dim_security` table stores detailed information about various securities, including their unique identifiers, codes, names, and types, facilitating the organization and classification of financial instruments."}, {"table_name": "tr_fact_clientwallet", "columns": [{"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':** \n\nThe 'id' column is a bigint that serves as a unique identifier for each record in the `tr_fact_clientwallet` table. It is nullable and does not have a default value, allowing for flexibility in record management. This column aids in distinguishing between different client wallets and is essential for tracking financial transactions and wallet statuses."}, {"column_name": "cid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'cid':**  \nThe 'cid' column is a text field that may contain client identification information related to wallet transactions. It is nullable, meaning it can have no value, and does not have a default value assigned. This column helps in associating financial records in the `tr_fact_clientwallet` table with specific clients, facilitating the management and tracking of client wallet activities."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created':**  \nThe 'created' column records the timestamp of when the wallet entry was created, with time zone information included. This column is nullable and does not have a default value, providing flexibility in tracking the creation time of records in the `tr_fact_clientwallet` table."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'updated':**  \nThe 'updated' column records the timestamp of the most recent modification to a client wallet entry. This column is of type timestamp with time zone, allows null values, and does not have a default value. It is crucial for tracking changes over time, ensuring accurate financial data management within the tr_fact_clientwallet table."}, {"column_name": "total_balance", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'total_balance':** \n\nRepresents the total monetary balance in a client's wallet, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of no balance being recorded for a wallet."}, {"column_name": "available_balance", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'available_balance':** \n\nRepresents the current available balance in the client wallet, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of no balance being recorded for certain clients."}, {"column_name": "lien_balance", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `lien_balance`**  \nThe `lien_balance` column represents the amount of funds that are currently subject to a lien within a client's wallet. It is stored as a double precision value, allowing for fractional amounts, and can be null if no lien is applicable. This column plays a crucial role in managing financial transactions and ensuring accurate reporting of available client funds in the `tr_fact_clientwallet` table."}, {"column_name": "cash_advance_limit", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `cash_advance_limit`**  \nRepresents the maximum amount of cash advance that can be issued to a client, stored as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in cases where a cash advance limit is not applicable."}, {"column_name": "cash_advance_spent", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `cash_advance_spent`**: This column represents the amount of cash advances that have been spent from the client wallet. It is stored as a double precision value and can be NULL, indicating that there may be cases where no cash advances have been recorded as spent."}, {"column_name": "cash_advance_balance", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `cash_advance_balance`**  \nThis column represents the current balance available for cash advances in the client's wallet. It is of type double precision, allowing for detailed financial calculations. The field is nullable, meaning it may not always contain a value, and has no default value assigned. This column contributes to the overall financial tracking within the `tr_fact_clientwallet` table, which manages client wallet information, including balances and cash advance details."}, {"column_name": "currency_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'currency_name':** \n\nThe 'currency_name' column holds the name of the currency associated with the client wallet. This text field is nullable and does not have a default value, allowing for flexibility in representing various currencies in the financial records of the `tr_fact_clientwallet` table."}, {"column_name": "currency_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'currency_code'**: This text column represents the currency code associated with the client's wallet in the `tr_fact_clientwallet` table. It is nullable and does not have a default value, allowing for flexibility in cases where the currency may not be specified."}, {"column_name": "oms_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'oms_name':**  \nThe `oms_name` column stores the name of the Operational Management System (OMS) associated with the client's wallet. This column is of type text, allows null values, and has no default value, providing flexibility in capturing the OMS information relevant to financial transactions and wallet management."}, {"column_name": "oms_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'oms_code':** \n\nThe `oms_code` column stores an optional text identifier related to the operational management system associated with the client wallet. It may provide additional context or categorization for financial transactions recorded in the `tr_fact_clientwallet` table."}, {"column_name": "is_deleted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_deleted':**  \nIndicates whether the wallet record is marked as deleted. A boolean value where true signifies that the record is considered deleted, while false indicates it is active. This column is nullable and does not have a default value, allowing for flexibility in record management within the `tr_fact_clientwallet` table."}], "description": "The `tr_fact_clientwallet` table stores financial information related to client wallets, including balances, cash advance details, and metadata for currency and operational management systems. It tracks the creation and update timestamps of each record, while also indicating the status of the wallet through a deletion flag."}, {"table_name": "tr_fact_matchedorder", "columns": [{"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'created'**  \nThe 'created' column records the timestamp of when a matched order was created, including the time zone information. This column is nullable, allowing for the possibility that some records may not have a specified creation time. It serves as a critical data point for tracking the lifecycle of matched orders within the trading system."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'updated':**  \nThe 'updated' column records the timestamp of the last update made to a matched order in the trading system. It is of type timestamp with time zone, allowing for accurate time tracking across different time zones. This column is nullable, indicating that an update may not have occurred for some orders."}, {"column_name": "processed_on", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'processed_on':**  \nThe 'processed_on' column records the timestamp (with time zone) when a matched order was processed. This column is nullable and does not have a default value, allowing for the possibility that some orders may not have a recorded processing time. It is essential for tracking the timing of order processing within the trading system."}, {"column_name": "tid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'tid':**  \nThe 'tid' column stores a unique identifier for each transaction within the matched orders, represented as text. It is nullable and does not have a default value, allowing for flexibility in recording transactions. This identifier is crucial for tracking and referencing specific matched orders within the trading system."}, {"column_name": "matched_id", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'matched_id':**  \nThe `matched_id` column stores a text identifier associated with matched orders in the trading system. It is designed to link specific matched transactions to their corresponding details within the `tr_fact_matchedorder` table. This column is nullable, allowing for the possibility of unmatched or untracked orders, and does not have a default value."}, {"column_name": "buy_tid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'buy_tid':**  \nThe `buy_tid` column stores the text identifier for the buyer associated with a matched order in the `tr_fact_matchedorder` table. It is nullable, meaning it can contain empty values, and has no default value. This column is essential for linking orders to their respective buyers within the trading system's transaction records."}, {"column_name": "sell_tid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'sell_tid':**  \nThe 'sell_tid' column stores the text identifier for the seller associated with a matched order in the `tr_fact_matchedorder` table. This column is nullable and does not have a default value, allowing for the possibility that some matched orders may not have a corresponding seller identifier recorded."}, {"column_name": "buyer_cid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `buyer_cid`**  \nThis column stores the unique identifier (CID) for the buyer associated with a matched order. It is of type text and is nullable, meaning it may not always contain a value. The `buyer_cid` helps in linking the order to the corresponding buyer within the trading system, facilitating detailed tracking and auditing of transactions."}, {"column_name": "seller_cid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'seller_cid':** \n\nThe 'seller_cid' column stores the unique identifier for the seller associated with a matched order. It is of text data type, allows null values, and does not have a default value. This information is crucial for linking transactions to specific sellers within the trading system."}, {"column_name": "order_units", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\n`order_units` (bigint, nullable) - This column represents the quantity of units associated with a matched order in the trading system. It may contain null values if no units are specified for the order."}, {"column_name": "order_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'order_price':** \n\nThe 'order_price' column represents the price at which a matched order is executed in the trading system. It is of type double precision, allowing for precise representation of monetary values. This column is nullable, indicating that there may be instances where the order price is not applicable or has not been recorded."}, {"column_name": "matched_units", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'matched_units':** \n\nThe `matched_units` column represents the total number of units associated with a matched order in the trading system. It is of type `bigint`, allowing for the storage of large numerical values, and it may contain null values, indicating that the matched units may not always be specified for every order. This column is crucial for understanding the volume of trades that have been executed and is integral to the overall record-keeping of matched transactions within the `tr_fact_matchedorder` table."}, {"column_name": "matched_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `matched_price`**  \nRepresents the price at which an order was matched in the trading system. This column is of type double precision and can contain null values, indicating that a matched price may not always be available for every transaction. It plays a crucial role in analyzing the financial aspects of matched orders within the `tr_fact_matchedorder` table."}, {"column_name": "invoiced_units", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `invoiced_units`**  \nRepresents the number of units that have been invoiced for a matched order. This column is of type bigint, allowing for a large range of values, and it is nullable, indicating that there may be instances where invoiced units are not applicable or recorded."}, {"column_name": "volume_per_unit", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`volume_per_unit`: Represents the volume of assets or goods for each unit in a matched order. This column is of type bigint and can contain null values, indicating that the volume may not always be applicable or available for every order entry."}, {"column_name": "calc_volume_per_unit", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'calc_volume_per_unit':** \n\nThe 'calc_volume_per_unit' column represents the calculated volume of a trading order per unit, stored as a bigint. This column is nullable and does not have a default value. It serves to provide insights into the volume dynamics of matched orders within the `tr_fact_matchedorder` table, which captures comprehensive details about trading transactions, including order specifics and processing statuses."}, {"column_name": "security_location_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `security_location_code`**  \nThis column stores a text identifier representing the location of the security associated with a matched order. It is nullable, allowing for the absence of a value, and does not have a default value. The `security_location_code` aids in categorizing and tracking the physical or virtual location of the security within the trading system."}, {"column_name": "order_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'order_type':**  \nThe 'order_type' column specifies the category or nature of the matched order within the trading system. It is of type text and can be left empty (nullable). This field helps in classifying orders, enabling better analysis and reporting of trading activities."}, {"column_name": "board_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'board_type':**  \nThe 'board_type' column stores the classification of the trading board associated with the matched order. This text field is nullable and does not have a default value, allowing for flexibility in capturing various board types relevant to the trading system."}, {"column_name": "security_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_name':**  \nThe 'security_name' column stores the name of the financial instrument or security associated with the matched order. It is of text data type, can be left empty (nullable), and does not have a default value. This column provides essential context for identifying the specific asset involved in the transaction recorded in the `tr_fact_matchedorder` table."}, {"column_name": "security_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `security_code`**: This column stores the unique identifier for the security associated with a matched order. It is of text data type, allows null values, and has no default value. The `security_code` helps in categorizing and identifying different financial instruments within the trading system, enhancing the traceability and management of matched transactions in the `tr_fact_matchedorder` table."}, {"column_name": "security_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_type':**  \nThe `security_type` column specifies the category of the security associated with each matched order, indicating whether it is a stock, bond, option, or other financial instrument. This column is of text data type, allows null values, and has no default value, providing flexibility in recording various security classifications within the trading system."}, {"column_name": "community", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'community'**  \nThe 'community' column stores text-based information related to the community context of matched orders, which may include identifiers or names of trading groups or market segments. This column is nullable and does not have a default value, indicating that community information may not always be applicable or available for every order record."}, {"column_name": "brokerage_community_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\nThe `brokerage_community_name` column contains the name of the brokerage community associated with a matched order. It is of type text, allowing for variable-length strings, and can be null if no community is specified. This column helps in identifying the community context of the trading transaction, contributing to a more detailed understanding of the order's background within the trading system."}, {"column_name": "brokerage_community_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`brokerage_community_code` is a text column that optionally stores the code representing the brokerage community associated with a matched order. This column may contain NULL values and does not have a default value, allowing for flexibility in recording community affiliations within the context of trading transactions."}, {"column_name": "promoter_community_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `promoter_community_name`**  \nThis column stores the name of the community associated with the promoter of the matched order. It is of type text, allows null values, and does not have a default value. It provides contextual information that links the order to a specific promotional community, enhancing the details captured in the trading system."}, {"column_name": "promoter_community_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n`promoter_community_code`: This text column optionally records the community code associated with the promoter of the matched order in the `tr_fact_matchedorder` table. It is used to provide context about the trading community linked to the order, enhancing the details captured regarding matched transactions."}, {"column_name": "oms_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'oms_name':** \n\nThe 'oms_name' column stores the name of the Order Management System (OMS) associated with a matched order. It is of type text and can contain null values, indicating that the OMS name is optional for certain records. This column aids in identifying the specific trading system used for order processing within the `tr_fact_matchedorder` table."}, {"column_name": "oms_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'oms_code':**  \nThe `oms_code` column is a text field that optionally stores the order management system (OMS) identifier associated with a matched order in the `tr_fact_matchedorder` table. It provides a reference for tracking and managing orders within the trading system, although it is not required for every entry."}, {"column_name": "is_on_behalf", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_on_behalf':** \n\nIndicates whether the matched order was placed on behalf of another party. This boolean column is nullable and does not have a default value, allowing it to capture scenarios where the order placement context is either specified or left unspecified."}, {"column_name": "comx_created_by_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`comx_created_by_id` (bigint, nullable) - This column stores the identifier of the user or system that created the matched order entry in the `tr_fact_matchedorder` table. It allows for tracking of the origin of the record, which is useful for auditing and accountability within the trading system."}, {"column_name": "total_order_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\n`total_order_price`: This column represents the total price of matched orders in the trading system, expressed as a double precision value. It may contain null values and does not have a default value."}, {"column_name": "total_order_price_with_fees", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`total_order_price_with_fees` - This column represents the total price of an order, inclusive of all applicable fees, recorded as a double precision value. It is nullable, meaning it may not always contain a value. This data is essential for understanding the complete financial impact of matched orders within the trading system."}, {"column_name": "total_afex_fees", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `total_afex_fees`**  \nRepresents the total fees associated with AFEX transactions for a matched order, stored as a double precision value. This column is nullable, indicating that it may not always contain a value, and has no default value assigned."}, {"column_name": "total_oms_fees", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`total_oms_fees`: A nullable double precision field representing the total fees associated with matched orders in the trading system. It captures the cumulative order management system (OMS) fees applicable to transactions recorded in the `tr_fact_matchedorder` table."}, {"column_name": "sec_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'sec_fee'**  \nThe 'sec_fee' column represents the security fee associated with a matched order in the trading system. It is stored as a double precision value and is nullable, indicating that it may not always have a value. This fee provides insight into the costs incurred for trading securities and is essential for calculating the overall financial impact of the order."}, {"column_name": "exchange_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `exchange_fee`**: Represents the fee charged by the exchange for processing a matched order. This value is stored as a double precision number and can be null, indicating that no fee may be applicable for certain transactions."}, {"column_name": "cm_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'cm_fee':** \n\nThe `cm_fee` column represents the commission or fee associated with a matched order in the trading system, recorded as a double precision floating-point number. It is nullable, indicating that it may not always have a value. This column is essential for understanding the costs incurred during the transaction process within the `tr_fact_matchedorder` table."}, {"column_name": "brokerage_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `brokerage_fee`**  \nRepresents the fee charged by the brokerage for processing a matched order. This value is stored as a double precision floating-point number and may be NULL if not applicable."}, {"column_name": "vat_value", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'vat_value':** \n\nThe `vat_value` column stores the value of Value Added Tax (VAT) associated with a matched order in the `tr_fact_matchedorder` table. This column is of type double precision, allowing for precise representation of VAT amounts. It is nullable, indicating that VAT may not always be applicable or provided for every order. There is no default value assigned to this column."}, {"column_name": "location_breakdown", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `location_breakdown`**  \nA text field that provides detailed information about the geographical distribution or segmentation of matched orders within the trading system. This column is nullable and has no default value, allowing for flexibility in capturing varying levels of location detail for each order."}, {"column_name": "is_contract_note_sent", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_contract_note_sent':** \n\nIndicates whether a contract note has been sent for the matched order. This boolean field can be null and does not have a default value, providing flexibility in tracking the status of contract note dispatches within the context of the matched order records."}, {"column_name": "is_audit_cancelled", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`is_audit_cancelled`: A boolean flag indicating whether the audit for the matched order has been cancelled. This column is nullable and does not have a default value, allowing for the possibility that the audit status may not be specified."}, {"column_name": "processed_for_inventory", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'processed_for_inventory':**  \nIndicates whether the matched order has been processed for inventory purposes. This boolean column can be NULL, reflecting that the processing status may not be applicable or known for all entries."}, {"column_name": "is_deleted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'is_deleted'**: A boolean flag indicating whether the matched order has been marked as deleted. This column is nullable and does not have a default value, allowing for the representation of both active and deleted orders within the trading system."}], "description": "The `tr_fact_matchedorder` table captures detailed information about matched orders in a trading system, including timestamps for creation, updates, and processing, as well as identifiers for buyers and sellers. It records the specifics of the orders such as units, prices, fees, and related community information, while also tracking whether certain actions have been completed, like contract note dispatch and inventory processing. Additionally, it includes flags for auditing and deletion status, providing a comprehensive view of matched trading transactions."}, {"table_name": "wb_dim_client", "columns": [{"column_name": "_airbyte_unique_key", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':**  \nThe 'id' column is a nullable bigint that serves as a unique identifier for each client in the `wb_dim_client` table. It is designed to facilitate the tracking and management of client records, although it does not have a default value assigned."}, {"column_name": "bvn", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\n**bvn**: This column stores the Bank Verification Number (BVN) of the client as a text value. It is nullable, indicating that not all clients may have a BVN recorded. This field is used to facilitate identification and verification processes within the client management system."}, {"column_name": "cid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'cid':** \n\nThe 'cid' column stores a unique client identifier in text format. It is nullable and has no default value. This column is used to associate records with specific clients in the `wb_dim_client` table, which contains comprehensive details about client identification, contact information, and activity tracking."}, {"column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "Column Description: The 'name' column in the `wb_dim_client` table contains the textual representation of the client's name. It is an optional field, meaning it can contain null values, and has no default value assigned. This column is essential for identifying clients and may include variations of their names for record-keeping and client management purposes."}, {"column_name": "email", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'email'**  \nThe 'email' column stores the email address of the client as a text value. It is nullable, meaning that it can contain no value for some records, and does not have a default value. This column is used to facilitate communication and identification of clients within the `wb_dim_client` table, which maintains comprehensive client information and activity tracking."}, {"column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'phone':** A text field that stores the client's phone number. This column is nullable, indicating that it may not contain a value for all records. There is no default value assigned."}, {"column_name": "address", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'address':**  \nThe 'address' column contains the client's physical address in text format. It is nullable, allowing for instances where an address may not be provided, and has no default value. This information is essential for client identification and communication within the `wb_dim_client` table."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\nThe 'created' column records the timestamp indicating when a client entry was created in the `wb_dim_client` table. It is stored with time zone information, allowing for accurate tracking of creation times across different regions. This column is nullable and has no default value, reflecting that the creation time may not always be provided."}, {"column_name": "id_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id_type':** \n\nThe 'id_type' column specifies the type of identification associated with the client, such as passport, driver's license, or national ID. This column is of text data type, allows NULL values, and has no default value, enabling flexible identification categorization for clients in the `wb_dim_client` table."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'updated':** \n\nThe 'updated' column records the timestamp of the most recent update made to the client information in the `wb_dim_client` table. It is of type timestamp with time zone, allowing for accurate tracking of updates across different time zones. This column is nullable, indicating that there may be instances where no update has been recorded."}, {"column_name": "user_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'user_id':**  \nThe 'user_id' column is a nullable bigint that uniquely identifies each client within the `wb_dim_client` table. It serves as a reference for client-related data, facilitating the tracking of client activity and verification statuses."}, {"column_name": "temp_cid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'temp_cid':**  \nThe 'temp_cid' column is a text field that may contain temporary client identification information. It is nullable, allowing for the absence of data, and does not have a default value. This column is used to facilitate the tracking or management of client records within the `wb_dim_client` table."}, {"column_name": "contacted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'contacted':** \n\nIndicates whether the client has been contacted. This boolean column can be set to true or false, and it is nullable, allowing for the possibility that this information may not be available."}, {"column_name": "id_number", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id_number':**  \nThe `id_number` column stores a unique identifier for each client in the `wb_dim_client` table. It is of type text, allows null values, and does not have a default value. This column is used to capture personal identification details, which may include government-issued IDs or other forms of identification relevant to client verification and tracking."}, {"column_name": "id_status", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id_status':** \n\nThe 'id_status' column stores the status identifier of a client in the `wb_dim_client` table. This text field is nullable and does not have a default value, allowing for flexibility in tracking various client statuses, including activity and verification states."}, {"column_name": "is_active", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_active':** \n\nIndicates whether the client is currently active. This boolean field can hold a value of true (active) or false (inactive) and is nullable, meaning it may not always have a specified value."}, {"column_name": "is_deleted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_deleted':** \n\nIndicates whether a client record has been marked as deleted. This boolean column can be used to filter out inactive or removed clients without physically deleting their records from the database. It is nullable, allowing for the representation of unknown or unspecified deletion status."}, {"column_name": "language_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`language_id`: A bigint value that optionally identifies the preferred language of the client. This column can be null and does not have a default value, allowing for cases where language preference is not specified."}, {"column_name": "company_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'company_type':**  \nThe `company_type` column specifies the classification of the client as a company, indicating the type of business entity (e.g., corporation, partnership, sole proprietorship). This text field is optional and may be left empty, with no default value assigned."}, {"column_name": "country_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'country_code':** \n\nThe 'country_code' column represents the country code associated with each client in the `wb_dim_client` table. It is a text field that can contain null values, indicating that a country code may not always be specified for a client. This column is used for identification and categorization of clients based on their geographical location."}, {"column_name": "matched_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'matched_name':** \n\nThe `matched_name` column stores an optional text field representing a client's name that has been matched or verified against external data sources. It may be used to facilitate data integration and ensure consistency in client identification. This field is nullable and has no default value."}, {"column_name": "was_restored", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'was_restored':** \n\nIndicates whether the client record has been restored from a previously inactive or deleted state. This boolean column is nullable, allowing for the absence of a value, and does not have a default value."}, {"column_name": "created_by_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created_by_id':**  \nThe 'created_by_id' column is a bigint data type that stores the identifier of the user or system responsible for creating the client record. This column is nullable and does not have a default value, allowing for flexibility in instances where the creator's information may not be available."}, {"column_name": "date_restored", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'date_restored':** \n\nThe 'date_restored' column records the timestamp of when a client’s status was restored, capturing the exact date and time with time zone information. This column is nullable and does not have a default value, indicating that it may not always be populated if a restoration event has not occurred."}, {"column_name": "phone_invalid", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'phone_invalid':**  \nIndicates whether the client's phone number is invalid. This boolean column can be null and does not have a default value, allowing for flexibility in representing the validity status of the contact information for each client in the `wb_dim_client` table."}, {"column_name": "is_id_verified", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_id_verified':**  \nIndicates whether the client's identification has been verified. This boolean field can be NULL, reflecting cases where verification status is not applicable or has not been determined."}, {"column_name": "is_phone_verified", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_phone_verified':**  \nIndicates whether the client's phone number has been verified. This boolean column can be null, reflecting that the verification status is not applicable or has not been determined for certain clients."}, {"column_name": "is_tenant_default", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_tenant_default':**  \nIndicates whether the client is set as the default tenant for the associated services or properties. This boolean column can be null, allowing for the possibility that no default tenant is specified."}, {"column_name": "last_verify_attempt", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `last_verify_attempt`**  \nThis column records the timestamp of the most recent verification attempt for the client, indicating when the last check for client verification status occurred. It is stored with time zone information and is nullable, meaning it can be left empty if no verification attempts have been made."}, {"column_name": "logistic_officer_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`logistic_officer_id` (bigint, nullable) - This column represents the unique identifier for the logistics officer assigned to a client. It may be null if no officer is assigned, allowing for flexibility in client management within the `wb_dim_client` table."}, {"column_name": "phone_number_status", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** The `phone_number_status` column stores the status of a client's phone number, indicating whether it is verified, unverified, or invalid. This column is of text data type, allows null values, and has no default value, providing flexibility in capturing the phone number verification status for clients in the `wb_dim_client` table."}, {"column_name": "inventory_settings_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** \n\n`inventory_settings_id`: This column stores a unique identifier for the inventory settings associated with each client. It is of type bigint, can be null, and does not have a default value. This identifier allows for the association of specific inventory configurations or preferences tied to individual clients in the `wb_dim_client` table."}, {"column_name": "name_matches_verified_id", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `name_matches_verified_id`**: A boolean flag indicating whether the client's name matches the verified identification records. This column is optional and may contain null values, reflecting that not all clients have undergone verification checks."}, {"column_name": "_airbyte_ab_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "_airbyte_emitted_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "_airbyte_normalized_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "_airbyte_crm_client_hashid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": ""}, {"column_name": "client_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'client_id':**  \nThe 'client_id' column is a bigint type that serves as a unique identifier for each client in the `wb_dim_client` table. This column is nullable, allowing for the possibility of missing values, and does not have a default value. It is essential for linking client information across various records within the table, facilitating data integration and management processes."}, {"column_name": "client_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `client_type`**  \nThis column represents the classification of the client, indicating the type or category to which the client belongs. It is of type text, can be left empty (nullable), and does not have a default value. This information is crucial for understanding client demographics and tailoring services accordingly."}], "description": "The `wb_dim_client` table stores detailed information about clients, including their personal identification details, contact information, and status attributes. It also tracks client activity and verification statuses, as well as metadata for data integration and management purposes."}, {"table_name": "wb_dim_clientbank", "columns": [{"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':**  \nThe 'id' column is a nullable bigint that serves as a unique identifier for each record in the `wb_dim_clientbank` table, which contains information about client bank accounts. It allows for the differentiation of records but does not have a default value."}, {"column_name": "bvn", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'bvn'**  \nThe 'bvn' (Bank Verification Number) column stores the text representation of the unique identification number assigned to a client's bank account. This column is nullable and has no default value, indicating that it may not always be provided. It is used for identification and verification purposes within the context of client bank account management in the `wb_dim_clientbank` table."}, {"column_name": "account_number", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\nThe `account_number` column stores the unique identification number associated with each client bank account. It is of type text, allows null values, and does not have a default value. This column is essential for identifying and linking bank accounts within the `wb_dim_clientbank` table, which contains comprehensive information about client banking details and their associated statuses."}, {"column_name": "bank_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'bank_code':**  \nThe `bank_code` column stores the unique identifier for the bank associated with a client’s bank account. This text field is optional (nullable) and does not have a default value, allowing for flexibility in cases where the bank code is not applicable or known."}, {"column_name": "bank_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `bank_name`**  \nThe `bank_name` column stores the name of the bank associated with the client's bank account. This column is of text data type, allows null values, and does not have a default value. It is part of the `wb_dim_clientbank` table, which contains various details related to client bank accounts."}, {"column_name": "bank_country", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'bank_country':** \n\nThe `bank_country` column stores the country associated with the client's bank account, represented as text. This field is nullable and does not have a default value, allowing for the possibility that some accounts may not have a specified country."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created':**  \nThe 'created' column records the timestamp of when the client bank account entry was created, stored as a timestamp with time zone. This column is nullable and does not have a default value, allowing for flexibility in tracking account creation dates."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'updated':**  \nThe 'updated' column records the timestamp of the most recent modification made to the client bank account information. It is stored with time zone awareness, allowing for accurate tracking of changes across different time zones. This column is nullable, indicating that it may not always have a value."}, {"column_name": "client_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'client_id':** \n\nThe `client_id` column is of type bigint and may contain null values. It serves as a reference identifier for clients associated with bank accounts in the `wb_dim_clientbank` table, linking bank account records to their respective clients."}, {"column_name": "tenant_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'tenant_id':** \n\nThe `tenant_id` column stores a bigint value representing the unique identifier for the tenant associated with the client bank account. This column is nullable and does not have a default value, allowing for the possibility of no associated tenant for certain bank accounts."}, {"column_name": "preferred", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'preferred':**  \nIndicates whether the bank account is the client's preferred account. This boolean column is nullable and does not have a default value, allowing for flexibility in specifying the preferred status of the account."}, {"column_name": "is_deleted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_deleted':**  \nThe 'is_deleted' column is a boolean flag that indicates whether a client bank account has been marked as deleted. A value of true signifies that the account is considered deleted, while false indicates it is active. This column is nullable, allowing for the possibility that the deletion status may not be defined."}, {"column_name": "is_approved", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_approved':** \n\nIndicates whether the client bank account has been approved. This boolean field can be null, meaning the approval status may not be set. A value of true signifies that the account is approved, while false indicates it is not approved."}, {"column_name": "is_rejected", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_rejected':**  \nIndicates whether the client bank account has been rejected. This boolean column can be NULL, signifying that the rejection status is unspecified. A value of true denotes that the account has been rejected, while false indicates it has not."}, {"column_name": "is_reverted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\n`is_reverted` (boolean, nullable) - Indicates whether the bank account status has been reverted to a previous state. A value of `true` signifies that the account has been reverted, while `false` indicates it has not. This column helps track changes in account approval or verification statuses within the `wb_dim_clientbank` table."}, {"column_name": "is_verified", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_verified':**  \nIndicates whether the client bank account has been verified. This boolean column can hold a value of true or false and is nullable, meaning it may not have a value assigned. A value of true signifies that the account has been successfully verified, while false indicates it has not."}, {"column_name": "is_bank_deleted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_bank_deleted':**  \nIndicates whether the bank account has been marked as deleted. This boolean value can be NULL, reflecting that the deletion status may not be specified. When set to true, it signifies that the bank account is no longer active or valid."}, {"column_name": "is_nominated", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_nominated':**  \nIndicates whether the client bank account is nominated for a specific purpose or status. This boolean field can be null, meaning it may not be applicable for all records, and it does not have a default value."}, {"column_name": "approval_date", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `approval_date`**  \nThe `approval_date` column records the timestamp when a client bank account was approved. It is of type timestamp with time zone and can be null, indicating that an account may not yet have an approval date. This column helps track the approval status of bank accounts within the `wb_dim_clientbank` table."}, {"column_name": "approval_done", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'approval_done':**  \nThe `approval_done` column indicates whether the approval process for a client bank account has been completed. It is of type text, can contain null values, and has no default value. This column helps track the approval status within the broader context of client bank account management."}, {"column_name": "created_by_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created_by_id':**  \nThe 'created_by_id' column is a bigint that optionally stores the identifier of the user or system that created the record in the `wb_dim_clientbank` table. This column helps in tracking the origin of each client bank account entry, facilitating auditing and accountability."}, {"column_name": "next_approval", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `next_approval`**  \nA text field that indicates the upcoming approval status or requirements for the client bank account. This column is nullable and does not have a default value, allowing for flexibility in tracking varying approval processes."}, {"column_name": "rejected_date", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`rejected_date`: This column records the timestamp when a bank account application was rejected, stored with time zone information. It is nullable, meaning that it may not always contain a value, and has no default value assigned."}, {"column_name": "revert_reason", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'revert_reason':** \n\nThe `revert_reason` column stores textual explanations for reverting changes made to a client bank account. This field is nullable and does not have a default value, allowing for flexibility in documenting specific reasons when applicable."}, {"column_name": "rejected_by_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `rejected_by_id`**: This column stores the identifier (bigint) of the user or system that rejected the bank account application. It is nullable and does not have a default value, indicating that the rejection may not always be applicable. This reference aids in tracking the approval process and accountability within the `wb_dim_clientbank` table."}, {"column_name": "created_offline", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`created_offline` (timestamp with time zone, Nullable) - This column records the timestamp of when the bank account was created offline, allowing for the tracking of bank accounts established outside of the online system. It is optional and does not have a default value."}, {"column_name": "rejection_reason", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`rejection_reason`: This text column records the reason for the rejection of a client bank account application. It is nullable, allowing for cases where no rejection has occurred, and has no default value."}], "description": "The `wb_dim_clientbank` table stores information related to client bank accounts, including identification details, bank information, and approval statuses. It tracks the creation and modification timestamps, as well as various flags indicating the account's state, such as approval, deletion, and verification. Additionally, it includes references to the client and tenant associated with each bank account."}, {"table_name": "wb_dim_farmer", "columns": [{"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created':**  \nThe 'created' column records the timestamp of when the farmer's profile was created, stored in UTC format. It is nullable and does not have a default value, allowing for flexibility in tracking the creation date of individual records within the `wb_dim_farmer` table."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'updated':**  \nThe 'updated' column records the timestamp of the last modification made to the farmer's profile in the `wb_dim_farmer` table. It is of type timestamp with time zone, allowing for the tracking of changes across different time zones. This column is nullable and does not have a default value, indicating that updates may not be applied to all records."}, {"column_name": "is_deleted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_deleted':**  \nIndicates whether the farmer record has been marked as deleted. A value of true signifies that the record is no longer active, while false indicates that it is still in use. This column is nullable, allowing for the absence of a deletion status."}, {"column_name": "folio_id", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'folio_id':**  \nThe `folio_id` column is a text field that optionally stores a unique identifier associated with a farmer's profile in the `wb_dim_farmer` table. This identifier may be used for tracking and referencing individual farmer records, but it is not mandatory, allowing for flexibility in the data entry process."}, {"column_name": "title", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'title':**  \nThe 'title' column stores the textual designation or title of the farmer, which may include designations such as \"Mr.\", \"Mrs.\", \"Dr.\", etc. This column is optional and can be left blank, as it is nullable and does not have a default value."}, {"column_name": "first_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'first_name':**  \nThe 'first_name' column stores the first names of farmers. It is of text data type, allows null values, and does not have a default value. This column is essential for identifying individual farmers within the `wb_dim_farmer` table, which contains comprehensive details about their profiles and related data."}, {"column_name": "last_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'last_name':**  \nThe 'last_name' column stores the last name of farmers as a text value. It is nullable, indicating that it may contain empty entries, and has no default value assigned. This column is essential for identifying and distinguishing between farmers in the `wb_dim_farmer` table, which consolidates various personal and demographic details relevant to agricultural management."}, {"column_name": "middle_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`middle_name`: A text field that optionally stores the middle name of the farmer. This column can be left empty and does not have a default value. It contributes to the personal identification details of farmers in the `wb_dim_farmer` table."}, {"column_name": "address", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\nThe 'address' column in the `wb_dim_farmer` table stores the text representation of the farmer's residential address. This field is nullable, meaning it can contain no value, and does not have a default value. It is used to provide additional context for the farmer's location within the agricultural system."}, {"column_name": "gender", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'gender':**  \nThe 'gender' column stores the gender identity of the farmer as a text value. It is nullable, allowing for the possibility that this information may not be provided. This column contributes to the demographic profiling of farmers within the `wb_dim_farmer` table, which contains comprehensive data for managing agricultural systems."}, {"column_name": "marital_status", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'marital_status':** \n\nThe 'marital_status' column stores the marital status of farmers, represented as text. This field is nullable and does not have a default value, allowing for the possibility that marital status may be unknown or not applicable. It contributes to the demographic information captured in the `wb_dim_farmer` table, aiding in understanding the social context of farmers within agricultural systems."}, {"column_name": "dob", "data_type": "date", "is_nullable": "YES", "column_default": null, "description": "Column Description: 'dob' (date, nullable) - Represents the date of birth of the farmer, providing demographic information for personal identification and age-related analysis."}, {"column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\nThe 'phone' column in the `wb_dim_farmer` table stores the contact phone number of the farmer. It is of type text, allows null values, and has no default value, enabling flexibility in capturing varying phone number formats or the absence of a phone number."}, {"column_name": "village", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'village':**  \nThe 'village' column stores the name of the village associated with the farmer. It is a text field that can be left empty (nullable) and has no default value. This information helps in identifying the geographical location of the farmer within the agricultural system."}, {"column_name": "farm_size", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`farm_size`: This column contains text descriptions of the size of the farm associated with each farmer. It is nullable, allowing for the possibility that some entries may not have specified farm sizes. There is no default value assigned."}, {"column_name": "passport_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'passport_type':** \n\nThe 'passport_type' column specifies the type of passport held by the farmer. It is a nullable text field that allows for the inclusion of various passport classifications, providing additional context for the farmer's identification and status."}, {"column_name": "passport_number", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'passport_number':** \n\nThe `passport_number` column stores the passport identification number of the farmer. It is of text data type and can be left empty (nullable), indicating that not all farmers may possess a passport. This column helps in uniquely identifying farmers for purposes related to personal identification and verification within the agricultural system."}, {"column_name": "nok_phone", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'nok_phone':**  \nThis column stores the contact phone number of the farmer's next of kin (NOK). It is of text data type, can be left empty (nullable), and does not have a default value."}, {"column_name": "nok_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'nok_name':**  \nThe 'nok_name' column stores the name of the farmer's next of kin. This text field is nullable and does not have a default value, allowing for flexibility in capturing familial information related to the farmer."}, {"column_name": "nok_relationship", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `nok_relationship`**: This column stores the relationship of the farmer to their next of kin (NOK), providing context for emergency contacts or family connections. It is of type text, allowing for flexible descriptions, and can be left empty if not applicable."}, {"column_name": "bvn", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'bvn':**  \nThe 'bvn' column stores the Bank Verification Number (BVN) of farmers as a text value. It is nullable and does not have a default value, indicating that not all farmers may have a BVN associated with their profile. This column aids in identifying farmers' banking information within the comprehensive dataset of the wb_dim_farmer table."}, {"column_name": "farm_coordinates", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`farm_coordinates`: This column stores the geographical coordinates of the farmer's farm in text format. It is nullable, allowing for instances where coordinates may not be available. This information is crucial for mapping and analyzing farm locations within the agricultural system."}, {"column_name": "farm_coordinates_polygon", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`farm_coordinates_polygon`: A text field representing the geographical boundary of a farmer's property as a polygon. This column is optional and may contain null values. It provides spatial information relevant to the farmer's location and is used for mapping and analysis within agricultural systems."}, {"column_name": "is_blacklist", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_blacklist':** \n\nIndicates whether a farmer is marked as a blacklist entry (true) or not (false). This column is nullable and has no default value, allowing for the possibility that the blacklist status of some farmers may be unknown."}, {"column_name": "languages", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'languages':**  \nThe 'languages' column contains text entries that specify the languages spoken by the farmers. This column is nullable, allowing for cases where language information may not be provided. It enhances the understanding of farmers' communication preferences and cultural backgrounds within the context of the wb_dim_farmer table."}, {"column_name": "client_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`client_id`: A bigint column that serves as a unique identifier for each farmer in the `wb_dim_farmer` table. It is nullable, allowing for the possibility of missing values, and does not have a default value. This identifier is crucial for linking the farmer's data with other related entities in the agricultural system."}, {"column_name": "warehouse_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'warehouse_id':** \n\nThe `warehouse_id` column is of type bigint and is nullable. It represents an optional identifier for the warehouse associated with a farmer in the `wb_dim_farmer` table. This column links farmers to specific warehouses, enabling the tracking of storage and distribution resources relevant to their agricultural operations."}, {"column_name": "crop_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** The `crop_name` column captures the name of the crop grown by the farmer. It is of text data type, allowing for flexible naming conventions, and is nullable, indicating that it may not be applicable for all entries. There is no default value assigned."}, {"column_name": "crop_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'crop_code':** \n\nThe `crop_code` column contains textual identifiers for various crops associated with farmers in the `wb_dim_farmer` table. This column is optional (nullable) and does not have a default value, allowing for flexibility in representing the types of crops cultivated by farmers."}, {"column_name": "feo_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'feo_name':**  \nThe 'feo_name' column stores the name of the farmer, represented as text. It is nullable, allowing for instances where the name may not be provided, and has no default value. This field is part of the `wb_dim_farmer` table, which contains comprehensive information about farmers, their demographics, and relationships within agricultural systems."}, {"column_name": "feo_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'feo_code':** \n\nA nullable text field representing the unique code associated with the farmer's financial engagement or status within the agricultural system. This code may link to financial records or cooperative affiliations, providing additional context to the farmer's profile in the `wb_dim_farmer` table."}, {"column_name": "feo_phone_number", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'feo_phone_number':**  \nA text field representing the phone number of the farmer, which may be null. This column captures contact information essential for communication and outreach efforts, supporting the management of farmer profiles within the `wb_dim_farmer` table."}, {"column_name": "cooperative_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`cooperative_id`: A bigint that optionally references the unique identifier of a cooperative associated with the farmer. This column allows for the establishment of relationships between farmers and their respective cooperatives, enhancing the management of agricultural data."}, {"column_name": "cooperative_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`cooperative_name`: A text field that optionally stores the name of the cooperative associated with the farmer. This column allows for the inclusion of cooperative affiliations, enhancing the relational context of the farmer's profile within the agricultural system."}, {"column_name": "cooperative_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'cooperative_code':**  \nThe `cooperative_code` column stores a text identifier representing the cooperative to which a farmer is affiliated. It is nullable, allowing for cases where a farmer may not belong to any cooperative. This column enhances the link between farmers and their respective cooperatives within the `wb_dim_farmer` table."}, {"column_name": "account_numbers", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'account_numbers':** \n\nThis column contains text-based identifiers for farmer accounts. It is nullable and does not have a default value, allowing for the possibility that some records may not have associated account numbers. This information is essential for linking farmers to their respective accounts within agricultural systems."}, {"column_name": "lga_of_origin", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`lga_of_origin`: This column stores the local government area (LGA) of origin for each farmer, represented as text. It is nullable, indicating that a value is not mandatory, and has no default value. This information helps in understanding the geographical distribution of farmers within the agricultural system."}, {"column_name": "lga_of_residence", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'lga_of_residence':**  \nThe `lga_of_residence` column stores the local government area (LGA) where the farmer resides. It is of text data type, can contain null values, and has no default value. This information is essential for demographic analysis and geographical mapping within the `wb_dim_farmer` table, which aggregates various data points related to farmers."}, {"column_name": "state_of_origin", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'state_of_origin'**  \nThis column stores the state of origin for the farmer, represented as text. It is nullable, allowing for instances where the state may not be provided. The information aids in demographic analysis and geographical profiling within the context of the `wb_dim_farmer` table, which manages comprehensive farmer data."}, {"column_name": "state_of_residence", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'state_of_residence':**  \nThe 'state_of_residence' column stores the name of the state where the farmer resides. It is a text field that can be left empty (nullable) and does not have a default value. This information is essential for demographic analysis and geographic profiling within the context of farmer data management."}, {"column_name": "tenant_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`tenant_id`: A nullable bigint that identifies the tenant associated with the farmer's record in the `wb_dim_farmer` table. This column allows for the association of farmers with specific tenants, facilitating data segmentation and management within agricultural systems."}, {"column_name": "phone_invalid", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'phone_invalid':**  \nIndicates whether the farmer's phone number is invalid. This boolean field can be null and helps to identify issues with the contact information provided for each farmer in the `wb_dim_farmer` table."}, {"column_name": "phone_number_status", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`phone_number_status`: This column indicates the status of the farmer's phone number, providing insights into its validity or current usage. It is of text data type, allows null values, and has no default value. This information aids in managing contact details within the broader context of farmer data."}, {"column_name": "coordinate_status", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `coordinate_status`**  \nIndicates the status of the geographical coordinates associated with a farmer’s profile. This text field is optional and may be left null, reflecting the availability or validity of the coordinates."}, {"column_name": "id_status", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id_status':** \n\nThe `id_status` column holds a text value that indicates the current status of the farmer within the agricultural system. It is nullable, meaning that it may not always have a value, and has no default value assigned. This column is essential for tracking the operational or administrative status of farmers in relation to their profiles in the `wb_dim_farmer` table."}, {"column_name": "location_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'location_id':** \n\nThe `location_id` column is a bigint that stores the identifier for the geographical location associated with each farmer in the `wb_dim_farmer` table. This column is nullable, allowing for instances where a farmer's location may not be specified. It serves to link farmers to their respective geographic data, facilitating the management of location-related information within agricultural systems."}, {"column_name": "warehouse_tenant_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`warehouse_tenant_id`: A bigint representing the identifier for the warehouse associated with the farmer. This column is nullable and does not have a default value, indicating that a relationship to a warehouse may not always be present for every farmer entry."}, {"column_name": "tenant_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `tenant_name`**: This column stores the name of the tenant associated with the farmer, represented as text. It is nullable, allowing for instances where a tenant name may not be applicable or provided. This field facilitates the identification of relationships between farmers and their respective tenants within the context of agricultural management."}, {"column_name": "warehouse_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'warehouse_name':**  \nThe 'warehouse_name' column stores the name of the warehouse associated with a farmer. It is a text field that can contain null values, indicating that some farmers may not be linked to a warehouse. This information helps in managing and organizing the relationship between farmers and their respective storage facilities within the agricultural system."}, {"column_name": "warehouse_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `warehouse_code`**: This column stores a text-based identifier for the warehouse associated with the farmer. It is nullable, allowing for cases where a farmer may not be linked to a specific warehouse. The `warehouse_code` facilitates the relationship between farmers and their respective storage facilities within the agricultural system."}, {"column_name": "capacity", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'capacity':** \n\nThe 'capacity' column represents the maximum potential output or production level associated with a farmer's operations, measured in a bigint format. This column is nullable, indicating that not all records may have a specified capacity, and it does not have a default value."}, {"column_name": "warehouse_manager_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`warehouse_manager_id`: This column stores the identifier (bigint) of the warehouse manager associated with the farmer. It is nullable, indicating that not all farmers may have an associated warehouse manager. This relationship helps in managing and organizing the logistics and support provided to farmers by warehouse personnel."}, {"column_name": "warehouse_address", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\nThe `warehouse_address` column stores the textual address of the warehouse associated with the farmer. This field is nullable, meaning it can contain no value, and does not have a default value. It is used to provide location details relevant to the farmer's operations and relationships within the agricultural system."}, {"column_name": "longitude", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'longitude'**  \nThe 'longitude' column stores the geographic longitudinal coordinates of farmers in double precision format. It is nullable, meaning that it can contain null values if the information is not available. This column is essential for mapping and analyzing the geographical distribution of farmers within the agricultural system."}, {"column_name": "latitude", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'latitude':**  \nRepresents the geographical latitude of the farmer's location, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility that some records may not have a specified latitude. It is essential for mapping and spatial analysis related to the farmers' profiles."}, {"column_name": "warehouse_email", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'warehouse_email':** \n\nThe 'warehouse_email' column contains the email address of the warehouse associated with a farmer. This field is of type text, may contain null values, and has no default value. It facilitates communication between farmers and warehouses, supporting efficient information exchange within the agricultural system."}, {"column_name": "location", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'location':**  \nThe 'location' column stores textual information indicating the geographical location of the farmer. This field is nullable, allowing for the absence of data, and has no default value. It is essential for linking farmers to their respective geographical areas and may include details such as city, state, or other relevant location descriptors."}, {"column_name": "state", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'state':**  \nThe 'state' column stores the name of the state associated with the farmer's residence or farm location. It is of text data type, allows null values, and does not have a default value. This information is essential for geographical categorization and demographic analysis within the `wb_dim_farmer` table."}, {"column_name": "capital", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'capital':**  \nThe 'capital' column stores textual information related to the financial resources or assets owned by the farmer. It is nullable, indicating that this information may not be available for all records, and has no default value. This column contributes to the overall understanding of the farmer's economic status within the `wb_dim_farmer` table."}, {"column_name": "region_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`region_code`: A text field that represents the geographical region associated with the farmer. This column is nullable and does not have a default value, allowing for flexibility in data entry when the region is not applicable or unknown."}, {"column_name": "region_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'region_name':**  \nThe 'region_name' column contains the name of the geographic region associated with the farmer. It is of text data type, allows null values, and does not have a default value. This column helps in categorizing farmers based on their location, facilitating better management and analysis of agricultural data within the wb_dim_farmer table."}, {"column_name": "country", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'country':**  \nThe 'country' column stores the name of the country where the farmer is located. It is of text data type, can contain null values, and has no default value. This information is crucial for understanding the geographical context of the farmer's data within agricultural systems."}, {"column_name": "country_capital", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `country_capital`**: This column contains the name of the capital city of the country where the farmer is located. It is of type text, allows null values, and has no default value. It is used to enhance the geographical context of the farmer's profile within the `wb_dim_farmer` table."}, {"column_name": "continent", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'continent':**  \nThis column stores the continent where the farmer is located. It is of type text, can contain null values, and does not have a default value. This information aids in geographic analysis and demographic categorization within the `wb_dim_farmer` table."}, {"column_name": "continent_subregion", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`continent_subregion` (text, nullable): This column represents the geographical subregion within a continent where the farmer operates. It provides additional context for the farmer's location, aiding in demographic analysis and regional categorization within the agricultural framework."}], "description": "The `wb_dim_farmer` table stores detailed information about farmers, including personal identification, contact details, demographic information, and farm-related data. It also encompasses relationships to cooperatives and warehouses, as well as geographical coordinates and status indicators relevant to the farmers' profiles. This table serves as a comprehensive resource for managing farmer data within agricultural systems."}, {"table_name": "wb_dim_item", "columns": [{"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':**  \nThe 'id' column is a bigint data type that uniquely identifies each item entry in the `wb_dim_item` table. It is nullable and does not have a default value, allowing for the possibility of missing identifiers in some records."}, {"column_name": "tenant_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'tenant_id':** \n\nThe `tenant_id` column is of type bigint and is nullable. It serves to associate each item in the `wb_dim_item` table with a specific tenant, allowing for multi-tenancy support in the dataset. This column does not have a default value, indicating that it may be left empty for certain items."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created':**  \nThe 'created' column stores the timestamp of when the item was initially created, including the time zone information. This column is nullable, meaning it may contain no value, and does not have a default value assigned."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'updated':**  \nThe 'updated' column stores the timestamp of the last modification made to the item, recorded with time zone information. It is nullable, meaning it can contain no value, and has no default value assigned. This column helps track changes in the `wb_dim_item` table, ensuring data integrity and providing a timeline for item updates."}, {"column_name": "grade_one_deduction", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'grade_one_deduction':** \n\nRepresents the amount deducted from the item's value based on grade criteria, stored as a double precision number. This column is optional and does not have a default value."}, {"column_name": "grade_two_deduction", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `grade_two_deduction`**  \nA nullable double precision value representing the deduction amount associated with grade two items in the `wb_dim_item` table. This column allows for customization of deductions based on grading criteria and does not have a default value."}, {"column_name": "grade_three_deduction", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'grade_three_deduction':** \n\nRepresents the deduction amount applied to items at grade three, stored as a double precision value. This column is nullable and does not have a default value."}, {"column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'name':**  \nThe 'name' column stores the textual representation of an item's name within the `wb_dim_item` table. It is nullable, allowing for entries without a specified name, and has no default value. This column is essential for identifying and describing items in the dimensional structure."}, {"column_name": "code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'code':**  \nA text field that optionally stores a unique identifier or classification code for items in the `wb_dim_item` table, used for distinguishing and categorizing items within the dimensional data structure. This column is nullable and does not have a default value."}, {"column_name": "product_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'product_type'**: This column stores the type of product associated with the item, represented as text. It is nullable, meaning that it may contain no value, and does not have a default value."}], "description": "The `wb_dim_item` table serves as a dimensional data structure for items, capturing essential attributes such as identification, tenant association, timestamps for creation and updates, deductions based on grading, and descriptive elements like name, code, and product type."}, {"table_name": "wb_dim_warehouse", "columns": [{"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':**  \nThe 'id' column is a nullable bigint that serves as a unique identifier for each warehouse entry in the `wb_dim_warehouse` table. It is used to distinguish individual warehouse records and facilitate relationships with other data entities, although it does not have a default value assigned."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created':** \n\nThe 'created' column records the timestamp of when the warehouse entry was created, including the associated time zone. This column is nullable and does not have a default value, allowing flexibility in tracking the creation time of warehouse records within the `wb_dim_warehouse` table."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'updated':** \n\nThe 'updated' column records the timestamp of the last modification made to the warehouse information. It is stored as a timestamp with time zone, allowing for accurate tracking of changes across different time zones. This column is nullable, meaning it can contain no value if no updates have occurred."}, {"column_name": "location_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`location_id`: A nullable bigint that represents the unique identifier for the geographic location associated with each warehouse in the `wb_dim_warehouse` table. This column links warehouses to their specific locations, facilitating the management of spatial and operational data within a tenant's infrastructure."}, {"column_name": "tenant_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'tenant_id':** \n\nThe `tenant_id` column is a bigint data type that represents the unique identifier for each tenant associated with a warehouse in the `wb_dim_warehouse` table. This column is nullable, indicating that it may not always have a value. It serves to establish a relationship between the warehouse and the tenant, facilitating the tracking of warehouse operations and management within a multi-tenant infrastructure."}, {"column_name": "tenant_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'tenant_name':** \n\nThe 'tenant_name' column stores the name of the tenant associated with a warehouse. This text field is nullable and does not have a default value, allowing for flexibility in instances where a tenant may not be specified. It is integral for identifying the ownership and operational context of the warehouse within the broader tenant infrastructure."}, {"column_name": "warehouse_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'warehouse_name':**  \nThe `warehouse_name` column stores the name of the warehouse. It is a text field that can accept null values and does not have a default value. This column is essential for identifying and referencing specific warehouses within the operational context of the tenant's infrastructure."}, {"column_name": "warehouse_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `warehouse_code`**: A text field representing the unique identifier for each warehouse in the `wb_dim_warehouse` table. This column is nullable and does not have a default value, allowing for the possibility of missing data. It is used to distinguish between different warehouses and is essential for linking warehouse-related information to operational and administrative contexts."}, {"column_name": "capacity", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'capacity':**  \nThe 'capacity' column represents the maximum storage capacity of the warehouse, measured in a numeric format (bigint). This column is nullable, indicating that the capacity may not be specified for every warehouse entry. It plays a crucial role in understanding the operational capabilities of each warehouse within the overall infrastructure."}, {"column_name": "warehouse_manager_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`warehouse_manager_id`: A bigint identifier for the manager responsible for the warehouse. This column is nullable, indicating that it may not always have a value, and does not have a default value. It links warehouse records to their respective management personnel, supporting organizational and operational oversight within the `wb_dim_warehouse` table."}, {"column_name": "address", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'address'**: This column stores the textual representation of the warehouse's physical location. It is nullable and does not have a default value, allowing for flexibility in cases where the address may not be applicable or available."}, {"column_name": "longitude", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`longitude`: A nullable double precision value representing the geographic longitude of the warehouse, used to specify its location within a tenant's infrastructure."}, {"column_name": "latitude", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'latitude':** \n\nThe 'latitude' column stores the geographic latitude of the warehouse as a double precision value. It is nullable, allowing for the possibility of missing data, and has no default value. This column facilitates the identification of the warehouse's precise location within its operational context."}, {"column_name": "warehouse_email", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `warehouse_email`**  \nThis column stores the email address associated with a warehouse. It is of type text and can be left empty (nullable), indicating that not all warehouses may have an associated email. The absence of a default value means that no predefined email will be assigned."}, {"column_name": "location", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'location':**  \nThe 'location' column stores textual information describing the geographical or administrative location of a warehouse. It is nullable, meaning it can hold no value, and has no default value assigned. This column is essential for understanding the operational context of each warehouse within the tenant's infrastructure."}, {"column_name": "state", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'state':**  \nThe 'state' column holds text data representing the geographical state where the warehouse is located. It is nullable, allowing for instances where the state may not be specified, and has no default value. This column is essential for understanding the regional context of each warehouse within the operational framework of the tenant's infrastructure."}, {"column_name": "capital", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'capital':**  \nThe 'capital' column stores textual information related to the financial resources or assets associated with each warehouse. It is nullable and does not have a default value, allowing for the representation of warehouses without specified capital details."}, {"column_name": "region_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`region_code`: This text column optionally identifies the geographical region associated with a warehouse. It can be used to categorize warehouses based on their location, providing insight into regional distribution and operational context. The column is nullable and does not have a default value, allowing for flexibility in data entry."}, {"column_name": "region_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Description for 'region_name':**  \nThe 'region_name' column stores the name of the geographical region associated with a warehouse. It is a text field that can be left empty (nullable) and does not have a default value. This column provides context for the warehouse's location within the broader operational structure of the tenant's infrastructure."}, {"column_name": "country", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'country':**  \nThe 'country' column stores the name of the country where the warehouse is located. It is of type text, allows null values, and has no default value, enabling flexibility in capturing geographic information for each warehouse in the `wb_dim_warehouse` table."}, {"column_name": "country_capital", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'country_capital':**  \nThe `country_capital` column stores the name of the capital city associated with a country. It is of text data type, allows null values, and does not have a default value. This column is used to provide geographical context within the `wb_dim_warehouse` table, enhancing the understanding of a warehouse's location in relation to its country's administrative center."}, {"column_name": "continent", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'continent':**  \nThe 'continent' column stores the name of the continent where the warehouse is located. It is a text field that can be left empty (nullable) and does not have a default value. This information enhances the geographical context of each warehouse within the `wb_dim_warehouse` table."}, {"column_name": "continent_subregion", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'continent_subregion':**  \nThis column stores the subregion within a continent where the warehouse is located. It is of text data type and may contain null values, indicating that the subregion information is optional. This attribute enhances the geographical context of the warehouse within the broader framework of the `wb_dim_warehouse` table."}], "description": "The `wb_dim_warehouse` table stores information about warehouses, including their identifiers, management details, location attributes, and capacity. It is designed to capture metadata related to each warehouse's operational context within a tenant's infrastructure, encompassing geographical and administrative data."}, {"table_name": "wb_fact_grn", "columns": [{"column_name": "cid", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'cid':**  \nThe 'cid' column stores an optional text identifier related to the goods receipt notes in the `wb_fact_grn` table. It may represent a client ID, category ID, or another relevant classification, aiding in the organization and retrieval of transaction-related data within the inventory and financial management system."}, {"column_name": "bags", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'bags':**  \nThe 'bags' column represents the quantity of bags associated with each goods receipt note in the `wb_fact_grn` table. It is of type bigint, allowing for large numerical values, and can be NULL if no quantity is specified. This column aids in tracking inventory levels and logistics related to the received goods."}, {"column_name": "grade", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'grade':** \n\nThe 'grade' column stores text values that may provide qualitative assessments or classifications of goods associated with receipt notes. This column is nullable and has no default value, allowing for flexibility in the documentation of item quality or condition within the `wb_fact_grn` table."}, {"column_name": "grn_id", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'grn_id':**  \nThe 'grn_id' column stores a unique identifier for each goods receipt note in the `wb_fact_grn` table. This text field is nullable and does not have a default value, allowing for flexibility in data entry when a specific goods receipt note ID is not applicable."}, {"column_name": "receipt_id", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'receipt_id':**  \nThe `receipt_id` column stores a unique identifier for each goods receipt note in the `wb_fact_grn` table. This text-based field is nullable and does not have a default value, allowing for flexibility in recording receipt information related to inventory and financial transactions."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created':**  \nThe 'created' column stores the timestamp of when the goods receipt note was created, including time zone information. This column is nullable, indicating that it may not always contain a value, and does not have a default value. It plays a crucial role in tracking the creation timeline of inventory and financial transactions within the `wb_fact_grn` table."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`updated`: This column records the timestamp of the last update made to the goods receipt note, stored with time zone information. It is nullable and does not have a default value, allowing for flexibility in tracking modifications when applicable."}, {"column_name": "gross_weight", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'gross_weight':** \n\nThe 'gross_weight' column represents the total weight of goods received, measured in double precision. It is nullable and does not have a default value, allowing for flexibility in recording instances where weight may not be applicable or available. This column is essential for accurately tracking inventory and managing logistics related to goods receipt notes in the `wb_fact_grn` table."}, {"column_name": "net_weight", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`net_weight`: A nullable double precision value representing the net weight of goods recorded in the goods receipt notes. This column captures the weight of the items after accounting for packaging and other non-product elements, crucial for inventory management and transaction accuracy in the `wb_fact_grn` table."}, {"column_name": "deduction", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'deduction':** \n\nThe 'deduction' column represents a double precision value that may contain adjustments or reductions in the financial aspects of goods receipt notes within the `wb_fact_grn` table. This column is nullable, indicating that it is not mandatory for every record, and does not have a default value. It is used to account for any deductions related to pricing or adjustments that may affect the overall transaction value."}, {"column_name": "total_deduction", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'total_deduction':**  \nThe 'total_deduction' column represents the total amount deducted from the transaction in the context of goods receipt notes. It is stored as a double precision value, allowing for precise financial calculations. This column is nullable, indicating that it may not always have a value, and does not have a default value assigned."}, {"column_name": "moisture", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'moisture':**  \nThe 'moisture' column stores the moisture content of items recorded in the `wb_fact_grn` table as a double precision value. This column is nullable, allowing for the absence of moisture data, and does not have a default value. It contributes to the detailed tracking of item specifics related to goods receipt notes, aiding in inventory management and quality assessment."}, {"column_name": "total_commodity_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`total_commodity_price`: A nullable double precision field representing the total price of commodities associated with a goods receipt note. This column captures the financial value of the items received, contributing to inventory and financial transaction management within the `wb_fact_grn` table."}, {"column_name": "price_per_tonne", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`price_per_tonne`: Represents the cost of goods per tonne, recorded as a double precision number. This column can contain null values and does not have a default value. It is used to provide detailed pricing information associated with goods receipt notes in the `wb_fact_grn` table, aiding in financial analysis and inventory management."}, {"column_name": "transaction_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`transaction_type`: This column captures the type of transaction associated with each goods receipt note in the `wb_fact_grn` table. It is of text data type, allowing for flexible categorization of transaction types, and is nullable, meaning it may not always contain a value."}, {"column_name": "approval_permissions", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `approval_permissions`**  \nThis column stores text-based information regarding the permissions related to the approval process for goods receipt notes. It is nullable and does not have a default value, allowing for flexibility in indicating specific approval rights or requirements associated with each transaction entry in the `wb_fact_grn` table."}, {"column_name": "approval_done", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'approval_done':**  \nThe 'approval_done' column records the status of approval for goods receipt notes within the `wb_fact_grn` table. It is of type text, allows null values, and does not have a default value. This column helps track whether the approval process for a specific transaction has been completed."}, {"column_name": "is_approved", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_approved':** \n\nIndicates whether the goods receipt note has been approved. This boolean column can hold a value of true or false and is nullable, allowing for the possibility of an unknown approval status."}, {"column_name": "is_approval_completed", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\n`is_approval_completed`: A boolean column indicating whether the approval process for the goods receipt note has been completed. This field is nullable and does not have a default value, allowing for flexibility in tracking the approval status within the `wb_fact_grn` table."}, {"column_name": "approval_date", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'approval_date':** \n\nThe `approval_date` column records the timestamp when a goods receipt note is approved. It is stored in the 'timestamp with time zone' format, allowing for accurate tracking across different time zones. This column is nullable, indicating that approval may not always be applicable, and it does not have a default value."}, {"column_name": "is_received_at_warehouse", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`is_received_at_warehouse`: A boolean flag indicating whether the goods have been received at the warehouse. This column is nullable, meaning it can have no value, and does not have a default value assigned."}, {"column_name": "is_reverted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `is_reverted`**: A boolean flag indicating whether the goods receipt note has been reverted. This column is nullable and does not have a default value, allowing for flexibility in tracking the status of the transaction."}, {"column_name": "rejection_reason", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'rejection_reason':** \n\nThe 'rejection_reason' column stores textual explanations for any rejections associated with goods receipt notes. This column is nullable, allowing for the absence of a reason in cases where no rejection occurred, and has no default value. It provides insights into the approval process by documenting the rationale behind rejected transactions, thus aiding in inventory and financial management."}, {"column_name": "total_payable_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `total_payable_price`**  \nThe `total_payable_price` column represents the total amount due for goods received, stored as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in cases where the payable amount is not yet determined or applicable."}, {"column_name": "transaction_fees", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'transaction_fees':** \n\nRepresents the fees associated with each transaction recorded in the `wb_fact_grn` table. This column is of double precision data type, allowing for the representation of fractional values, and it is nullable, indicating that it may not always contain a value. There is no default value assigned."}, {"column_name": "is_processed", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_processed':**  \nIndicates whether the goods receipt note has been processed. This boolean column can hold a value of true or false and is nullable, allowing for the possibility that the processing status is not yet determined."}, {"column_name": "is_disabled_for_listing", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`is_disabled_for_listing`: A boolean flag indicating whether the associated item is disabled from being listed in the inventory. This column is nullable and does not have a default value, allowing for a flexible representation of the item's listing status in relation to goods receipt notes."}, {"column_name": "spot_payment", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'spot_payment':**  \nThe `spot_payment` column represents the amount paid for goods at the time of receipt, stored as a double precision value. It is nullable, allowing for entries without a specified payment amount, and does not have a default value. This column aids in tracking immediate payment transactions within the detailed records of goods receipt notes in the `wb_fact_grn` table."}, {"column_name": "employee_id", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'employee_id':**  \nRepresents the unique identifier for employees associated with goods receipt transactions. This text field is nullable and does not have a default value, allowing for flexibility in capturing employee information related to inventory management and financial processes within the `wb_fact_grn` table."}, {"column_name": "cash_advance_account_pk", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'cash_advance_account_pk'**  \nThis column stores a text-based identifier for the cash advance account associated with a goods receipt note. It is nullable, indicating that it may not always contain a value, and does not have a default value. This column aids in linking financial transactions to specific cash advance accounts within the context of the `wb_fact_grn` table, which tracks detailed information about goods receipt notes, including payment options and transaction details."}, {"column_name": "item_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'item_name':**  \nThe 'item_name' column stores the name of the item associated with each goods receipt note in the `wb_fact_grn` table. It is of type text, allows null values, and does not have a default value. This column provides essential details for identifying goods received, contributing to the overall management of inventory and transaction records."}, {"column_name": "item_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'item_code':**  \nThe 'item_code' column stores the unique identifier for each item associated with goods receipt notes in the `wb_fact_grn` table. It is of type text, allows null values, and does not have a default value, enabling flexible entry for item identification."}, {"column_name": "item_type", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'item_type':**  \nThe 'item_type' column stores the classification of items associated with goods receipt notes in the `wb_fact_grn` table. This text field is nullable and does not have a default value, allowing for flexibility in categorizing various item types as needed."}, {"column_name": "warehouse_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `warehouse_code`**: This text column stores the unique identifier for the warehouse associated with each goods receipt note. It is nullable, allowing for cases where the warehouse information may not be applicable. The `warehouse_code` aids in tracking inventory logistics and managing the flow of goods within the warehouse system."}, {"column_name": "is_deleted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_deleted':** \n\nIndicates whether the record has been marked as deleted. This boolean column allows for soft deletion of entries in the `wb_fact_grn` table, enabling the retention of historical data without permanently removing it from the database. The column is nullable, meaning it can hold a value of true, false, or null, and has no default value."}, {"column_name": "next_approval", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `next_approval`** - This column stores information about the upcoming approval stage in the goods receipt process. It is of type `text`, allowing for flexible input, and can hold null values if no subsequent approval is required or applicable."}, {"column_name": "is_rejected", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_rejected':**  \nIndicates whether the goods receipt note has been rejected in the approval process. This boolean value can be null, allowing for cases where the rejection status is not yet determined."}, {"column_name": "rejected_date", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'rejected_date':**  \nThe `rejected_date` column records the timestamp when a goods receipt note was rejected, capturing the exact date and time of the rejection event. This column is nullable, indicating that it may not always have a value if the note has not been rejected. The inclusion of time zone data allows for accurate tracking across different geographic locations, aiding in the management of inventory and financial transactions within the `wb_fact_grn` table."}, {"column_name": "created_offline", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created_offline':** \n\nThe 'created_offline' column records the timestamp (with time zone) indicating when a goods receipt note was created offline. This column is nullable and does not have a default value, allowing for flexibility in tracking offline transactions within the `wb_fact_grn` table."}, {"column_name": "is_traded", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_traded':**  \nIndicates whether the goods associated with the receipt note have been traded or not. This boolean field can be null and does not have a default value, allowing for flexibility in tracking the trading status of items within the `wb_fact_grn` table."}, {"column_name": "raised_for_farmer", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'raised_for_farmer':**  \nIndicates whether a goods receipt note has been raised specifically for a farmer. This boolean column is nullable and does not have a default value, allowing for flexibility in recording the association with farming activities in the context of inventory and transaction management."}, {"column_name": "cash_advance_account_paid", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `cash_advance_account_paid`**: Indicates whether the cash advance account has been paid. This boolean column can be null, reflecting cases where payment status is not applicable or not yet determined."}, {"column_name": "truck_no", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'truck_no':**  \nThe 'truck_no' column stores the identification number of the truck associated with a goods receipt note. It is a text field that can be left empty (nullable) and has no default value. This column aids in tracking the logistics of inventory movement within the `wb_fact_grn` table."}, {"column_name": "created_by_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`created_by_id`: A nullable bigint that identifies the user or system responsible for creating the record in the `wb_fact_grn` table. This column aids in tracking the origin of entries related to goods receipt notes, enhancing accountability and auditability in inventory and financial management."}, {"column_name": "goods_receipt_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'goods_receipt_id':**  \nA bigint that optionally identifies the goods receipt associated with transactions in the `wb_fact_grn` table, linking detailed records of goods receipt notes to their respective financial and inventory management activities."}, {"column_name": "rejected_by_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'rejected_by_id':**  \nThis column stores the identifier (bigint) of the user or entity that rejected the goods receipt note. It is nullable, indicating that there may be instances where a rejection has not been recorded. This column is crucial for tracking accountability and managing the approval process within the inventory and financial transaction workflow."}, {"column_name": "cash_advance_account_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `cash_advance_account_id`**: This column stores the identifier for the cash advance account associated with a goods receipt note. It is of type bigint and can be null, indicating that not all records may have a corresponding cash advance account. The absence of a default value allows for flexibility in linking transactions to cash advance accounts as needed."}, {"column_name": "additional_fees", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `additional_fees`**  \nRepresents any extra charges associated with goods receipt transactions, stored as a double precision value. This column is nullable, allowing for the possibility of no additional fees being applicable to certain transactions, and does not have a default value."}, {"column_name": "wm_updated", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'wm_updated':**  \nIndicates whether the associated goods receipt note has been updated in the warehouse management system. This boolean field is optional and defaults to NULL, providing flexibility in tracking the update status of records within the `wb_fact_grn` table."}, {"column_name": "is_clean", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_clean':**  \nIndicates whether the goods receipt note has been verified and is free of discrepancies. This boolean value can be NULL, suggesting that the cleanliness status may not be determined for some records."}, {"column_name": "revert_reason", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'revert_reason':**  \nThe `revert_reason` column captures the rationale for reverting a goods receipt note, providing context for changes made to the transaction. This text field is optional and can be left blank, allowing users to document specific reasons for adjustments in the goods receipt process."}, {"column_name": "is_accounting_posted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_accounting_posted':**  \nIndicates whether the goods receipt note has been posted to accounting. This boolean column can be null, reflecting that the posting status may not be determined for certain entries."}, {"column_name": "tenant_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'tenant_id':**  \nThe 'tenant_id' column (bigint, nullable) represents the unique identifier for the tenant associated with each goods receipt note in the `wb_fact_grn` table. It allows for multi-tenant data management, enabling the differentiation of transactions and inventory records across various tenants within the system."}, {"column_name": "is_uploaded", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_uploaded':**  \nIndicates whether the goods receipt note has been uploaded to the system. This boolean column can be NULL, reflecting its optional nature, and does not have a default value."}, {"column_name": "certified", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'certified':** \n\nIndicates whether the goods receipt note has been certified (TRUE) or not (FALSE). This boolean column is nullable and does not have a default value, allowing for cases where certification status may not be applicable."}, {"column_name": "is_edited", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_edited':**  \nIndicates whether the goods receipt note has been modified (true) or remains in its original state (false). This boolean column is nullable and does not have a default value, allowing for flexibility in tracking edits made to the record."}, {"column_name": "payment_option", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'payment_option':**  \nIndicates the method of payment used for the transaction, allowing for flexibility in payment processing. This text column can be left empty (nullable) and does not have a default value, accommodating various payment methods associated with goods receipt notes in the `wb_fact_grn` table."}, {"column_name": "preferred_bank_account_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`preferred_bank_account_id`: This bigint column stores the identifier for the user's preferred bank account, which can be associated with transactions recorded in the `wb_fact_grn` table. It is nullable, indicating that a preferred bank account may not be specified for every entry."}, {"column_name": "discount", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'discount'**  \nRepresents the discount applied to a transaction in the `wb_fact_grn` table, expressed as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in recording discounts on goods receipt notes."}, {"column_name": "source", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'source':**  \nThe 'source' column contains textual information indicating the origin of the goods receipt note. It is nullable and has no default value, allowing for flexibility in capturing various source identifiers relevant to the transaction."}], "description": "The `wb_fact_grn` table stores detailed information related to goods receipt notes, including transaction details, approval statuses, weights, pricing, and item specifics. It tracks various attributes such as the approval process, payment options, and warehouse logistics, along with timestamps for creation and updates, facilitating comprehensive management of inventory and financial transactions."}, {"table_name": "wb_fact_loan", "columns": [{"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':**  \nThe 'id' column is a nullable bigint that serves as a unique identifier for each loan record in the `wb_fact_loan` table. It allows for the tracking and referencing of individual loans within the dataset."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created':**  \nThe 'created' column records the timestamp of when a loan entry is created in the `wb_fact_loan` table. This column uses the data type 'timestamp with time zone' and is nullable, allowing for the possibility that a creation timestamp may not be provided. It is essential for tracking the chronological order of loan records and understanding the timeline of loan processing within the system."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'updated':** \n\nThe 'updated' column stores the timestamp of the last update made to a loan record in the `wb_fact_loan` table. It is of type timestamp with time zone and can be null, indicating that a loan record may not have been updated since its creation. This column is essential for tracking changes in loan details over time."}, {"column_name": "loan_bundlename", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `loan_bundlename`**: This column stores the name of the loan bundle associated with a loan entry in the `wb_fact_loan` table. It is of type text, can be left empty (nullable), and does not have a default value. This field may be used to group loans for reporting or analysis purposes."}, {"column_name": "created_offline", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created_offline':**  \nThe `created_offline` column stores the timestamp (with time zone) indicating when the loan was created offline. This column is nullable and does not have a default value, allowing for the possibility that some loans may not have an associated offline creation timestamp. It is part of the `wb_fact_loan` table, which captures comprehensive details about loans, including their approval status and financial metrics."}, {"column_name": "approval_date", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'approval_date':** \n\nThe 'approval_date' column records the timestamp of when a loan was officially approved, including the time zone information. This column is nullable, indicating that it may not have a value if the loan has not yet been approved. It is essential for tracking the loan's approval status within the broader context of the loan's lifecycle in the `wb_fact_loan` table."}, {"column_name": "farmer_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`farmer_id`: A bigint data type column that stores the unique identifier for farmers associated with loans in the `wb_fact_loan` table. This column is nullable and does not have a default value, allowing for the possibility of loans that may not be linked to a specific farmer."}, {"column_name": "project_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'project_id':**  \nThe `project_id` column is a nullable bigint that uniquely identifies the associated project for each loan recorded in the `wb_fact_loan` table. It links loan entries to specific projects, facilitating the tracking of project-related financial metrics and statuses."}, {"column_name": "project_start_date", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `project_start_date`**  \nThe `project_start_date` column records the timestamp of when a project associated with a loan begins. It is of type \"timestamp with time zone,\" allowing for accurate tracking across different time zones. This column is nullable, meaning it may not contain a value for all records, and it does not have a default value."}, {"column_name": "project_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'project_name':**  \nThe `project_name` column stores the name of the project associated with a loan in the `wb_fact_loan` table. It is of text data type, can contain null values, and does not have a default value. This column provides contextual information about the specific project linked to each loan record."}, {"column_name": "project_code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'project_code':**  \nThe `project_code` column stores a text identifier for the related project associated with the loan. It is nullable and has no default value, allowing for flexibility in cases where a project may not be specified."}, {"column_name": "maturity_date", "data_type": "date", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`maturity_date`: Represents the date on which the loan is due for full repayment. This field is of type date and can be left empty (nullable). It does not have a default value."}, {"column_name": "warehouse_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'warehouse_id':**  \nThe `warehouse_id` column (bigint, nullable) identifies the specific warehouse associated with the loan entry in the `wb_fact_loan` table. This column links loans to their respective storage locations, enabling tracking of inventory and asset management related to the financed projects. It does not have a default value."}, {"column_name": "warehouse_name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'warehouse_name':**  \nThe `warehouse_name` column stores the name of the warehouse associated with the loan, represented as text. This column is nullable and has no default value. It provides context for the location or storage related to the loan's associated projects or assets within the `wb_fact_loan` table."}, {"column_name": "tenant_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'tenant_id':** \n\nThe `tenant_id` column is a bigint that represents the identifier for the tenant associated with the loan record. It is nullable, allowing for cases where a tenant may not be specified, and does not have a default value. This column is used to link loans to specific tenants, providing context for the financial data captured in the `wb_fact_loan` table."}, {"column_name": "ln_id", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'ln_id'**  \nThe 'ln_id' column is a text field that serves as a unique identifier for each loan entry in the `wb_fact_loan` table. It is nullable and does not have a default value, allowing for flexibility in loan record management. This identifier is crucial for tracking and referencing specific loans within the detailed framework of loan information captured in the table."}, {"column_name": "hectare", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'hectare'**  \nRepresents the area, in hectares, associated with the loan project, providing a measure of the agricultural land involved. This column is of type bigint, allowing for large numeric values, and can accept null entries if the land area is not specified."}, {"column_name": "total_loan_value", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `total_loan_value`**: Represents the total monetary value of the loan, recorded as a double precision number. This column is nullable and does not have a default value, reflecting the financial magnitude associated with each loan entry in the `wb_fact_loan` table."}, {"column_name": "repayment_value", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'repayment_value':** \n\nThe `repayment_value` column represents the amount of money repaid towards a loan, recorded as a double precision floating-point number. This column is nullable, indicating that it may not always contain a value, and has no default value assigned. It is part of the `wb_fact_loan` table, which captures comprehensive loan information, including repayment details and financial metrics."}, {"column_name": "amount_repaid", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `amount_repaid`**: Represents the total amount of money repaid towards the loan. This column is of double precision type, allowing for precise financial calculations. It is nullable, indicating that there may be instances where repayment information is not available. The absence of a default value means that explicit entries are required for this column when applicable."}, {"column_name": "insurance", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'insurance':**  \nThe 'insurance' column represents the financial value of insurance associated with the loan, stored as a double precision number. This column is nullable, indicating that insurance information may not be applicable for all loans, and it does not have a default value."}, {"column_name": "crg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'crg':** \n\nThe 'crg' column in the `wb_fact_loan` table represents a financial metric related to the loan, stored as a double precision value. It is nullable and does not have a default value, allowing for flexibility in instances where this metric may not be applicable or available."}, {"column_name": "interest", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'interest':**  \nRepresents the interest amount associated with the loan, expressed as a double precision value. This column can contain null values and does not have a default value."}, {"column_name": "admin_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`admin_fee`: A nullable column of type double precision that represents the administrative fee associated with the loan. This fee is recorded in the `wb_fact_loan` table, which tracks comprehensive details about loans, including financial metrics and repayment information."}, {"column_name": "equity", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'equity'**  \nRepresents the amount of equity associated with the loan, stored as a double precision value. This column is nullable, indicating that it may not have a value in all records, and does not have a default value assigned."}, {"column_name": "to_balance", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'to_balance':**  \nRepresents the remaining balance of the loan, recorded as a double precision value. This column is nullable and does not have a default value, indicating that it may not always contain data. It plays a crucial role in tracking the financial status of loans within the `wb_fact_loan` table, contributing to the overall understanding of loan repayment dynamics."}, {"column_name": "loan_status", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'loan_status':**  \nThe 'loan_status' column indicates the current status of the loan, reflecting its approval or repayment state. This text field is nullable, allowing for the possibility of unspecified statuses, and does not have a default value. It plays a crucial role in assessing the overall health of the loan and its progress within the loan management system."}, {"column_name": "is_repaid", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_repaid':**  \nIndicates whether the loan has been fully repaid. This boolean column can be null, reflecting cases where the repayment status is not applicable or unknown."}, {"column_name": "is_approved", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_approved':** \n\nIndicates whether the loan has been approved. This boolean value can be either true (approved) or false (not approved) and is nullable, meaning it may not have a value in certain cases. The default value is set to none."}, {"column_name": "is_approval_completed", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`is_approval_completed` (boolean, nullable) - Indicates whether the loan approval process has been completed. This field is used to track the status of loan approvals in the `wb_fact_loan` table and can be NULL if the approval status is not yet determined."}, {"column_name": "is_rejected", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_rejected':**  \nIndicates whether the loan has been rejected. This boolean column can hold a value of true or false and is nullable, meaning it may not always contain a value."}, {"column_name": "is_reverted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_reverted':** \n\nIndicates whether the loan has been reverted (true) or not (false). This boolean field is nullable and does not have a default value."}], "description": "The `wb_fact_loan` table captures detailed information about loans, including their approval status, repayment details, and associated projects and farmers. It tracks key timestamps related to loan creation and updates, as well as project specifics such as name, code, and start date. Financial metrics related to the loan, including total value, repayments, and fees, are also recorded, alongside indicators of loan status and approval processes."}, {"table_name": "wb_fact_loan_breakdown", "columns": [{"column_name": "id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'id':**  \nThe 'id' column is a bigint type that serves as a unique identifier for each record in the `wb_fact_loan_breakdown` table. This column is nullable and does not have a default value, allowing for flexibility in record management. It is essential for referencing specific loans associated with agricultural projects, facilitating tracking and management within the context of tenant operations."}, {"column_name": "created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'created':** \n\nThe 'created' column records the timestamp of when a loan breakdown entry was created, stored as a timestamp with time zone. This column is nullable and does not have a default value, allowing for flexibility in recording creation dates for various entries in the `wb_fact_loan_breakdown` table, which manages detailed loan information for agricultural projects."}, {"column_name": "updated", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'updated'**  \nThe 'updated' column records the timestamp of the most recent modification made to the loan breakdown entry, stored with time zone information. It is nullable, meaning it may not always have a value, and it does not have a default value. This column is essential for tracking changes and ensuring the accuracy of loan records within the `wb_fact_loan_breakdown` table."}, {"column_name": "maturity_date", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'maturity_date':**  \nThe 'maturity_date' column represents the timestamp indicating when a loan associated with an agricultural project is due for repayment. This column is of type 'timestamp with time zone' and may contain null values, signifying that not all loans may have a defined maturity date. It plays a crucial role in tracking the repayment schedule and managing loan timelines within the `wb_fact_loan_breakdown` table."}, {"column_name": "farmer_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'farmer_id':**  \nThe `farmer_id` column is a bigint that optionally identifies the farmer associated with a specific loan record in the `wb_fact_loan_breakdown` table. This identifier links the loan details to the corresponding farmer, enabling tracking and management of loans within agricultural projects."}, {"column_name": "project_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'project_id':** \n\nThe `project_id` column is a nullable bigint that uniquely identifies the agricultural project associated with each loan entry in the `wb_fact_loan_breakdown` table. This column serves to link loan records to specific projects, enabling detailed tracking and management of loans within the context of agricultural operations."}, {"column_name": "warehouse_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'warehouse_id':**  \nThe `warehouse_id` column is a bigint that optionally identifies the warehouse associated with a specific loan record in the `wb_fact_loan_breakdown` table. It may contain NULL values and serves to link loans to their respective storage locations, enhancing the management and tracking of agricultural project loans."}, {"column_name": "item_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'item_id':**  \nThe `item_id` column is a bigint that uniquely identifies each item associated with loans in the `wb_fact_loan_breakdown` table. It is nullable and does not have a default value, allowing for flexibility in data entry. This column is essential for linking loan records to specific items within agricultural projects, aiding in the organization and management of loan-related information."}, {"column_name": "tenant_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'tenant_id':**  \nThe `tenant_id` column is a bigint data type that serves as an identifier for the tenant associated with each loan record in the `wb_fact_loan_breakdown` table. This column is nullable and does not have a default value, allowing for flexibility in representing loans that may not be linked to a specific tenant. It plays a critical role in managing and tracking loans within the context of agricultural operations specific to individual tenants."}, {"column_name": "ln_id", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'ln_id':**  \nThe 'ln_id' column stores a unique identification string for each loan entry in the `wb_fact_loan_breakdown` table. It is of text data type and is nullable, meaning it can contain no value. This column is essential for linking loan records to specific agricultural projects and farmers, aiding in the tracking and management of loans within agricultural operations."}, {"column_name": "line_id", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'line_id':**  \nThe `line_id` column is a text field that optionally identifies a specific line item within the loan breakdown records. It may be used to differentiate between multiple entries or components associated with a single loan, aiding in the detailed tracking and management of loans related to agricultural projects. This column can contain null values and does not have a default value."}, {"column_name": "hectare", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'hectare':**  \nRepresents the area (in hectares) associated with the agricultural project linked to the loan. This column is of type bigint, allowing for large numerical values, and is nullable, indicating that it may not always have a value. It does not have a default value."}, {"column_name": "units", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'units':**  \nThe 'units' column stores a bigint value representing the quantity of units associated with each loan in the `wb_fact_loan_breakdown` table. This column is nullable and has no default value, allowing for flexibility in recording the number of units linked to agricultural loan projects."}, {"column_name": "unit_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'unit_price':**  \nThe `unit_price` column represents the price per unit of goods or services related to the loans in the `wb_fact_loan_breakdown` table. It is stored as a double precision value, allowing for precise financial calculations. This column is nullable, indicating that there may be instances where the unit price is not applicable or not provided for certain records."}, {"column_name": "total_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `total_price`**  \nRepresents the total financial amount associated with a loan in the context of agricultural projects. This column is of type double precision, allowing for a detailed representation of monetary values. It is nullable, meaning that it may not always have a value assigned, and does not have a default value. The `total_price` is crucial for understanding the overall financial commitment of loans within the `wb_fact_loan_breakdown` table, which tracks various aspects of agricultural financing."}, {"column_name": "total_loan_value", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'total_loan_value':**  \nRepresents the total monetary value of loans associated with agricultural projects. This column is of double precision type, allowing for accurate representation of large financial figures. It is nullable, meaning that it may contain no value in some records. The 'total_loan_value' is crucial for analyzing the financial metrics related to loan distribution and management within the agricultural sector."}, {"column_name": "repayment_value", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `repayment_value`**  \nRepresents the monetary amount associated with the repayment of loans for agricultural projects. This column is of type double precision, can contain null values, and does not have a default value. It is used to track the financial performance and obligations related to loans in the `wb_fact_loan_breakdown` table."}, {"column_name": "amount_repaid", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`amount_repaid`: This column represents the total amount of loan repayments made against a loan associated with agricultural projects. It is of double precision data type, allowing for accurate representation of monetary values. This column is nullable, indicating that there may be instances where repayment data is not available or applicable."}, {"column_name": "insurance", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'insurance':**  \nThe 'insurance' column represents the monetary value of insurance associated with each loan in the `wb_fact_loan_breakdown` table. It is of type double precision and can contain null values, indicating that insurance may not be applicable or provided for every loan. This column aids in analyzing the financial protection measures related to agricultural loans."}, {"column_name": "crg", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'crg':**  \nThe 'crg' column represents a financial metric related to loans in the `wb_fact_loan_breakdown` table, stored as double precision. This column is nullable and does not have a default value, indicating that it may not always contain data for every record."}, {"column_name": "interest", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'interest':** \n\nThe 'interest' column represents the interest rate applicable to the loans recorded in the `wb_fact_loan_breakdown` table. It is stored as a double precision value, allowing for precise representation of interest rates. This column is nullable, indicating that it may not always have a value, and there is no default value assigned."}, {"column_name": "admin_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'admin_fee':** \n\nThe `admin_fee` column represents the administrative fee associated with each loan in the `wb_fact_loan_breakdown` table. This value is stored as a double precision number and can be null, indicating that some loans may not incur an administrative fee. The absence of a default value allows for flexibility in recording varying fee amounts based on the loan specifics."}, {"column_name": "equity", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `equity`**  \nRepresents the amount of equity associated with a loan in the context of agricultural projects. This column is of double precision data type, allowing for decimal values, and it is nullable, meaning it may not always have a value assigned. The `equity` field plays a crucial role in providing insights into the financial structure of loans, helping to assess the balance between borrowed funds and the farmer's own capital investment."}, {"column_name": "to_balance", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `to_balance`**  \nRepresents the remaining balance of a loan associated with agricultural projects, stored as a double precision value. This column is nullable and does not have a default value, indicating that it may not always contain data for every loan record in the `wb_fact_loan_breakdown` table."}, {"column_name": "loan_status", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `loan_status`**  \nThe `loan_status` column indicates the current status of a loan associated with agricultural projects, allowing for tracking of its repayment and management. It is of text data type and can be null, meaning that a loan may not have a defined status at all times."}, {"column_name": "data_identification_verification", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`data_identification_verification`: A double precision column that stores verification metrics related to the identification of loans within agricultural projects. This column is nullable and does not have a default value, allowing for flexibility in data entry. It plays a role in ensuring the accuracy and reliability of loan-related data in the `wb_fact_loan_breakdown` table."}, {"column_name": "value_chain_management", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`value_chain_management`: A nullable double precision column that represents the financial metrics related to the management of value chains within agricultural projects. This column captures the economic impact or costs associated with different stages of the value chain, contributing to the overall analysis of loans in the `wb_fact_loan_breakdown` table."}, {"column_name": "is_repaid", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_repaid':**  \nIndicates whether the loan has been repaid. This boolean column can hold values of true (repaid) or false (not repaid) and is nullable, allowing for cases where repayment status is unknown."}, {"column_name": "is_approved", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_approved':** \n\nIndicates whether the loan has been approved. This boolean column can take on a value of true or false and is nullable, meaning that it may not have a value assigned. It is used to assess the status of loan approvals within the context of agricultural projects managed in the `wb_fact_loan_breakdown` table."}, {"column_name": "is_approval_completed", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_approval_completed':** \n\nIndicates whether the loan approval process has been completed. This boolean column can be NULL, signifying that the approval status is unknown or not applicable. It plays a crucial role in tracking the loan's progression within the agricultural project management framework."}, {"column_name": "is_rejected", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_rejected':**  \nIndicates whether the loan application has been rejected. This boolean column is nullable, allowing for the representation of an unknown status, and does not have a default value."}, {"column_name": "is_reverted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_reverted':**  \nIndicates whether a loan has been reverted. This boolean column can be NULL and does not have a default value. A value of TRUE signifies that the loan has been reverted, while FALSE indicates it has not. This information aids in tracking the status and management of loans within agricultural projects."}], "description": "The `wb_fact_loan_breakdown` table stores detailed information about loans associated with agricultural projects, including financial metrics, loan status, and identification related to farmers and projects. It captures data on loan values, repayment details, and various fees, along with timestamps for record creation and updates. This table facilitates the tracking and management of loans within a specific tenant's agricultural operations."}, {"table_name": "tr_fact_transactions", "columns": [{"column_name": "tid", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('tr_fact_transactions_tid_seq'::regclass)", "description": "**Column Description for 'tid':** \n\nThe `tid` column is an integer that serves as the unique identifier for each trading transaction in the `tr_fact_transactions` table. It is a non-nullable field that automatically increments using the sequence `tr_fact_transactions_tid_seq`. This column is essential for uniquely referencing individual transactions and linking them to their associated financial metrics, trade attributes, and client details."}, {"column_name": "actual_trade_value", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: actual_trade_value**  \nRepresents the actual monetary value of a trade transaction, recorded as a double precision floating-point number. This column is nullable and does not have a default value, allowing for the possibility of missing data in certain transaction records."}, {"column_name": "actual_trade_vol_mt", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `actual_trade_vol_mt`**  \nRepresents the actual trading volume in metric tons for a transaction. This column is of double precision data type, allowing for precise numerical values. It is nullable, indicating that a trade may not always have a recorded volume. This metric is essential for analyzing the scale and impact of trading activities within the `tr_fact_transactions` table."}, {"column_name": "order_id", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `order_id`**  \nThe `order_id` column is an integer that represents a unique identifier for each trading transaction recorded in the `tr_fact_transactions` table. This column is nullable, allowing for the possibility of missing values, and does not have a default value. It serves as a critical reference for tracking and analyzing individual orders within the broader context of trade activities and client interactions."}, {"column_name": "trade_creation_date", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `trade_creation_date`**\n\nRepresents the timestamp when a trading transaction was created. This column is of type \"timestamp without time zone,\" allowing for the recording of precise creation times without timezone information. It is nullable, meaning that it may contain no value for certain records, and has no default value set. This field is essential for tracking the timing of trade activities within the `tr_fact_transactions` table."}, {"column_name": "oms_provider_id", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'oms_provider_id':** \n\nAn optional integer identifier that associates the trading transaction with a specific Order Management System (OMS) provider. This column aids in linking transactions to the corresponding OMS, enhancing the ability to analyze and report on trade activities within the broader context of trading operations."}, {"column_name": "trade_status", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'trade_status':**  \nThe `trade_status` column indicates the current status of a trading transaction within the `tr_fact_transactions` table. It is a character varying field that may contain values such as 'pending', 'completed', 'canceled', or 'failed', providing insights into the lifecycle of each trade. This column is nullable, allowing for the absence of a status in certain cases, and does not have a default value."}, {"column_name": "ovs_validation", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'ovs_validation':**  \nThe `ovs_validation` column stores optional character varying data related to the validation status or results of trading transactions. This field is nullable, allowing for entries without a specified validation status, and it does not have a default value. It is intended to support the analysis and reporting of transaction verifications within the `tr_fact_transactions` table."}, {"column_name": "trade_status_summary", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'trade_status_summary':**  \nA character varying field that provides a summarized status of trading transactions, reflecting the current state or outcome of each trade. This column is nullable and does not have a default value, allowing for flexibility in representing the status of trades within the `tr_fact_transactions` table."}, {"column_name": "order_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'order_type':** \n\nThe `order_type` column specifies the nature of the trading order, indicating whether it is a market, limit, or other type of order. This column is of variable character type, allows null values, and does not have a default value, providing flexibility in capturing the specific type of order associated with each transaction in the `tr_fact_transactions` table."}, {"column_name": "is_on_behalf", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_on_behalf':**  \nIndicates whether the transaction was executed on behalf of another party. This boolean column can be null, allowing for the possibility that the representation of the transaction's agency status is not specified."}, {"column_name": "is_rejected", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `is_rejected`**  \nIndicates whether a trading transaction has been rejected. This boolean column can hold a value of true (rejected) or false (not rejected) and is nullable, meaning it can also be left undefined. This column helps in tracking the status of transactions within the broader context of trade activities captured in the `tr_fact_transactions` table."}, {"column_name": "is_deleted", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'is_deleted':**  \nIndicates whether a transaction has been marked as deleted. This boolean field can be used to filter out inactive records from the `tr_fact_transactions` table, allowing for more accurate analysis and reporting of current trade activities. It is nullable, meaning a value may not always be present."}, {"column_name": "is_order_cancelled", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`is_order_cancelled`: A boolean flag indicating whether a trading order has been cancelled. This column is nullable and does not have a default value, allowing for the representation of both cancelled and active orders within the trading transactions captured in the `tr_fact_transactions` table."}, {"column_name": "volume_per_unit", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'volume_per_unit':** \n\nRepresents the quantity of units traded per transaction, stored as an integer. This column is nullable, allowing for the absence of data in cases where the volume is not applicable or not recorded. It plays a crucial role in analyzing transaction metrics and trade volume within the `tr_fact_transactions` table."}, {"column_name": "calc_volume_per_unit", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`calc_volume_per_unit`: An integer that represents the calculated trading volume per unit for each transaction. This column is nullable and does not have a default value. It aids in analyzing transaction efficiency and volume distribution within the `tr_fact_transactions` table."}, {"column_name": "trade_order_units", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `trade_order_units`**: An integer that represents the number of units involved in a trade order. This column is nullable, indicating that it may not always have a value. It provides essential information for analyzing trade volume and order execution within the context of trading transactions captured in the `tr_fact_transactions` table."}, {"column_name": "trade_order_vol_mt", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'trade_order_vol_mt':** \n\nRepresents the volume of trade orders measured in metric tons. This column is of type double precision, allowing for the storage of decimal values. It is nullable, indicating that it may not always contain a value, and does not have a default value assigned. This metric is essential for analyzing trading activity and performance within the `tr_fact_transactions` table."}, {"column_name": "trade_order_vol_kg", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'trade_order_vol_kg':** \n\nRepresents the volume of trade orders in kilograms. This integer column is nullable and does not have a default value, allowing for flexibility in recording transactions where the volume may not be applicable. It is part of the `tr_fact_transactions` table, which focuses on detailed trading transaction data."}, {"column_name": "matched_units", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'matched_units':** \n\nThe `matched_units` column represents the number of trading units successfully matched in a transaction. It is of integer data type and can contain null values, indicating that there may be instances where this information is not applicable or available. This column plays a crucial role in analyzing trade effectiveness and transaction outcomes within the `tr_fact_transactions` table."}, {"column_name": "matchedorder_units", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'matchedorder_units':** \n\nRepresents the number of units associated with matched trading orders in the `tr_fact_transactions` table. This integer field is nullable and does not have a default value, allowing for flexibility in recording transactions where matched order units may not be applicable."}, {"column_name": "invoiced_units", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'invoiced_units':**  \nThe 'invoiced_units' column represents the quantity of units for which invoices have been generated in the context of trading transactions. This integer field is nullable, indicating that it may not always contain a value, and does not have a default value assigned. It is integral for analyzing invoicing metrics associated with transactions recorded in the `tr_fact_transactions` table."}, {"column_name": "matchedorder_vol_mt", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `matchedorder_vol_mt`**  \nRepresents the volume of matched orders in a trading transaction, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility of absent data in cases where no matched orders are present. It contributes to the overall analysis of trading activities within the `tr_fact_transactions` table."}, {"column_name": "matchedorder_vol_kg", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'matchedorder_vol_kg':** \n\nRepresents the volume of matched orders in kilograms. This integer column is nullable and does not have a default value, allowing for the potential absence of data in the context of trading transactions recorded in the `tr_fact_transactions` table."}, {"column_name": "cancelled_units", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`cancelled_units`: Represents the number of trading units that have been cancelled for a transaction. This column is of integer type, can contain null values, and has no default value, allowing for flexible tracking of cancellations within trade activities documented in the `tr_fact_transactions` table."}, {"column_name": "currency", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'currency':** \n\nThe 'currency' column stores the currency code used in the trading transactions recorded in the `tr_fact_transactions` table. It is of type character varying, allowing for flexibility in length, and can be left null if not applicable. This column helps identify the currency in which each transaction was conducted, supporting accurate financial analysis and reporting."}, {"column_name": "currency_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `currency_code`**: This column stores the currency code associated with each trading transaction, represented as a variable-length character string. It is optional and can contain null values, allowing for flexibility in cases where the currency is not specified. The currency code is crucial for understanding the monetary context of transactions within the `tr_fact_transactions` table."}, {"column_name": "currency_sign", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'currency_sign':** \n\nRepresents the symbol of the currency used in the transaction (e.g., $, €, £). This column is a variable character field that allows null values, indicating that the currency sign may not always be specified for a transaction."}, {"column_name": "order_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'order_price':** \n\nThe `order_price` column represents the price at which a trading order is executed. It is of type double precision, allowing for detailed numerical representation, and can be null if not applicable. This column is essential for analyzing the financial metrics of transactions recorded in the `tr_fact_transactions` table."}, {"column_name": "order_base_price", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'order_base_price':** \n\nThe `order_base_price` column represents the initial price at which a trade order is executed, expressed as a double precision value. It is nullable, indicating that it may not always have a value assigned. This column is essential for analyzing trade transactions within the `tr_fact_transactions` table, providing insight into the financial metrics related to trading activities."}, {"column_name": "complete_charged_fees", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `complete_charged_fees`**  \nThis column stores information regarding the total fees charged for completed transactions within the `tr_fact_transactions` table. It is of type character varying, allowing for a flexible representation of fee values, and it can accept NULL entries, indicating that not all transactions may have associated fees recorded."}, {"column_name": "fee_per_unit", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'fee_per_unit':** \n\nThe 'fee_per_unit' column represents the cost associated with each unit of the traded asset, stored as a double precision value. It can accept null values, indicating that a fee may not always be applicable or defined for every transaction. This column is crucial for calculating total transaction costs and analyzing financial metrics related to trading activities within the `tr_fact_transactions` table."}, {"column_name": "use_vat", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'use_vat':**  \nIndicates whether Value Added Tax (VAT) is applicable to the transaction. This boolean field allows for the inclusion of VAT-related calculations in the transaction's financial metrics. The column is nullable and does not have a default value, allowing flexibility in specifying VAT applicability for each transaction."}, {"column_name": "use_ecn_fees", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `use_ecn_fees`**: A boolean flag indicating whether ECN (Electronic Communication Network) fees are applicable for the transaction. This column is nullable and does not have a default value, allowing flexibility in capturing whether ECN fees are utilized in each transaction record within the `tr_fact_transactions` table."}, {"column_name": "brokerage_fees_breakdown", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`brokerage_fees_breakdown`: A character varying field that provides a detailed breakdown of brokerage fees associated with each trading transaction recorded in the `tr_fact_transactions` table. This column is nullable and does not have a default value, allowing for flexibility in capturing fee information as applicable to each transaction."}, {"column_name": "brokerage_fee_percent", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `brokerage_fee_percent`**\n\nThis column represents the percentage of the brokerage fee associated with a trading transaction. It is stored as a double precision value and may be null if not applicable. This metric is essential for calculating the total costs incurred by clients during trades and is crucial for financial analysis and reporting within the trading environment."}, {"column_name": "cm_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'cm_fee':**  \nThe 'cm_fee' column represents the commission fee associated with a trading transaction, stored as a double precision value. This column is nullable and does not have a default value, allowing for the possibility that some transactions may not incur a commission. It contributes to the financial metrics captured in the `tr_fact_transactions` table, aiding in the analysis of trade costs and overall transaction profitability."}, {"column_name": "sec_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'sec_fee':** \n\nThe `sec_fee` column represents the secondary fee associated with a trading transaction. It is stored as a double precision value, allowing for precise financial representation. This column is nullable, indicating that a fee may not always be applicable to every transaction. There is no default value assigned, meaning that if no fee is specified, the value will be NULL."}, {"column_name": "discount_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`discount_type`: A character varying field that specifies the type of discount applied to a transaction. This column is nullable, indicating that not all transactions may have a discount type assigned. It plays a role in categorizing and analyzing discounts within trading activities recorded in the `tr_fact_transactions` table."}, {"column_name": "discount", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'discount':** \n\nRepresents the discount applied to a trading transaction, expressed as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in capturing transactions that may or may not include a discount."}, {"column_name": "vat_value", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'vat_value':**  \nThe 'vat_value' column represents the value of Value Added Tax (VAT) associated with each trading transaction. It is stored as a double precision floating-point number, allowing for precise calculations. This column is nullable, indicating that it may not have a value for every transaction, and does not have a default value."}, {"column_name": "exchange_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'exchange_fee':**  \nThe `exchange_fee` column represents the fee charged by the exchange for processing a trade transaction. It is stored as a double precision value and can be null, indicating that not all transactions may incur an exchange fee. This column is essential for calculating the total cost of transactions and analyzing trading expenses within the `tr_fact_transactions` table."}, {"column_name": "total_oms_fees", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'total_oms_fees':**  \nRepresents the total fees associated with order management system (OMS) transactions for a specific trade, stored as a double precision value. This column is nullable and does not have a default value, allowing for flexibility in cases where fee information may not be applicable."}, {"column_name": "total_afex_fees", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'total_afex_fees':** \n\nThe 'total_afex_fees' column represents the total fees associated with AFEX transactions for each trade entry in the `tr_fact_transactions` table. This column is of double precision data type and can accept null values, indicating that not all transactions may have associated fees recorded. It is used to facilitate financial analysis and reporting related to transaction costs within the trading environment."}, {"column_name": "trade_value", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`trade_value`: Represents the monetary value of a trading transaction, stored as a double precision floating-point number. This column is nullable, indicating that it may contain missing values, and does not have a default value. It is a key metric for analyzing the financial impact of trades within the `tr_fact_transactions` table."}, {"column_name": "trade_value_with_fee", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `trade_value_with_fee`**: Represents the total value of a trade, including any associated fees, expressed as a double precision floating-point number. This column is nullable and does not have a default value, allowing for flexibility in recording transactions where fee information may be absent."}, {"column_name": "calculated_trade_value", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'calculated_trade_value':**  \nRepresents the computed monetary value of a trade transaction, expressed as a double precision floating-point number. This column is nullable, indicating that it may not always contain a value, and it does not have a default value. It is used to facilitate financial analysis and reporting within the context of trading activities captured in the `tr_fact_transactions` table."}, {"column_name": "otc_bags_delivered", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `otc_bags_delivered`**: An integer column representing the number of over-the-counter (OTC) bags delivered as part of a trading transaction. This field is nullable and does not have a default value, allowing for the possibility of no OTC bags being delivered in certain transactions."}, {"column_name": "otc_volume_delivered", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'otc_volume_delivered':** \n\nRepresents the volume of over-the-counter (OTC) transactions delivered, measured in double precision. This column is nullable and does not have a default value, allowing for the possibility of missing data in the context of trading transactions captured in the `tr_fact_transactions` table."}, {"column_name": "otc_calculated_trade_value_delivered", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`otc_calculated_trade_value_delivered`: This column represents the calculated value of over-the-counter (OTC) trades that have been delivered. It is stored as a double precision numeric value, allowing for precise financial calculations. This column is nullable, meaning it may contain null values when trade delivery data is not applicable or unavailable. It plays a crucial role in analyzing the financial metrics associated with OTC transactions in the context of trading activities."}, {"column_name": "deduction_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'deduction_type':**  \nSpecifies the type of deduction applied to a transaction, allowing for categorization of various financial adjustments. This column is of variable character type, can be left empty, and has no default value."}, {"column_name": "deduction", "data_type": "double precision", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'deduction':** \n\nThe `deduction` column represents a financial adjustment or reduction applied to a trading transaction, stored as a double precision value. It is nullable, indicating that it may not always have a value, and has no default assigned. This column is integral for analyzing the net financial impact of transactions within the `tr_fact_transactions` table."}, {"column_name": "security_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_code':**  \nThe `security_code` column stores a variable-length string representing the unique identifier for the traded security. This column is nullable and does not have a default value, allowing for the absence of data in cases where the security may not be specified. It is essential for linking transactions to specific financial instruments within the `tr_fact_transactions` table."}, {"column_name": "security_name", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_name':**  \nThe 'security_name' column stores the name of the financial instrument involved in the trading transaction. It is a variable-length character field that can contain null values, allowing for cases where the security might not be specified. This information is essential for identifying the specific asset traded, contributing to a comprehensive understanding of transaction details within the `tr_fact_transactions` table."}, {"column_name": "sec_security_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'sec_security_type':**  \nThe `sec_security_type` column stores the type of security involved in the transaction, represented as a variable-length character string. This column is nullable, allowing for the absence of data, and does not have a default value. It is essential for categorizing transactions within the `tr_fact_transactions` table, aiding in the analysis of different security types traded."}, {"column_name": "security_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'security_type':**  \nThe 'security_type' column in the `tr_fact_transactions` table specifies the category of financial instruments involved in each transaction, such as stocks, bonds, options, or derivatives. This column is of character varying type, allows null values, and does not have a default value, enabling flexibility in categorizing various security types associated with trading activities."}, {"column_name": "board_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'board_type':** \n\nThe `board_type` column stores the type of trading board associated with the transaction. It is a variable-length character field that may contain NULL values, indicating that the board type is optional for certain transactions. This information aids in categorizing and analyzing trade activities based on the specific trading platform or board utilized."}, {"column_name": "board_type_sec", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'board_type_sec':**  \nThe 'board_type_sec' column represents the type of board associated with the security in a trading transaction. It is a variable character field that can accommodate different board designations and is nullable, indicating that this information may not always be available. This column is essential for categorizing securities based on their trading platforms or market segments within the broader context of transaction analysis in the 'tr_fact_transactions' table."}, {"column_name": "security_location_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\nThe `security_location_code` column stores a character varying code that indicates the specific location or venue where the security transaction took place. This column is nullable and does not have a default value, allowing for flexibility in cases where the location is not specified. It is integral to understanding the context of the trading transaction within the `tr_fact_transactions` table, aiding in the analysis of trade activities and their geographical implications."}, {"column_name": "client_creation_date", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'client_creation_date':** \n\nThe `client_creation_date` column records the timestamp of when a client was created within the system. This field is of type timestamp without time zone and is nullable, meaning it may not always have a value. This information is essential for tracking the age of client accounts and analyzing client behavior over time in relation to trading activities captured in the `tr_fact_transactions` table."}, {"column_name": "client_cid", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'client_cid':** \n\nThe 'client_cid' column is a character varying data type that stores unique identifiers for clients associated with trading transactions. This column is nullable, meaning it can contain empty values, and does not have a default value. It plays a crucial role in linking transaction records to specific clients, thereby aiding in the analysis of client-related trading activities within the `tr_fact_transactions` table."}, {"column_name": "client_fullname", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'client_fullname':**  \nThe 'client_fullname' column stores the full name of the client associated with each trading transaction. It is of type character varying, allowing for flexible name lengths, and can contain null values if a client's name is not provided. This column aids in identifying and analyzing client-specific transaction details within the 'tr_fact_transactions' table."}, {"column_name": "client_account_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'client_account_type':** \n\nRepresents the type of account held by the client, allowing for differentiation between various account classifications. This column is of variable character type, can contain null values, and does not have a default value."}, {"column_name": "client_user_account_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** \n\nThe `client_user_account_type` column specifies the type of user account associated with the client involved in the transaction. This field is of variable character type, allowing for flexibility in account type representation. It is optional and may contain null values, indicating that the account type is not always specified for every transaction."}, {"column_name": "client_email", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'client_email':**  \nThe `client_email` column stores the email address of the client associated with each trading transaction. This field is of variable character type, allowing for flexibility in length, and it is nullable, meaning that it may not be populated for all records. It plays a crucial role in identifying and communicating with clients within the context of the `tr_fact_transactions` table."}, {"column_name": "client_phone", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'client_phone':**  \nThe `client_phone` column stores the contact phone numbers of clients involved in trading transactions. It is a variable-length character field that allows for nullable entries, meaning it can be left empty if not applicable. This column enhances the `tr_fact_transactions` table by providing essential client contact information for communication and support purposes."}, {"column_name": "client_country", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `client_country`**  \nRepresents the country of the client involved in the transaction. This column is of type character varying, allowing for variable-length string entries. It is nullable, meaning that it may contain no value for certain transactions. The absence of a default value indicates that it must be explicitly set when applicable. This information aids in analyzing client demographics and geographical distributions related to trading activities."}, {"column_name": "client_country_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`client_country_code`: A character varying field that stores the country code of the client associated with the trading transaction. This column is nullable and does not have a default value, allowing for flexibility in representing clients from various countries within the `tr_fact_transactions` table."}, {"column_name": "client_is_kyc_complete", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** \n\n`client_is_kyc_complete`: A boolean flag indicating whether the client has completed their Know Your Customer (KYC) verification process. This column is nullable and does not have a default value, allowing for the possibility of unknown KYC status within the `tr_fact_transactions` table, which records detailed information about trading transactions and associated client details."}, {"column_name": "brokerage_community_id", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`brokerage_community_id`: An integer that represents the identifier for the brokerage community associated with the transaction. This column is nullable and does not have a default value, allowing for instances where a transaction may not be linked to a specific brokerage community. It aids in categorizing and analyzing trade activities based on community affiliations within the `tr_fact_transactions` table."}, {"column_name": "broker_community_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `broker_community_code`**  \nA character varying field that optionally stores the code representing the community affiliation of the broker involved in the transaction. This code helps in identifying the broker's community for analytical and reporting purposes within trading activities documented in the `tr_fact_transactions` table."}, {"column_name": "broker_community_name", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n`broker_community_name`: This column stores the name of the broker's community associated with a trading transaction. It is of type character varying, can be null, and has no default value. This information helps in identifying the affiliation of the broker within the trading environment, contributing to a comprehensive analysis of transaction data in the `tr_fact_transactions` table."}, {"column_name": "broker_community_city", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'broker_community_city':** \n\nThis column stores the name of the city associated with the broker's community. It is of type character varying, allowing for flexible string lengths, and can be left empty (nullable). The 'broker_community_city' field provides contextual geographical information that enhances the understanding of trading transactions related to specific broker communities within the `tr_fact_transactions` table."}, {"column_name": "broker_community_state", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `broker_community_state`**  \nRepresents the state of the broker's community affiliation within the context of trading transactions. This column is of variable character type, allowing for diverse state identifiers. It is nullable, indicating that not all records may have an associated community state. This information aids in the analysis of regional trading trends and broker relationships."}, {"column_name": "broker_community_is_viewable", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `broker_community_is_viewable`**: This boolean column indicates whether the broker community associated with a transaction is viewable by the client. A value of `true` signifies that the community details are accessible, while `false` means they are not. This column is nullable, allowing for the possibility of no value being set."}, {"column_name": "broker_community_is_approved", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `broker_community_is_approved`**  \nIndicates whether the broker community associated with the transaction is approved. This boolean field allows for null values, suggesting that the approval status may be unknown or not applicable for certain transactions."}, {"column_name": "broker_community_creation_date", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`broker_community_creation_date`: This column stores the timestamp indicating when the broker community was created. It is of type \"timestamp without time zone\" and can be NULL, meaning that the creation date may not be recorded for all entries. This information is relevant in the context of analyzing the establishment of broker communities within trading transactions."}, {"column_name": "broker_community_owner_id", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`broker_community_owner_id`: This integer column represents the identifier for the broker community owner associated with a trading transaction. It is nullable, meaning that it may not always have a value, and does not have a default value. This column helps link transactions to specific broker communities, aiding in the analysis of community-related trading activities within the `tr_fact_transactions` table."}, {"column_name": "brocom_owner_cid", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'brocom_owner_cid':**  \nA character varying field representing the unique identifier of the broker community owner associated with a trading transaction. This column is optional and can contain null values, allowing for flexibility in recording client details within the context of trading activities captured in the `tr_fact_transactions` table."}, {"column_name": "brocom_owner_fullname", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n`brocom_owner_fullname`: This column stores the full name of the brokerage owner associated with a transaction. It is of type character varying, allowing for flexible name lengths, and is nullable, indicating that it may not always contain a value. This information aids in identifying the owner responsible for the trade within the context of the `tr_fact_transactions` table."}, {"column_name": "brocom_owner_account_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`brocom_owner_account_type`: This column stores the type of account associated with the broker or owner involved in a trading transaction. It is of variable character type, allowing for flexible input of account type designations. The column is nullable, indicating that account type information may not always be available for a transaction."}, {"column_name": "brocom_owner_user_account_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`brocom_owner_user_account_type`: This column stores the account type of the broker-com owner user associated with the trading transaction. It is a variable-length character field that allows for null values, indicating that the account type may not be specified for every transaction. This information aids in categorizing and analyzing user profiles within the trading data."}, {"column_name": "brocom_owner_email", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "Column Description: \n\n**brocom_owner_email**: This column stores the email address of the broker or community owner associated with the trading transaction. It is of type character varying, allowing for variable-length email strings. This field is nullable, indicating that it may not always contain a value, and has no default value."}, {"column_name": "brocom_owner_phone", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`brocom_owner_phone`: A variable character field that stores the phone number of the broker or owner associated with a trading transaction. This column is optional (nullable) and does not have a default value, allowing for flexibility in recording contact information for transactions in the `tr_fact_transactions` table."}, {"column_name": "brocom_owner_country", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'brocom_owner_country':** \n\nRepresents the country of the broker or owner involved in the transaction. This field is of variable character type, allowing for the entry of country names or codes. It is nullable, meaning it can be left empty if the information is not available."}, {"column_name": "brocom_owner_country_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`brocom_owner_country_code`: A variable character field that optionally stores the country code of the broker or owner associated with a trading transaction. This code aids in identifying the geographical origin of the broker, enhancing the analysis of trade activities and client demographics within the `tr_fact_transactions` table."}, {"column_name": "brocom_owner_is_kyc_complete", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`brocom_owner_is_kyc_complete`: A boolean indicator that specifies whether the Know Your Customer (KYC) requirements have been completed for the broker owner associated with the transaction. This column is nullable and does not have a default value, allowing for potential omission in the dataset."}, {"column_name": "brocom_owner_is_afex_broker", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** Indicates whether the owner of the transaction is an AFEX broker. This boolean column may contain null values, suggesting that the ownership status may not be applicable or specified for certain transactions."}, {"column_name": "promoter_community_id", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`promoter_community_id`: An integer that optionally identifies the community associated with the promoter of a trading transaction. This column can be null and does not have a default value, allowing for flexibility in representing transactions that may not be linked to a specific promoter community."}, {"column_name": "promoter_community_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n`promoter_community_code`: A variable-length character field that optionally stores a code representing the community affiliation of the promoter related to the trading transaction. This information aids in identifying community-specific attributes associated with the transaction."}, {"column_name": "promoter_community_name", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`promoter_community_name`: A variable-length character field that optionally holds the name of the community associated with the promoter involved in the transaction. This column aids in identifying community affiliations related to trading activities within the `tr_fact_transactions` table."}, {"column_name": "promoter_community_city", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`promoter_community_city`: A nullable character varying field that represents the city associated with the promoter's community in the context of trading transactions. This column helps link transaction data to specific geographic community affiliations, enhancing the analysis of trade activities and client demographics within the `tr_fact_transactions` table."}, {"column_name": "promoter_community_state", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'promoter_community_state':**  \nRepresents the state of the community associated with the promoter involved in the transaction. This column allows for capturing regional affiliations and is nullable, indicating that this information may not always be available."}, {"column_name": "promoter_community_is_viewable", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`promoter_community_is_viewable`: A boolean flag indicating whether the promoter community associated with the trading transaction is viewable. This column is nullable and has no default value, allowing for flexibility in representing the visibility status of the community in relation to the transaction data."}, {"column_name": "promoter_community_is_approved", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** \n\n`promoter_community_is_approved`: A boolean value indicating whether the promoter community associated with the transaction has been approved. This column is nullable and does not have a default value, allowing for the representation of transactions where the approval status is unknown or not applicable."}, {"column_name": "promoter_community_creation_date", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description:** \n\n`promoter_community_creation_date`: This column stores the timestamp of when the promoter community was created, allowing for tracking and analysis of community establishment within the context of trading transactions. The data type is a timestamp without time zone, and it is nullable, meaning it may not contain a value for all records."}, {"column_name": "promoter_community_owner_id", "data_type": "integer", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`promoter_community_owner_id`: An optional integer identifier representing the owner of the community associated with the transaction. This column links transactions to specific community promoters, enhancing the contextual understanding of trade activities within the `tr_fact_transactions` table."}, {"column_name": "procom_owner_cid", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: procom_owner_cid**  \nA character varying field that optionally stores the client ID of the owner associated with a trading transaction in the `tr_fact_transactions` table. This column is used to link transactions to specific clients, aiding in client-related analysis and reporting within the trading environment."}, {"column_name": "procom_owner_fullname", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `procom_owner_fullname`**  \nThis column stores the full name of the owner associated with a trading transaction. It is of type character varying and allows null values, indicating that the owner's name may not always be available."}, {"column_name": "procom_owner_account_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**\n\n`procom_owner_account_type`: This column stores the type of account associated with the owner of the transaction. It is a variable character field that can accommodate various account type designations. The column is nullable, indicating that the account type information may not always be available for every transaction."}, {"column_name": "procom_owner_user_account_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`procom_owner_user_account_type` (character varying, nullable) - This column indicates the account type of the user who owns the procom (professional communication) associated with the trading transaction. It provides insight into the nature of the user's account, which may influence trading behavior and transaction characteristics within the `tr_fact_transactions` table."}, {"column_name": "procom_owner_email", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`procom_owner_email`: This column stores the email address of the owner associated with the trading transaction. It is a variable-length character field and can be left empty (nullable). This information may be used for communication purposes related to the transaction or for identifying the owner in reporting and analysis."}, {"column_name": "procom_owner_phone", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'procom_owner_phone':**  \nThis column stores the phone number of the owner associated with the trading transaction. It is of variable character type, allowing for phone numbers of varying lengths. The column is nullable, indicating that it may not contain a value for all records. This information aids in identifying and contacting the owner for transaction-related inquiries or issues."}, {"column_name": "procom_owner_country", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: 'procom_owner_country'**  \nThis column stores the country of the owner associated with the trading transaction. It is of type character varying, allowing for flexible character length, and can contain NULL values, indicating that the country information may not be applicable or available for all transactions."}, {"column_name": "procom_owner_country_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'procom_owner_country_code':** \n\nThis column stores the country code of the owner associated with a trading transaction, using a variable character format. It is nullable, meaning that it may not always have a value. This information can aid in identifying the geographical location of the transaction owner, thereby enhancing the analysis of trade activities in relation to regional factors."}, {"column_name": "procom_owner_is_kyc_complete", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "Column Description: Indicates whether the owner of the transaction has completed the Know Your Customer (KYC) verification process. This boolean field may contain null values and does not have a default value."}, {"column_name": "procom_owner_is_afex_broker", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`procom_owner_is_afex_broker`: A boolean flag indicating whether the owner of the transaction is an AFEX broker. This column is nullable and does not have a default value, allowing for flexibility in representing ownership status within trading transactions in the `tr_fact_transactions` table."}, {"column_name": "trade_creator_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'trade_creator_type':**  \nRepresents the type of entity or individual responsible for creating the trade transaction. This column is of variable character type, allowing for the inclusion of diverse identifiers (e.g., trader, algorithm, client). It is nullable, indicating that not all transactions may have a specified creator type."}, {"column_name": "creator_cid", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'creator_cid':**  \nThe 'creator_cid' column stores the client identifier of the individual or entity that initiated the trading transaction. It is of type character varying, allowing for flexible input, and can be left empty (nullable) if no specific creator is associated with the transaction. This column helps link transactions to their respective creators for tracking and reporting purposes within the trading environment."}, {"column_name": "creator_fullname", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'creator_fullname':** \n\nThe `creator_fullname` column stores the full name of the individual or entity that created the transaction record. It is of type character varying, allowing for flexibility in name lengths, and it can be null if no creator is specified. This information is essential for tracing the origin of transactions within the `tr_fact_transactions` table, enhancing accountability and facilitating reporting on transaction creators."}, {"column_name": "creator_account_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `creator_account_type`**  \nIndicates the type of account that created the transaction, represented as a variable-length string. This column is nullable and does not have a default value, allowing for flexibility in recording account details relevant to each transaction."}, {"column_name": "creator_user_account_type", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `creator_user_account_type`**  \nThis column indicates the type of user account that created the trading transaction. It is of variable character type, allowing for different account classifications, and may contain null values if the information is not applicable or available. This data helps in understanding the origin of the transaction within the trading environment, contributing to client analysis and reporting."}, {"column_name": "creator_email", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'creator_email':** \n\nThe 'creator_email' column stores the email address of the individual or entity that initiated the trading transaction. It is of type character varying, allows null values, and does not have a default value. This column is instrumental in identifying the creator of each transaction for tracking and communication purposes within the trading environment."}, {"column_name": "creator_phone", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'creator_phone':** \n\nThe `creator_phone` column stores the phone number of the individual or entity that created the transaction. It is of type character varying, allowing for flexible formatting. This column is nullable, indicating that a phone number may not always be provided. It plays a role in identifying and contacting the creator of the trading transaction within the `tr_fact_transactions` table."}, {"column_name": "creator_country", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'creator_country':**  \nThe `creator_country` column stores the country of origin for the creator associated with each trading transaction. It is a variable-length character field that can accommodate entries of varying lengths. This column is optional (nullable) and does not have a default value, allowing for flexibility in the representation of creator information within the transaction records."}, {"column_name": "creator_country_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'creator_country_code':** \n\nThe `creator_country_code` column holds the country code of the entity or individual who created the transaction. It is a character varying type and may contain null values, indicating that the country code is not always specified. This column aids in identifying the geographical origin of the transaction creator within the context of trading activities recorded in the `tr_fact_transactions` table."}, {"column_name": "creator_is_kyc_complete", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description:**  \n`creator_is_kyc_complete` (boolean, nullable) - Indicates whether the creator of the transaction has completed the Know Your Customer (KYC) verification process. This column helps in assessing the compliance status of the transaction creator, contributing to the overall analysis of client profiles and regulatory adherence in trading activities."}, {"column_name": "creator_is_afex_broker", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "description": "**Column Description: `creator_is_afex_broker`**: A boolean flag indicating whether the creator of the transaction is an AFEX broker. This column is nullable and has no default value, allowing for flexibility in capturing transaction details where the creator's broker status may be unknown."}], "description": "The `tr_fact_transactions` table captures detailed information about trading transactions, including financial metrics, trade attributes, client details, and community affiliations. It is designed to provide a comprehensive view of trade activities, their statuses, associated fees, and client-related information, facilitating analysis and reporting within a trading environment."}, {"table_name": "wb_dim_crop", "columns": [{"column_name": "warehouse_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'warehouse_id':**  \nThe `warehouse_id` column is a bigint data type that stores the unique identifier for a warehouse. It is nullable and does not have a default value. This column links crop data to specific warehouses within the `wb_dim_crop` table, facilitating the organization and management of crop-related information."}, {"column_name": "farmer_created", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'farmer_created':** \n\nThis column stores the timestamp of when the farmer record was created, including time zone information. It is nullable and does not have a default value, indicating that it may not always be populated."}, {"column_name": "farmer_id", "data_type": "bigint", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'farmer_id':** \n\nThe `farmer_id` column is a bigint that stores the unique identifier for the farmer associated with the crop entry. This column is nullable, meaning it may not always have a value. It is used to link crop data to the specific farmer responsible for cultivating the crops in the `wb_dim_crop` table."}, {"column_name": "code", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'code':**  \nThe 'code' column stores a text identifier for crops within the `wb_dim_crop` table. It is nullable and does not have a default value, allowing for the possibility of missing or unspecified crop codes."}, {"column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null, "description": "**Column Description for 'name':**  \nThe 'name' column stores the textual representation of the crop's name in the `wb_dim_crop` table. It is nullable and does not have a default value, allowing for the possibility of missing data."}], "description": "The `wb_dim_crop` table stores information related to crops in a warehouse context, including identifiers for the warehouse and the farmer, as well as metadata such as crop code and name."}]