image: python:3.8

clone:
  depth: full              # SonarCloud scanner needs the full history to assign issues properly

pipelines:
  branches:
    master:    
      - step:
          name: Package Vulnerability Scan
          script:
            - pipe: aquasecurity/trivy-pipe:1.0.0
              variables:
               scanType: 'fs'
               ignoreUnfixed: 'false'
               format: 'table'
               severity: "CRITICAL, HIGH"
               exitcode: '0'
                
     # - parallel:      # these 2 steps will run in parallel
      #   steps: 
       #    - step:
        #        name: SonarCloud Scan Run
         #       image: atlassian/default-image:latest
                #trigger: manual
         #       runs-on:
          #        - self.hosted
          #        - vultr
          #        - linux
          #      caches:
          #       - docker
           #     script:
           #       - pipe: sonarsource/sonarcloud-scan:2.0.0
                 # - pipe: sonarsource/sonarcloud-quality-gate:0.1.6
            #    services:
            #      - docker
            
      - step:
          name: Push Docker Image to AWS ECR
         # trigger: manual
          script:
            - docker build -t afex-sherlock .
            - pipe: atlassian/aws-ecr-push-image:2.0.0
              variables:
                IMAGE_NAME: "afex-sherlock"
                TAGS: "latest" 

      - step:
          name: Deploy to Server
          deployment: production
          trigger: manual
          script:
            - chmod +x deploy.sh
            - /bin/sh deploy.sh
options:
  docker: true
  size: 2x
definitions:
  services:
    docker:
      memory: 4096