import pyotp
from django.conf import settings


class TwoFactorAuth:
    def __init__(self, user):
        self._user = user
        self.__secret = self.get_secret()

    @staticmethod
    def generate_secret_key():
        return pyotp.random_base32()

    def get_secret(self):
        if not self._user.two_factor_auth_secret:
            self._save_user_secret()
        return self._user.two_factor_auth_secret

    def _save_user_secret(self):
        self._user.two_factor_auth_secret = self.generate_secret_key()
        self._user.save(update_fields=["two_factor_auth_secret"])

    def get_qr_code_url(self):
        return pyotp.totp.TOTP(self.__secret).provisioning_uri(
            name=self._user.email,
            issuer_name=settings.AUTHENTICATOR_ISSUER_NAME,
            image=settings.LOGO_URL,
        )

    def verify_user_otp(self, user_otp: str) -> bool:
        return pyotp.totp.TOTP(self.__secret).verify(user_otp)
