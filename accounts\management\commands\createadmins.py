import csv
import os

from django.contrib.auth.models import Group
from django.core.management.base import BaseCommand

from accounts.models import User


class Command(BaseCommand):
    help = "Remove all user permissions and create admin users"

    def handle(self, *args, **kwargs):
        user_group = Group.objects.get(name="USER")
        # clear all user from user group
        user_group.user_set.clear()
        self.stdout.write("USER group removed from all users.")

        user_email = []
        # Get the directory of the current script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        file_name = "admins.csv"
        file_path = os.path.join(script_dir, file_name)

        with open(file_path) as file:
            reader = csv.reader(file)
            for row in reader:
                User.objects.get_or_create(email=row[0])
                user_email.append(row[0])

        # update is_admin field for all users
        User.active_objects.filter(email__in=user_email).update(is_admin=True)
        self.stdout.write("Admin users created.")
