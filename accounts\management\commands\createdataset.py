import psycopg2
import psycopg2.extras
from decouple import config
from django.core.management.base import BaseCommand

from dataset.models import Dataset
from self_service.models import Column

POSTGRES_ADDRESS = config("POSTGRES_ADDRESS")
POSTGRES_PORT = config("POSTGRES_PORT")
POSTGRES_USERNAME = config("POSTGRES_USERNAME")
POSTGRES_PASSWORD = config("POSTGRES_PASSWORD")
POSTGRES_DBNAME = config("POSTGRES_DBNAME")


def get_schemas(connection):
    "return schema in the datasource"
    cursor = connection.cursor()

    cursor.execute("SELECT schema_name FROM information_schema.schemata")

    # Fetch all the results
    schema_names = cursor.fetchall()

    # return [schema[0] for schema in schema_names]
    cursor.close()

    return schema_names


def get_tables(connection):
    """get schema name and table names"""
    cursor = connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

    cursor.execute("""SELECT table_schema, table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'sherlock'
                        ORDER BY table_schema, table_name""")

    tables = cursor.fetchall()

    cursor.close()

    return tables


def get_columns(connection, table_schema, table_name):
    """
    Creates and returns a list of dictionaries for the specified
    schema.table in the database connected to.
    """

    where_dict = {"table_schema": table_schema, "table_name": table_name}

    cursor = connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

    cursor.execute(
        """SELECT column_name, data_type
                      FROM information_schema.columns
                      WHERE table_schema = %(table_schema)s
                      AND table_name   = %(table_name)s""",
        where_dict,
    )

    column_info = cursor.fetchall()

    cursor.close()

    # this returns column name and its data type
    return column_info


class Command(BaseCommand):
    help = "Create a dataset and its columns from database"

    def handle(self, *args, **kwargs):
        try:
            conn = psycopg2.connect(
                f"dbname={POSTGRES_DBNAME} host={POSTGRES_ADDRESS} user={POSTGRES_USERNAME} password={POSTGRES_PASSWORD}"
            )

            tables = get_tables(conn)
            for row in tables:
                name_pattern = f'{row["table_schema"]}.{row["table_name"]}'
                dataset, _ = Dataset.objects.get_or_create(name=name_pattern)

                column_info = get_columns(conn, row["table_schema"], row["table_name"])
                for row in column_info:
                    column_name = row["column_name"]
                    column_data_type = row["data_type"]
                    Column.objects.update_or_create(
                        name=column_name,
                        dataset=dataset,
                        defaults={"data_type": column_data_type},
                    )
            self.stdout.write("Dataset created successfully")

            conn.close()

        except psycopg2.Error as e:
            print(e)
