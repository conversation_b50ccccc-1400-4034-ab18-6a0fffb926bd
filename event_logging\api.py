from django.conf import settings
from django.http import JsonResponse
from influxdb_client import InfluxDBClient
from influxdb_client.client.write_api import SYNCHRONOUS
from ninja import Router

from base.constants import BUCKET, HOST, ORG, SUCCESS, TOKEN
from sherlock.schemas import Error, Success

from .schemas import EventCreateSchema

router = Router()

client = InfluxDBClient(
    url=f"http://{settings.INFLUXDB[HOST]}:8086", token=settings.INFLUXDB[TOKEN]
)


@router.get("/get")
def get_events(request, start_time: str | None = None, end_time: str | None = None):
    """
    - gets data in influxdb timeseries database
    """
    if not start_time:
        start_time = "-48h"

    if end_time:
        start_time = start_time + f", stop: {end_time}"

    query = f'from(bucket: "{settings.INFLUXDB[BUCKET]}")\
            |> range(start: {start_time})\
            |> yield(name: "mean")'

    records = client.query_api().query(query, org=settings.INFLUXDB[ORG])

    results = []
    for table in records:
        for record in table.records:
            results.append({"data": record.get_value(), "time": record.get_time()})

    return JsonResponse(results, safe=False)


@router.post("/create", response={200: Success, 400: Error})
def create_event(request, event: EventCreateSchema | list[EventCreateSchema]):
    """
    - stores data in influxdb timeseries database
    """
    if isinstance(event, list):
        if len(event) > 1:
            evt_data = event
        else:
            return 400, {"error": "Array cannot be empty"}
    else:
        if hasattr(event, "data"):
            evt_data = [event]
        else:
            return 400, {"error": "Data not supplied"}

    write_api = client.write_api(write_options=SYNCHRONOUS)
    query = [f'mem,host=host1 event="{event.data}"' for event in evt_data]

    write_api.write(settings.INFLUXDB[BUCKET], settings.INFLUXDB[ORG], query)
    return SUCCESS
