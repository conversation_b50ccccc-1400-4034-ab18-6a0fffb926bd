import os

from celery import Celery
from celery.schedules import crontab

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "sherlock.settings")
app = Celery("sherlock")
app.config_from_object("django.conf:settings", namespace="CELERY")

app.conf.timezone = "Africa/Lagos"
app.broker_connection_retry_on_startup = True

app.autodiscover_tasks()

app.conf.beat_schedule = {
    "check_alerts": {
        "task": "ai.tasks.check_alerts",
        # At every 5 minutes, every hour from 6 through 18 (6pm) on every day-of-week from Monday through Friday.
        "schedule": crontab(
            minute="*/5",
            hour="6-18",
            day_of_month="*",
            month_of_year="*",
            day_of_week="1-5",
        ),
    },
    "delete_read_notifications": {
        "task": "accounts.tasks.delete_read_notifications",
        # At 00:00 on day-of-month 1
        "schedule": crontab(
            minute="0", hour="0", day_of_month="1", month_of_year="*", day_of_week="*"
        ),
    },
}


@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f"Request: {self.request!r}")


@app.task
def save_request_log(log_data):
    return log_data.get("user")
