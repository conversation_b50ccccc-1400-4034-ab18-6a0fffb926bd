.idea/*
*__pycache__*
*.pyc
venv/*
*.sqlite3
*venv/*
.vscode/*
*.orig
.DS_Store
media/
celerybeat-schedule
celerybeat.pid
.cache/*
.env
*/migrations/0*.py
/logs
.local.env
.dbenv
/staticfiles
env/*
server.py
query.json
shvenv/
ai/db/
ai/sherlock.log
ai/metadata/table_index_dir
ai/metadata/full_*
dump.rdb
exp_cost_analysis.ipynb
llama_index_tracing_tutorial.ipynb
=2.0.0
time_analysis.ipynb
exp.py
exp.ipynb
ai/*_v2.py
*.png
monitor/
ai/.mypy_cache
conextdata
ai_exp.ipynb
ai_requirements.txt
db

# # Streamlit test scripts and documentation
ai/test_chat_streamlit.py
ai/demo_chat_interface.py
ai/backend_test_streamlit.py
ai/run_chat_tester.py
ai/run_backend_tester.py
ai/test_django_setup.py
ai/test_requirements.txt
ai/README_STREAMLIT_TESTER.md
