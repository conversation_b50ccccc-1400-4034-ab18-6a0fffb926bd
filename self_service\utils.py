from asgiref.sync import sync_to_async
from django_celery_beat.models import ClockedSchedule, CrontabSchedule, PeriodicTask

from base.choices import Frequency
from self_service.models import Recipient


def serialize_queryset(schema, qs):
    return [serialize_single_obj(schema, obj) for obj in qs]


async def async_serialize_queryset(*args, **kwargs):
    return await sync_to_async(serialize_queryset)(*args, **kwargs)


def serialize_single_obj(schema, qs):
    return schema.from_orm(qs).dict()


async def async_serialize_single_obj(*args, **kwargs):
    return await sync_to_async(serialize_single_obj)(*args, **kwargs)


def create_periodic_task(schedule_report):
    task_name = f"send_scheduled_report_{schedule_report.id}"
    if schedule_report.frequency == Frequency.dnr:
        schedule, _ = ClockedSchedule.objects.get_or_create(
            clocked_time=schedule_report.scheduled_datetime
        )
        task = PeriodicTask.objects.create(
            clocked=schedule,
            name=task_name,
            one_off=True,
            start_time=schedule_report.start_time,
            task="self_service.tasks.send_scheduled_report",
            args=[schedule_report.id],
        )
    else:
        schedule, _ = CrontabSchedule.objects.get_or_create(
            minute=schedule_report.minute,
            hour=schedule_report.hour,
            day_of_week=schedule_report.day_of_week,
            day_of_month=schedule_report.day_of_month,
            month_of_year=schedule_report.month_of_year,
        )
        task = PeriodicTask.objects.create(
            crontab=schedule,
            name=task_name,
            start_time=schedule_report.start_time,
            task="self_service.tasks.send_scheduled_report",
            args=[schedule_report.id],
        )
    return task


def update_recipients(recipients, schedule):
    for recipient_type, recipient_ids in recipients:
        schedule.recipients.filter(
            recipient_type=recipient_type
        ).delete()  # Delete existing recipients
        Recipient.objects.bulk_create(
            [
                Recipient(
                    user_id=id,
                    schedule_report=schedule,
                    recipient_type=recipient_type,
                )
                for id in recipient_ids
            ]
        )


def edit_periodic_task(schedule_report):
    task = schedule_report.task
    schedule = task.schedule
    if schedule_report.frequency == Frequency.dnr:
        schedule, _ = ClockedSchedule.objects.get_or_create(
            clocked_time=schedule_report.scheduled_datetime
        )
        task = PeriodicTask.objects.filter(name=task.name).update(
            clocked=schedule, one_off=True
        )

    else:
        schedule, _ = CrontabSchedule.objects.get_or_create(
            minute=schedule_report.minute,
            hour=schedule_report.hour,
            day_of_week=schedule_report.day_of_week,
            day_of_month=schedule_report.day_of_month,
            month_of_year=schedule_report.month_of_year,
        )
        task = PeriodicTask.objects.filter(name=task.name).update(
            crontab=schedule, start_time=task.start_time, one_off=False
        )

    return task
