"""
Modified By: <PERSON>
Modified At: 2024-06-27
"""

import urllib.parse

from decouple import config
from sqlalchemy import create_engine
import sqlalchemy
import sqlalchemy.exc
from ai.settings import SherlockSettings


def db_connect():
    """
    This connects to a postgres DB using a .env file

    Parameters
    ----------
    conn_param :
            LIVE_POSTGRES_ADDRESS : link DB on cloud
            LIVE_POSTGRES_PORT : port
            LIVE_POSTGRES_USERNAME
            LIVE_POSTGRES_PASSWORD
            LIVE_POSTGRES_DBNAME

    Returns
    -------
    postgres engine, postgres connection
    """
    # Create the engine
    # print("Connecting to Database")
    POSTGRES_ADDRESS = config("POSTGRES_ADDRESS")
    POSTGRES_PORT = config("POSTGRES_PORT")
    POSTGRES_USERNAME = config("POSTGRES_USERNAME")
    POSTGRES_PASSWORD = urllib.parse.quote_plus(config("POSTGRES_PASSWORD"))
    POSTGRES_DBNAME = config("POSTGRES_DBNAME")
    dbschema = "sherlock"
    connection_string = f"postgresql://{POSTGRES_USERNAME}:{POSTGRES_PASSWORD}@{POSTGRES_ADDRESS}:{POSTGRES_PORT}/{POSTGRES_DBNAME}"

    engine = create_engine(
        connection_string, connect_args={"options": f"-csearch_path={dbschema}"}
    )
    # test connection
    try:
        conn = engine.connect()
        return engine, conn
    except sqlalchemy.exc.OperationalError as err:
        SherlockSettings.logger.error(str(err))
        raise err

        
   
