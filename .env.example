DEBUG=
SECRET_KEY=
DB_HOST=
DB_NAME=
DB_USERNAME=
DB_PASSWORD=
DB_PORT=
BROKER_URL=
FRONTEND_SERVER_URL=
FRONTEND_SET_PASSWORD_URL=
CELERY_BROKER=
CELERY_BACKEND=
SERVER_EMAIL=
EMAIL_USE_TLS=
EMAIL_HOST=
EMAIL_PORT=
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
DEFAULT_FROM_EMAIL=
EMAIL_BACKEND=
CACHE_BACKEND=
EMAIL_PORT=
EMAIL_HOST_PASSWORD_TEST=
ALLOWED_HOSTS=*
CORS_ALLOWED_ORIGINS=
CSRF_TRUSTED_ORIGINS=
API_KEY=
JWT_SALT=""
INFLUXDB_ORG=""
INFLUXDB_BUCKET=""
SSO_URL= ""
SSO_API_KEY=""
SSO_SECRET_KEY=""
AZURE_CLIENT_ID=
AZURE_CLIENT_SECRET=
AZURE_TENANT_ID=

OPENAI_API_KEY =
POSTGRES_ADDRESS=""
POSTGRES_DBNAME=""
POSTGRES_USERNAME=""
POSTGRES_PASSWORD=""
POSTGRES_PORT=
ENCRYPTION_KEY=
ENCRYPTION_VECTOR=

 # aws settings
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=
AWS_S3_SIGNATURE_NAME=
AWS_S3_REGION_NAME=
AWS_DEFAULT_ACL=
DEFAULT_FILE_STORAGE=
# USE_S3 = True

WH_ADDRESS=
WH_DBNAME=
WH_USERNAME=
WH_PASSWORD=
WH_PORT=
