import uuid

from django.contrib.auth import get_user_model
from django.db import models

from accounts.models import Role, UserManagedModel
from base.choices import Sensitivity
from base.models import BaseModel

# Create your models here.

User = get_user_model()


class DataSource(BaseModel):
    name = models.CharField(max_length=200, db_index=True)
    address = models.CharField(max_length=200)
    port = models.PositiveIntegerField()
    username = models.CharField(max_length=200)
    password = models.JSONField(
        default=dict
    )  # this is a dict of the encrypted password
    alias = models.CharField(max_length=100, db_index=True, default="")

    def __str__(self):
        return self.name


class Dataset(BaseModel):
    """this is the Datasource schemas"""

    identifier = models.UUIDField(default=uuid.uuid4, editable=False, db_index=True)
    name = models.CharField(max_length=100, db_index=True)
    datasource = models.ForeignKey(
        DataSource, on_delete=models.CASCADE, related_name="dataset"
    )

    def __str__(self):
        return self.name

    @property
    def datasource_id(self):
        return self.datasource.id


class Table(BaseModel):
    name = models.CharField(max_length=100, db_index=True)
    alias = models.CharField(max_length=100, db_index=True, default="")
    description = models.TextField(null=True, blank=True)
    dataset = models.ForeignKey(
        Dataset, on_delete=models.CASCADE, related_name="tables"
    )

    def __str__(self):
        return self.name

    @property
    def schema_name(self):
        return self.dataset.name

    def has_everyone_applied_rule(self):
        return self.rules.filter(is_applied_to_everyone=True).exists()

    def has_general_rule(self):
        return self.rules.filter(is_general=True).exists()


class Tag(UserManagedModel):
    name = models.CharField(max_length=100, db_index=True)
    alias = models.CharField(max_length=100, db_index=True, default="")

    def __str__(self):
        return self.name


class Column(BaseModel):
    name = models.CharField(max_length=100, db_index=True)
    table = models.ForeignKey(Table, on_delete=models.CASCADE, related_name="columns")
    data_type = models.CharField(max_length=50, default="")
    description = models.TextField(null=True, blank=True)
    alias = models.CharField(max_length=200, null=True, blank=True)
    sensitivity = models.CharField(
        max_length=50, choices=Sensitivity.choices, default=Sensitivity.low
    )
    tags = models.ManyToManyField(Tag, through="ColumnTag", blank=True)

    def __str__(self):
        return self.name


class ColumnTag(BaseModel):
    """intermediate table for column tags many to many"""

    tags = models.ForeignKey(Tag, on_delete=models.CASCADE)
    columns = models.ForeignKey(Column, on_delete=models.CASCADE)


class Rule(UserManagedModel):
    table = models.ForeignKey(Table, on_delete=models.CASCADE, related_name="rules")
    name = models.CharField(max_length=100, db_index=True)
    columns = models.JSONField(default=dict)
    is_general = models.BooleanField(default=False)
    is_applied_to_everyone = models.BooleanField(default=False)
    roles = models.ManyToManyField(Role, related_name="roles", blank=True)

    def __str__(self):
        return self.name

    @property
    def filters(self):
        return self.rule_filters.all()

    @property
    def users(self):
        return self.rule_users.all()

    def user_has_access(self, user_id):
        return self.users.filter(user_id=user_id).exists()

    @property
    def users_names(self):
        return self.users.values_list("user__first_name", "user__last_name", "user__id")

    def role_has_access(self, roles_id):
        # check if any of the user roles exist in rule roles
        return self.roles.filter(id__in=roles_id).exists()


class RuleFilter(BaseModel):
    filter = models.JSONField()
    connector = models.CharField(max_length=10, null=True, blank=True)
    rule = models.ForeignKey(
        Rule,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="rule_filters",
    )

    def __str__(self):
        return self.rule.name


class RuleUser(BaseModel):
    rule = models.ForeignKey(
        Rule, on_delete=models.CASCADE, related_name="rule_users", null=True, blank=True
    )
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return self.user.get_full_name()
