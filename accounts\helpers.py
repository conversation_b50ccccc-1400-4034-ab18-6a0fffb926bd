from .models import User


def validate_ids(Model, ids):
    """validate if an id is part of the model object_id.
    Returns only valid ids
    """
    return Model.active_objects.filter(id__in=ids).values_list("id", flat=True)


def validate_perm_ids(Permission, ids):
    return Permission.objects.only("id").filter(id__in=ids)


def get_user_email(ids):
    return User.active_objects.filter(id__in=ids).values_list("email", flat=True)
