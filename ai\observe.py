"""
Script Name: Sherlock Observer
Author: <PERSON>
Created At: 2024-08-07
Description: Instrumentation Script. Traces and pushes all llm chain flow to the phoenix UI
"""

import requests  # type: ignore
from decouple import config
from openinference.instrumentation.langchain import <PERSON><PERSON>hainInstrumentor
from openinference.instrumentation.llama_index import LlamaIndexInstrumentor
from opentelemetry import trace as trace_api
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk import trace as trace_sdk
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace.export import SimpleSpanProcessor
from openinference.semconv.resource import ResourceAttributes

from ai.settings import SherlockSettings


def start_instrument():
    """Starts the instrumentation of LLM Prompt Flow"""
    resource = Resource(attributes={ResourceAttributes.PROJECT_NAME: SherlockSettings.PROJECT_NAME})
    tracer_provider = trace_sdk.TracerProvider(resource=resource)
    endpoint = config("PHOENIX_URL", cast=str)

    try:
        # Test if phoenix is live
        status = requests.get(endpoint, timeout=5)
        if status.status_code == 200:
            SherlockSettings.logger.info("phoenix is live!!!")
            span_exporter = OTLPSpanExporter(endpoint,
                            headers={"authorization": F"Bearer {SherlockSettings.PHOENIX_SYSTEM_KEY}"})
            span_processor = SimpleSpanProcessor(span_exporter)
            tracer_provider.add_span_processor(span_processor)
            trace_api.set_tracer_provider(tracer_provider)
            LlamaIndexInstrumentor().instrument()
            LangChainInstrumentor().instrument()

            SherlockSettings.logger.info("Started Instrumentation!!!")
        else:
            SherlockSettings.logger.error(f"Failed to connect to pheonix server!!!\nStatus: {status.status_code}")

    except requests.exceptions.ConnectionError as conn_err:
        conn_err.add_note("Failed to connect to phoenix")
        SherlockSettings.logger.error(f"Failed to connect to Phoenix Server. Status: {str(conn_err)}")
        # print(f"View Prompt Flow at {endpoint}")
    except requests.exceptions.Timeout as time_err:
        time_err.add_note("Connecting to Phoenix Timeout")
        SherlockSettings.logger.error(f"{str(time_err)}")


def  stop_instrument():
    LangChainInstrumentor().uninstrument()
    LlamaIndexInstrumentor().uninstrument()
    SherlockSettings.logger("Instrumentation Stopped. Traces will not be collected")


if __name__ == "__main__":
    start_instrument()
