import json
import os

from django.conf import settings
from django.core.management.base import BaseCommand

from dataset.models import Column, Table


class Command(BaseCommand):
    help = "Update Table and column description"

    def handle(self, *args, **kwargs):
        # Load the JSON file
        file_name = "sherlock_table_descriptions.json"
        base_dir = settings.BASE_DIR / "dataset/metadata"
        file_path = os.path.join(base_dir, file_name)

        with open(file_path) as file:
            data = json.load(file)

        def update_datatype(data):
            columns_to_update = []

            for table_data in data:
                # get the table
                table = Table.objects.filter(name=table_data["table_name"]).first()

                # Update each column's datatype
                for column_data in table_data.get("columns", []):
                    # Retrieve column with reference to the table
                    column = Column.objects.filter(
                        name=column_data["column_name"], table=table
                    ).first()
                    column.data_type = column_data["data_type"]

                    columns_to_update.append(column)

            # Perform bulk updates for tables and columns
            Column.objects.bulk_update(columns_to_update, ["data_type"])

        update_datatype(data)
        self.stdout.write("datatype updated successfully.")
