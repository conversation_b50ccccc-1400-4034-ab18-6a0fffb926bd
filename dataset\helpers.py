import json
import re
from typing import Any

import orjson
import pandas as pd
import psycopg2
from asgiref.sync import sync_to_async
from django.conf import settings
from django.db.models import Q
from ninja import FilterSchema
from psycopg2 import sql

from base.cache_keys import WAREHOUSE_DATA_CACHE_KEY
from base.constants import DND, SENSITIVE, QueryJsonKey
from utils.aes import aes_gcm_cipher
from utils.cache_utils import add_to_cache, get_from_cache

from .models import Column, Dataset, DataSource, Rule, RuleFilter, RuleUser, Table, Tag
from .sql import ALL_COLUMNS_LIST, SQL
from .tasks import generate_metadata


class ConnectDB:
    def __init__(self, db_name, hostname, username, password, port):
        self.db_name = db_name
        self.hostname = hostname
        self.username = username
        self.password = password
        self.port = port
        self.conn, self.cursor = self.connect()

    def connect(self):
        """connect to a datasource"""
        connection = psycopg2.connect(
            f"dbname={self.db_name} host={self.hostname} user={self.username} password={self.password} port={self.port}"
        )
        cursor = connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        return connection, cursor

    def get_schemas(self):
        """return all schema in the datasource"""

        self.cursor.execute("SELECT schema_name FROM information_schema.schemata")
        schema_names = self.cursor.fetchall()  # Fetch all the results # returns a tuple
        self.disconnect()

        return [schema["schema_name"] for schema in schema_names]

    def get_db_tables(self, schema_name):
        """get table names from schema"""
        query = sql.SQL("""
                        SELECT table_schema, table_name FROM information_schema.tables WHERE table_schema = {schema_name}
                        ORDER BY table_schema, table_name
                            """).format(schema_name=sql.Literal(schema_name))

        self.cursor.execute(query)
        tables = self.cursor.fetchall()

        return tables

    def get_db_columns(self, table_schema, table_name):
        """get column name from table"""
        query = sql.SQL(
            """SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_schema = {table_schema} AND table_name = {table_name}
            """
        ).format(
            table_schema=sql.Literal(table_schema),
            table_name=sql.Literal(table_name),
        )
        self.cursor.execute(query)

        column_info = self.cursor.fetchall()
        # this returns column name and its data type
        return column_info

    def execute_query(self, query):
        """execute query using the cursor.
        returns a dataframe of data excluding the id column"""
        # print(query.as_string(self.conn))
        self.cursor.execute(query)
        result = self.cursor.fetchall()
        df = pd.DataFrame(result)
        df = df.loc[:, df.columns != "id"]  # exclude id from the dataframe
        data = json.loads(df.to_json(orient="records"))
        return data

    def construct_query(
        self,
        table_name,
        schema,
        limit: int | None = None,
        offset: int | None = None,
        filters: list | None = None,
        column_list: list | None = None,
        dynamic_columns: list | None = None,
        order_by: list | None = None,
        group_by: list | None = None,
    ):
        """returns SQL query that selects specified columns or all"""
        if column_list is None:
            column_list = ALL_COLUMNS_LIST

        derived_columns = (
            self.construct_dynamic_columns(dynamic_columns) if dynamic_columns else None
        )
        filters = self.construct_filter_query(filters) if filters else None
        order_by = self.construct_order_by_query(order_by) if order_by else None
        group_by = self.construct_group_by_query(group_by) if group_by else None
        query = SQL.select(
            table_name,
            schema,
            limit,
            offset,
            column_list,
            filters,
            derived_columns,
            order_by,
            group_by,
        )

        return query

    def construct_count_query(
        self,
        table_name,
        schema,
        column_name="*",
        alias=None,
        filters: list | None = None,
    ):
        """returns a SQL count query for all columns with or without filter"""
        filters = self.construct_filter_query(filters) if filters else None
        query = SQL.count(table_name, schema, column_name, alias, filters)

        return query

    def construct_filter_query(self, filters_list: list):
        """returns filter query
        Args:
            filters: [dict]
        skip first group and values or if len(value) == 1, connector if any

        """
        composed_strings = []
        for i, obj in enumerate(filters_list):
            filters = obj["filter"]
            group_connector = obj["connector"]

            strings = []
            for j, filter in enumerate(filters):
                connector = filter.get("connector")
                column_name = filter.get("column_name")
                operator = filter.get("operator")
                value = filter.get("value")

                if operator.lower() == "in":
                    value = sql.SQL(", ").join([sql.Literal(n) for n in value])
                else:
                    value = sql.Literal(value)

                filter_clause = sql.SQL(
                    "{connector} {column_name} {operator} {value}"
                ).format(
                    connector=sql.SQL(connector)
                    if j > 0
                    else sql.SQL(
                        ""
                    ),  # set the first connector of filter list to None if any
                    column_name=sql.Identifier(column_name),
                    operator=sql.SQL(operator),
                    value=value,
                )
                strings.append(filter_clause)
            group_connector = sql.SQL(group_connector) if i > 0 else sql.SQL("")
            composed_query = sql.SQL(" ").join(strings)
            composed_query = sql.SQL("({composed_query} )").format(
                composed_query=composed_query
            )
            composed_query = group_connector + composed_query
            composed_strings.append(composed_query)
        query = sql.SQL(" ").join(composed_strings)
        filtered_query = sql.SQL(" WHERE {query}").format(query=query)
        return filtered_query

    def construct_dynamic_columns(self, dynamic_columns: list):
        """args: [dict]
        for each dynamic column:
        convert formula str to a list of char
        join list of SQL identified characters,
        returns a list of SQL composed query
        """
        all_composed_expression = []

        for column in dynamic_columns:
            formula = column["formula"]
            alias = column["alias"]

            # Define operators and aggregate functions
            operators = {"+", "-", "*", "^", "/", "%", "(", ")"}
            aggregate_func = {"min", "max", "count", "sum", "avg"}

            # Regular expression to tokenize the formula
            # matches (), +, -, *, ^, /, %, variable names and digits
            pattern = r"(\(|\)|\+|-|\*|/|\^|%|[a-zA-Z_]+|\d+)"
            tokens = re.findall(pattern, formula)

            derived_columns = []

            for token in tokens:
                if token.isdigit():
                    derived_columns.append(sql.Literal(token))
                elif token in operators or token.lower() in aggregate_func:
                    derived_columns.append(sql.SQL(token))
                else:
                    derived_columns.append(sql.Identifier(token))

            # Join the derived_columns with a space
            composed_expression = sql.SQL(" ").join(derived_columns)
            query = sql.SQL("{expression} AS {alias}").format(
                expression=composed_expression, alias=sql.Identifier(alias)
            )

            all_composed_expression.append(query)
        derived_query = sql.SQL(", ").join(all_composed_expression)
        return derived_query

    def construct_order_by_query(self, order_by: list):
        order_by = sql.SQL(", ").join([sql.Identifier(n) for n in order_by])
        query = sql.SQL(" ORDER BY {order_by}").format(order_by=order_by)
        return query

    def construct_group_by_query(self, group_by: list):
        group_by = sql.SQL(", ").join([sql.Identifier(n) for n in group_by])
        query = sql.SQL(" GROUP BY {group_by}").format(group_by=group_by)
        return query

    def disconnect(self):
        self.cursor.close()
        self.conn.close()


def get_columns(order_by_fields: list[str], filters: dict[str, Any]):
    "get columns excluding those containing 'id' in their name."
    return (
        Column.active_objects.filter(**filters)
        .order_by(*order_by_fields)
        .exclude(name__icontains="id")
    )


def get_filtered_columns(order_by_fields: list[str], filters: dict[str, Any]):
    """
    get filtered columns excluding those tagged as 'DND' or 'SENSITIVE' or containing 'id' in their name.
    """
    return get_columns(order_by_fields, filters).exclude(
        Q(tags__name__iexact=DND) | Q(tags__name__iexact=SENSITIVE)
    )


def get_filtered_columns_list(order_by_fields: list[str], filters: dict[str, Any]):
    """
    Retrieve a list of names of active columns excluding those tagged as 'DND' or 'SENSITIVE' or containing 'id' in their name.
    """
    return get_filtered_columns(order_by_fields, filters).values_list("name", flat=True)


def get_sensitive_columns():
    """return all sensitive columns to filter AI model df"""
    return Column.objects.filter(tags__name=SENSITIVE).values_list("name", flat=True)


def get_tables(
    filters: dict[str, Any] | None = None, filter_query: FilterSchema = None
):
    """filter_query is used for search on a table"""
    q = filter_query.get_filter_expression()
    base_query = Table.active_objects.all()

    if filters:
        base_query = base_query.filter(**filters)

    if filter_query:
        base_query = base_query.filter(q)

    return base_query.distinct()


def get_tables_and_columns(db_schemas: list, datasource: DataSource):
    connect_db = ConnectDB
    result = []
    error = None
    try:
        db_info = connect_db(
            db_name=datasource.name,
            hostname=datasource.address,
            username=datasource.username,
            password=aes_gcm_cipher.decrypt(datasource.password),
            port=datasource.port,
        )
        for schema in db_schemas:
            dataset = Dataset.objects.create(name=schema, datasource=datasource)
            tables = db_info.get_db_tables(schema)

            table_names = []
            for row in tables:
                name = row.get("table_name")
                table_names.append(name)
                table = Table.objects.create(
                    dataset=dataset, name=name, alias=name
                )  # set alias to tablename as default

                column_info = db_info.get_db_columns(
                    table_schema=dataset.name, table_name=name
                )
                for row in column_info:
                    Column.objects.create(
                        name=row["column_name"], data_type=row["data_type"], table=table
                    )
            # generate metadata description for tables and columns
            generate_metadata.delay(schema, table_names)
            result.append(
                {
                    "dataset_identifier": dataset.identifier,
                    "name": dataset.name,
                    "tables": table_names,
                }
            )
    except psycopg2.Error as e:
        error = e

    db_info.disconnect()

    return result, error


def get_all(Model, **kwargs):
    """return all active objects in a named model"""
    return Model.active_objects.filter(**kwargs)


def get_datasets(**kwargs):
    return get_all(Dataset, **kwargs)


def get_rule_filters(**kwargs):
    return get_all(RuleFilter, **kwargs)


def get_rule_users(**kwargs):
    return get_all(RuleUser, **kwargs)


def get_tags(**kwargs):
    return get_all(Tag, **kwargs)


def get_rules(**kwargs):
    return get_all(Rule, **kwargs)


def get_applied_rules(table_id, user):
    rules = get_rules(table_id=table_id)
    """
       first, check if user is part of the rule users if not,
         check if any of user roles is part of assigned roles to rule. if not,
         assign the rule applied to everyone or general rule.
    """
    # Check if the user has direct access for all rules
    for rule in rules:
        if rule.user_has_access(user.id):
            return rule

    # Check if any of the user's roles grant access for all rules
    for rule in rules:
        if rule.role_has_access(user.user_group_list()):
            return rule

    # Check if the rule is applied to everyone or is a general rule for all rules
    for rule in rules:
        if rule.is_applied_to_everyone or rule.is_general:
            return rule

    # If no rules matched, user has no access to this table
    return None


def get_report_filters(table_id, user, filters=None):
    """this check if table has rule filters.
    if true, join rule filters with report filters
    """
    report_filters = {"filter": filters, "connector": "AND"} if filters else None
    # get rule applied to user
    rule = get_applied_rules(table_id, user)
    # rule = Rule.active_objects.filter(table_id=table_id).first()
    if rule:
        rule_filters = rule.filters
        group = [
            {"filter": rule_filter.filter, "connector": rule_filter.connector}
            for rule_filter in rule_filters
        ]
        # join the filters together
        if report_filters:
            group.append(report_filters)
        report_filters = group

    # convert a single filter to a list
    report_filters = (
        [report_filters] if isinstance(report_filters, dict) else report_filters
    )
    return report_filters


get_datasets_async = sync_to_async(get_datasets)
get_tables_async = sync_to_async(get_tables)
get_columns_async = sync_to_async(get_columns)
get_rule_filters_async = sync_to_async(get_rule_filters)
get_rule_users_async = sync_to_async(get_rule_users)
get_tags_async = sync_to_async(get_tags)
get_rules_async = sync_to_async(get_rules)


def pull_warehouses_data() -> tuple[dict, psycopg2.Error | None]:
    result = {}
    error = None
    connect_db = ConnectDB

    db_info = connect_db(
        db_name=settings.WH_DBNAME,
        hostname=settings.WH_ADDRESS,
        username=settings.WH_USERNAME,
        password=settings.WH_PASSWORD,
        port=settings.WH_PORT,
    )

    try:
        with open("query.json", "rb") as query_file:
            data = orjson.loads(query_file.read())

        result = {
            QueryJsonKey.CAPACITY: db_info.execute_query(
                data.get(QueryJsonKey.CAPACITY)
            ),
            QueryJsonKey.VALUE_CHAIN: db_info.execute_query(
                data.get(QueryJsonKey.VALUE_CHAIN)
            ),
            QueryJsonKey.PRODUCERS_REACHED: db_info.execute_query(
                data.get(QueryJsonKey.PRODUCERS_REACHED)
            ),
            QueryJsonKey.VOLUME_TRADED_TURNOVER: db_info.execute_query(
                data.get(QueryJsonKey.VOLUME_TRADED_TURNOVER)
            ),
            QueryJsonKey.WAREHOUSES: db_info.execute_query(
                data.get(QueryJsonKey.WAREHOUSES)
            ),
            QueryJsonKey.FARMERS_REACHED: db_info.execute_query(
                data.get(QueryJsonKey.FARMERS_REACHED)
            ),
        }

    except psycopg2.Error as e:
        error = e
    finally:
        db_info.disconnect()

    return result, error


def get_warehouses_data():
    result = get_from_cache("warehouse_data")
    error = None
    if result is None:
        result, error = pull_warehouses_data()
        if not error:
            add_to_cache(
                WAREHOUSE_DATA_CACHE_KEY,
                result,
                timeout=settings.WAREHOUSE_DATA_CACHE_TIMEOUT,
            )
    return result, error
