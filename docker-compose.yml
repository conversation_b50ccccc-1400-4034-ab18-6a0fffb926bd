version: "3"

services:
  web:
    restart: on-failure
    image: sherlock:latest
    env_file:
      - .env
    ports:
      - "4000:4000"
    # command: bash -c "mkdir -p staticfiles && python manage.py makemigrations && python manage.py migrate && python manage.py collectstatic --noinput && gunicorn -b 0.0.0.0:4000 -w 4 sherlock.wsgi:application"
    command: bash -c "gunicorn -b 0.0.0.0:4000 -w 4 --access-logfile - -k uvicorn.workers.UvicornWorker sherlock.asgi:application"
    volumes:
      - staticfiles:/home/<USER>/staticfiles/
      - ai:/home/<USER>/ai/migrations
      - accounts:/home/<USER>/accounts/migrations
      - self_service:/home/<USER>/self_service/migrations
      - dataset:/home/<USER>/dataset/migrations
    network_mode: "host"

  influxdb:
    image: influxdb:2.0.7
    env_file:
      - .env
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: ${INFLUXDB_USERNAME}
      DOCKER_INFLUXDB_INIT_PASSWORD: ${INFLUXDB_PASSWORD}
      DOCKER_INFLUXDB_INIT_ORG: ${INFLUXDB_ORG}
      DOCKER_INFLUXDB_INIT_BUCKET: ${INFLUXDB_BUCKET}
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: ${INFLUXDB_TOKEN}
    ports:
      - "8086:8086"
    volumes:
      - sherlock_influxdb:/var/lib/influxdb2

  redis:
    restart: unless-stopped
    image: redis:7.0.5-alpine
    command: --port 6382
    ports:
      - "6382:6382"
    volumes:
      - redis_data:/var/lib/redis_volume

  celery_worker:
    restart: on-failure
    image: sherlock:latest
    env_file:
      - .env
    command: bash -c "celery -A sherlock.celery worker -l info"
    depends_on:
      - web
    network_mode: "host"

  celery_beat:
    restart: on-failure
    image: sherlock:latest
    env_file:
      - .env
    command: bash -c "celery -A sherlock.celery beat -l info"
    depends_on:
      - web
    network_mode: "host"

  phoenix:
    image: arizephoenix/phoenix:version-8.13.2 # Must be greater than 4.0 version to work
    depends_on:
      - phoenix_backend_db
    ports:
      - 6006:6006  # PHOENIX_PORT
      - 4317:4317  # PHOENIX_GRPC_PORT
      - 9090:9090  # [Optional] PROMETHEUS PORT IF ENABLED
    environment:
      - PHOENIX_SQL_DATABASE_URL=postgresql://${PHOENIX_POSTGRES_USER}:${PHOENIX_POSTGRES_PASSWORD}@phoenix_backend_db:5432/${PHOENIX_POSTGRES_DB}
      - PHOENIX_ENABLE_AUTH=${PHOENIX_ENABLE_AUTH}
      - PHOENIX_SECRET=${PHOENIX_SECRET}
      - PHOENIX_USE_SECURE_COOKIES=True
      # - PHOENIX_HOST_ROOT_PATH="/observe"
      - PHOENIX_SQL_DATABASE_SCHEMA=${PHOENIX_SQL_DATABASE_SCHEMA}
  phoenix_backend_db:
    image: postgres:16
    restart: always
    environment:
      - POSTGRES_USER=${PHOENIX_POSTGRES_USER}
      - POSTGRES_PASSWORD=${PHOENIX_POSTGRES_PASSWORD}
      - POSTGRES_DB=${PHOENIX_POSTGRES_DB}
      - POSTGRES_PORT=${PHOENIX_POSTGRES_PORT}
      
    ports:
      - "${PHOENIX_POSTGRES_PORT}:5432"
    volumes:
      - phoenix_data:/var/lib/postgresql/data

volumes:
  sherlock_influxdb:
  staticfiles:
  ai:
  accounts:
  self_service:
  dataset:
  redis_data:
  phoenix_data:
    driver: local
