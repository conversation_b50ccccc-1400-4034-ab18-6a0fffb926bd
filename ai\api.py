import psycopg2
from django.conf import settings
from django.http import JsonResponse
from langchain_community.callbacks.manager import get_openai_callback
from ninja import Router
from ninja.pagination import paginate
from ninja.responses import codes_4xx
from openai import AuthenticationError, RateLimitError

from accounts.auth import Auth<PERSON><PERSON><PERSON>, async_auth_bearer
from ai.tasks import checkConditionalAlert, parsePeriodicAlert
from base import errors
from base.constants import SUCCESS
from base.perm_constants import CAN_VIEW_FEEDBACK, CAN_VIEW_PROMPT_lOG, permissions
from self_service.utils import async_serialize_queryset
from sherlock.pagination import Pagination
from sherlock.permissions import has_permissions
from sherlock.schemas import Error, ResponseWithData, Success

from .core import AICore, Sherlock
from .helpers import get_conversations_async
from .models import (
    Alert,
    AlertTypes,
    Conversation,
    Feedback,
    PromptLog,
    UserPrompt,
)
from .schemas import (
    AlertCreateSchema,
    AlertSchema,
    AlertUpdateSchema,
    ConversationSchema,
    ConversationUpdateSchema,
    ConvoPromptSchema,
    FeedbackInputSchema,
    FeedbackSchema,
    PromptLogSchema,
    RateUserPromptSchema,
    UserPromptFeedbackSchema,
    UserPromptInput,
    UserPromptOutput,
    formatUserPrompt,
)

router = Router(auth=AuthBearer())
sherlock = Sherlock()
# core_v2 initalization

model_core = AICore().build_workflow()

# model_core = AICore()


@router.post(
    "/query", response={200: Success, codes_4xx: Error}, auth=async_auth_bearer
)
@has_permissions(permissions.ai.prompts.access)
async def user_prompt(request, user_prompt: UserPromptInput):
    """
    - converts user prompt to sql (with chatGPT)
    - runs sql on workbench datawarehouse
    - stores data, prompt, sql in UserPrompt object
    - attaches UserPrompt to Conversation (creates one if not specified)
    - logs cost of chatGPT query

    on error:
    - creates a user prompt object with empty data and a comment
    """

    if settings.ENABLE_PROMPT:
        if not user_prompt.prompt:
            return 404, {"error": "Empty Prompt"}

        if len(user_prompt.prompt) > Sherlock.max_query_length:
            return 400, {
                "error": "Please limit your query to %s words"
                % Sherlock.max_query_length
            }

        with get_openai_callback() as cb:
            sql = data = None
            conversation = ""
            response = None
            try:
                if not user_prompt.conversation_id:
                    convo = await Conversation.objects.acreate(
                        user=request.auth
                    )  # , name=convo_name)
                    user_prompt.conversation_id = convo.id

                    convo_name, response = await model_core.new_chat(
                        user_prompt=user_prompt.prompt, converse_id=convo.id
                    )
                    convo.name = convo_name
                    await convo.asave(update_fields=["name"])
                else:
                    convo = await Conversation.objects.filter(
                        id=user_prompt.conversation_id, user=request.auth
                    ).afirst()
                    if not convo:
                        return 404, {"error": "Conversation not found"}
                    await convo.asave(
                        update_fields=["updated_at"]
                    )  # update the convo time when a new prompt is made to it

                    response = await model_core.a_chat(
                        user_prompt=user_prompt.prompt, converse_id=convo.id
                    )
            except AuthenticationError as ex:
                raise ex
            except RateLimitError as re:
                return 400, {"error": errors.RATE_LIMIT_MESSAGE, "detail": str(re)}
            except psycopg2.Error as psy_e:
                return 400, {"error": errors.PSYCOPG_ERROR, "detail": str(psy_e)}
            finally:
                if response is not None:
                    conversation = response.get("response")
                    data = response.get("data")
                    sql = response.get("sql_query")
                # further_question = response.get("next_question")

                if sql and data:
                    obj = await UserPrompt.objects.acreate(
                        sql=sql,
                        data=data,
                        prompt=user_prompt.prompt,
                        conversation=convo,
                        comment=conversation,
                        model_version=Sherlock.model_version,
                    )
                    await PromptLog.objects.acreate(
                        prompt=obj,
                        tokens=cb.total_tokens,
                        cost=cb.total_cost,
                    )
                elif sql:
                    obj = await UserPrompt.objects.acreate(
                        sql=sql,
                        data=data,
                        prompt=user_prompt.prompt,
                        conversation=convo,
                        comment=conversation,
                        model_version=Sherlock.model_version,
                    )
                    await PromptLog.objects.acreate(
                        prompt=obj,
                        tokens=cb.total_tokens,
                        cost=cb.total_cost,
                    )
                else:
                    obj = await UserPrompt.objects.acreate(
                        prompt=user_prompt.prompt,
                        conversation=convo,
                        model_version=Sherlock.model_version,
                        comment=conversation,
                    )
            return JsonResponse(formatUserPrompt(obj), safe=False)
    else:
        return 200, {"message": "This module is currently unavailable"}


@router.post("/query/refresh", response={200: UserPromptOutput, codes_4xx: Error})
def refresh_user_prompt(request, prompt_id: int):
    """
    - rerun prompt sql on workbench datawarehouse
    - updates fetched_at to current time
    """

    prompt = UserPrompt.objects.filter(id=prompt_id).first()
    if not prompt:
        return 404, {"error": "Prompt not found"}


@router.put("/prompt/{id}/feedback", response={200: Success, codes_4xx: Error})
def rate_user_prompt(request, id: int, data: RateUserPromptSchema):
    """
    - give prompt a rating from and optional comment
        INACCURATE = 1
        WRONG_CONTEXT = 2
        INCOMPLETE = 3
        GOOD = 4
        EXCELLENT = 5
    """
    prompt = UserPrompt.objects.filter(id=id).first()
    if not prompt:
        return 400, {"error": "Prompt not found"}

    prompt.rating = data.rating
    prompt.user_comment = data.comment
    prompt.save(update_fields=["rating", "user_comment"])
    return SUCCESS


@router.get("/prompt/feedback", response=list[UserPromptFeedbackSchema])
@paginate(Pagination)
@has_permissions([CAN_VIEW_FEEDBACK])
def get_user_prompt_feedback(request):
    return UserPrompt.objects.filter(rating__isnull=False).order_by("-created_at")


@router.get("/logs", response={200: list[PromptLogSchema]})
@paginate(Pagination)
@has_permissions([CAN_VIEW_PROMPT_lOG])
def get_prompt_logs(request):
    return PromptLog.objects.all()


@router.get("/conversations", response={200: ResponseWithData}, auth=async_auth_bearer)
@has_permissions(permissions.ai.prompts.access)
async def get_user_conversations(request):
    conversations = await get_conversations_async(
        order_by_fieldlines="updated_at", user=request.auth
    )
    return {
        "message": "Success",
        "data": await async_serialize_queryset(
            schema=ConversationSchema, qs=conversations
        ),
    }


@router.get(
    "/conversations/{id}", response={200: list[ConvoPromptSchema], codes_4xx: Error}
)
@has_permissions(permissions.ai.prompts.access)
@paginate(Pagination)
def get_conversation_prompts(request, id: str):
    convo = Conversation.active_objects.filter(id=id).first()
    if not convo:
        return 404, {"error": "Conversation not found"}

    if convo.user_id != request.auth.id:
        return 403, {"error": "You do not have permission to view this"}

    # return list of convo orderby the last updated conversation
    return convo.prompts.select_related("conversation").order_by("-updated_at")


@router.post("/conversations", auth=async_auth_bearer)
@has_permissions(permissions.ai.prompts.access)
async def create_conversation(request, data: ConversationUpdateSchema | None = None):
    convo = Conversation(user=request.auth)

    if data and data.name:
        convo.name = data.name

    await convo.asave()
    return {"conversation_id": convo.id}


@router.put(
    "/conversations",
    response={200: ConversationSchema, codes_4xx: Error},
    auth=async_auth_bearer,
)
@has_permissions(permissions.ai.prompts.access)
async def update_conversation(request, id: str, data: ConversationUpdateSchema):
    convo = await Conversation.active_objects.filter(id=id).afirst()
    if not convo:
        return 404, {"error": "Conversation not found"}

    if convo.user_id != request.auth.id:
        return 403, {"error": "You do not have permission to view this"}

    if data.name:
        convo.name = data.name

    await convo.asave()
    return convo


@router.delete(
    "/conversations", response={200: Success, codes_4xx: Error}, auth=async_auth_bearer
)
@has_permissions(permissions.ai.prompts.access)
async def delete_conversation(request, id: str):
    convo = await Conversation.active_objects.filter(id=id).afirst()
    if not convo:
        return 404, {"error": "Conversation not found"}

    await convo.asoft_delete()
    return SUCCESS


@router.get("/alerts", response={200: list[AlertSchema]})
def get_user_alerts(request):
    return (
        Alert.objects.filter(user=request.auth)
        .order_by("-created_at")
        .select_related("user_prompt")
    )


@router.post("/alerts", response={200: AlertSchema, codes_4xx: Error})
def create_alert(request, data: AlertCreateSchema):
    """
    - creates alert for a UserPrompt
        NOTE: UserPrompts can only have one alert
        also NOTE: Each user can have a maximum of 5 prompts

    - checks if alert configuration (config) is valid for its type

    """
    user_prompt = UserPrompt.objects.filter(id=data.prompt_id).first()
    if not user_prompt:
        return 404, {"error": "User prompt not found"}

    if hasattr(user_prompt, "alert") and user_prompt.alert is not None:
        return 400, {"error": "User prompt already has an alert"}

    if user_prompt.conversation.user != request.auth:
        return 403, {"error": "You do not have permission to create this"}

    if request.auth.alert_set.count() >= settings.MAX_USER_ALERT_COUNT:
        return 400, {"error": "You have reached your alert limit"}

    alert = Alert(
        user=request.auth,
        user_prompt=user_prompt,
        alert_type=data.type,
        config=data.config,
        description=data.description,
        day=data.day,
        hour=data.hour,
    )

    errorMsg = "Invalid alert configuration"

    if alert.alert_type == AlertTypes.PERIODIC:
        config = parsePeriodicAlert(alert)
        if not config:
            return 400, {"error": errorMsg}

        if (
            config.get("weeks", 0) or config.get("days", 0) or config.get("hours", 0)
        ) == 0:
            # return if no interval is set i.e.
            #   {"weeks": 0, "days": 0, "hours": 0, "minutes": 0}
            return 400, {"error": errorMsg}
    elif alert.alert_type == AlertTypes.CONDITIONAL:
        config = checkConditionalAlert(alert)
        if config is None:
            return 400, {"error": errorMsg}

    err = alert.save()
    if err:
        return 400, {"error": err}

    return alert


@router.put("/alerts", response={200: AlertSchema, codes_4xx: Error})
def update_alert(request, id: int, data: AlertUpdateSchema):
    alert = Alert.objects.filter(id=id).first()
    if not alert:
        return 400, {"error": "Alert not found"}

    if alert.user_id != request.auth.id:
        return 403, {"error": "You do not have permission to edit this"}

    # ? A serializer would be nice
    if data.type:
        alert.alert_type = data.type

    if data.is_active is not None:
        alert.is_active = data.is_active

    if data.config:
        alert.config = data.config

    if data.description:
        alert.description = data.description

    if data.hour:
        alert.hour = data.hour

    if data.day:
        alert.day = data.day

    err = alert.save()
    if err:
        return 400, {"error": err}

    return alert


@router.delete("/alerts", response={200: Success, codes_4xx: Error})
def delete_alert(request, id: int):
    alert = Alert.objects.filter(id=id).first()
    if not alert:
        return 400, {"error": "Alert not found"}

    if alert.user != request.auth:
        return 403, {"error": "You do not have permission to delete this"}

    # No need have is_deleted
    alert.delete()
    return SUCCESS


@router.post("/feedback", response={200: Success, 400: Error})
def give_feedback(request, data: FeedbackInputSchema):
    obj = Feedback(user=request.auth, text=data.text)
    obj.save()

    return SUCCESS


@router.get("/feedback", response={200: list[FeedbackSchema]})
@paginate(Pagination)
@has_permissions([CAN_VIEW_FEEDBACK])
def get_user_feedback(request):
    return Feedback.objects.all().order_by("-created_at")
