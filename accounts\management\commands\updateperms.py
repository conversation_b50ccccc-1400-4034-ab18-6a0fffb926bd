from django.contrib.auth.models import Group
from django.core.management.base import BaseCommand

from accounts.models import User


class Command(BaseCommand):
    help = "Update user group and permission"

    def handle(self, *args, **kwargs):
        user_group = Group.objects.get(name="USER")
        # update all user who does not have the default permissions yet
        users_list = User.objects.exclude(groups__name="USER").values_list(
            "id", flat=True
        )
        user_group.user_set.add(*users_list)
        self.stdout.write("User group updated successfully.")
