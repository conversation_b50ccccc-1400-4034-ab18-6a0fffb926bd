"""
WSGI config for sherlock project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

from .ssm_param import load_parameter_store

if os.environ.get("ENV_MODE", "dev") == "prod":
    load_parameter_store()
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "sherlock.settings")

application = get_wsgi_application()
