# Define constant variables here
from copy import deepcopy

API_KEY_HEADER_NAME = "X-API-KEY"

ACTUAL_API_KEY_HEADER_NAME = API_KEY_HEADER_NAME.lower()

custom_header_names = [
    ACTUAL_API_KEY_HEADER_NAME,
]

ISSUER = "ISSUER"
ACCESS_TOKEN_LIFETIME = "ACCESS_TOKEN_LIFETIME"  # nosec
HASH_ALG = "HS256"
SIGNING_KEY = "SIGNING_KEY"
REFRESH_TOKEN_LIFETIME = "REFRESH_TOKEN_LIFETIME"  # nosec
TOKEN = "TOKEN"  # nosec
ORG = "ORG"
BUCKET = "BUCKET"
HOST = "HOST"

SUCCESS = {"message": "Success"}

RESTRICTED_COLUMNS = ["phone", "phone_number", "first_name", "last_name", "address"]

RESET_PASSWORD_SUBJECT = "Reset Password Verification"  # nosec
SIGN_UP_SUBJECT = "Welcome to Sherlock"

AUTH_SCHEME: str = "bearer"
AUTH_HEADER: str = "Authorization"
ALLOWED_PHOTO_EXTENSION = ("png", "jpeg", "jpg")
AFEX_EMAIL = ("afexnigeria.com", "africaexchange.com")
HIGH = "High"
LOW = "Low"
MEDIUM = "Medium"
UPDATED_FIELDS = ["updated_at"]
DND = "Do not display"
SENSITIVE = "Sensitive"
EVERYONE = "Everyone"
INDIVIDUALS = "Individuals"
OWNER = "Owner"
CAN_VIEW = "can_view"
CAN_EDIT = "can_edit"
DAILY = "Daily"
WEEKLY = "Weekly"
MONTHLY = "Monthly"
CUSTOM = "Custom"
DNR = "Do not repeat"
UNIT = ["days", "weeks", "months"]
TO = "to"
CC = "cc"
BCC = "bcc"
SHARED_REPORT = "Shared Report"
SCHEDULED_DELIVERY = "Scheduled Delivery"
DATA_ACCESS = "Data Access"
ACCOUNT_UPDATE = "Account Update"

REFRL = "refrl"


def get_updated_fields():
    return deepcopy(UPDATED_FIELDS)


class QueryJsonKey:
    CAPACITY = "capacity"
    PRODUCERS_REACHED = "producers_reached"
    VALUE_CHAIN = "value_chain"
    VOLUME_TRADED_TURNOVER = "volume_traded_turnover"
    FARMERS_REACHED = "farmers_reached"
    WAREHOUSES = "warehouses"
