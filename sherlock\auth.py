from decouple import config
from ninja.security import APIKeyHeader

from base import constants
from utils.exceptions import MissingHeaderException


class HeaderApiKeyMixin:
    header_name = None

    def authenticate(self, request, key):
        is_key_valid = super().authenticate(request, key)
        if is_key_valid is not None and self.header_name in request.META.keys():
            return key
        raise MissingHeaderException(
            f"{self.header_name.replace('_', '-')[5:]} header is missing."
        )


class ApiKey(APIKeyHeader):
    param_name = constants.API_KEY_HEADER_NAME

    def authenticate(self, request, key):
        if key == config("API_KEY", cast=str):
            return key


header_key = ApiKey()
