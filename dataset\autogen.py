"""
Script Name: Auto-Gen Metadata
Author: <PERSON>
Created At: 2024-08-26
Description: This script is use to generate a table metadata for sherlock dataset managment.
"""

import json
import os
from urllib.parse import quote_plus

from decouple import config
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from sqlalchemy import MetaData, Table, create_engine, text
from sqlalchemy.sql import select
from tqdm.std import tqdm

BASE_PROMPT = """
You are seasoned Database Administrator at AFEX (Africa Exchange).
You understand that AFEX is an agro tech company that operates majorly in:

        1. Africa Commodity Exchange Limited (ACEL). This is a commodity trading platform - majorly cash crops
        2. Africa Investment Limited (AFIL)
        3. Africa Fair Trade Limited (AFTL). This is platform that deals directly with farmers, offering them loans as input in exchange for repayment with commodity

Task:
We want data consumers and stakeholders to have clear understanding of the tables and the database.
Use you understanding of AFEX operations to create a detail database documentation.

The Systems We have:
Workbench: which is the internal system for handling farmers activitie such as registering, giving out loans, approving reciept of goods from farmers etc
OVS (Order Validation System) - A validation system for the exchange platform. So this deals with validation information of all trading activities on the exchange platform
Exchange: This the commodity trading (exchange) system where different kinds of user individual, investor, and brokers can buy and sell securities (commodity).


Convention Used in our Database:
wb: Workbench
ovs: OVS
tr: Trade (Exchange)
cx: ComX - The old Exchange system

dim: Dimension - This means the table is a dimension table.
fact: Fact - This means the table is a Fact table.

For a clearer understanding 'wb_dim_crop' is a workbench crop dimension table.

\n
"""

metadata = MetaData()


def get_schema_tabs(schema, engine):
    # Define the table name (ensure it's validated/sanitized)
    table_name = "tables"
    schema_name = "information_schema"
    table = Table(table_name, metadata, schema=schema_name, autoload_with=engine)
    query = select(table.c.table_name).where(text("table_schema = :schema"))
    with engine.connect() as conn:
        results = conn.execute(query, {"schema": schema})
        schema_tabs = []
        for res in results:
            schema_tabs.append(res[0])
    return schema_tabs


def get_table_info(table_name, schema, engine):
    # Define the table name (ensure it's validated/sanitized)
    target_table = "columns"
    schema_name = "information_schema"
    table = Table(target_table, metadata, schema=schema_name, autoload_with=engine)
    query = select(
        table.c.column_name,
        table.c.data_type,
        table.c.is_nullable,
        table.c.column_default,
    ).where(text("table_schema = :schema and table_name = :table"))
    with engine.connect() as conn:
        results = conn.execute(query, {"schema": schema, "table": table_name})
        table_columns = results.fetchall()

    table_info = {"table_name": table_name, "columns": []}
    for column in tqdm(table_columns, desc="compiling column information"):
        column_info = {
            "column_name": column[0],
            "data_type": column[1],
            "is_nullable": column[2],
            "column_default": column[3],
        }
        table_info["columns"].append(column_info)

    return table_info


def generate_descriptions(table_info, llm):
    table_prompt_template = (
        BASE_PROMPT
        + """
    Given a table name and the columns it contains, generate an accurate yet concise description of this table. Don't include redundant information e.g mentioning the columns it contains:

    Table Name: {table_name}
    Columns:
    {columns}

    Return just the actual description

    Example Response:
    This table hold information on the validated trades on the exchange platform...

    Infer a more descriptive table name rather than using the raw table name provided directly
    Refer to the conventions to give clearer, less technical and user friendly table description
    """
    )
    column_prompt_template = (
        BASE_PROMPT
        + """
    Given a column name and it's table description, generate an accurate and concise description for this column named.
    Column Name: '{column_name}'
    Data type: {data_type}
    Nullable: {is_nullable}
    Default Value: {column_default}

    Parent Table Description: {table_name} - {table_desc}

    Note:
    * Infer user friendly table name and column names in your description.
    * Avoid use of raw column name of table name in the description. As an illustration of a column is 'student_id', then in your description you should say 'Student ID'
      Likewise if a table is 'wb_dim_crop' then you can say 'Workbench Crop Dimension'
    * Return just the actual description

    Example Response:
    This column contains the unique identifier of each element..
    """
    )

    table_name = table_info["table_name"]

    # Generate table description

    columns = table_info["columns"]
    columns_description = "\n".join(
        [
            f"- {col['column_name']} ({col['data_type']}, nullable: {col['is_nullable']}, default: {col['column_default']})"
            for col in table_info["columns"]
        ]
    )

    table_prompt = PromptTemplate(
        template=table_prompt_template, input_variables=["table_name", "columns"]
    )
    table_chain = table_prompt | llm
    table_description = table_chain.invoke(
        input={"table_name": table_name, "columns": columns_description}
    ).content.strip()

    table_info["description"] = table_description

    # Generate column descriptions
    for column in tqdm(columns, desc="Generating Column Description"):
        column_prompt = PromptTemplate(
            template=column_prompt_template,
            input_variables=[
                "column_name",
                "table_name",
                "data_type",
                "is_nullable",
                "column_default",
                "table_desc",
            ],
        )
        if "_airbyte_" in column["column_name"]:
            column["description"] = ""
            continue

        column_chain = column_prompt | llm
        column_description = column_chain.invoke(
            {
                "column_name": column["column_name"],
                "table_name": table_name,
                "data_type": column["data_type"],
                "is_nullable": column["is_nullable"],
                "column_default": column["column_default"],
                "table_desc": table_description,
            }
        ).content.strip()

        column["description"] = column_description
    return table_info


def save_metadata_to_json(metadata, updates, file_path):
    if updates is not None:
        metadata.append(updates)

        with open(file_path, "w") as json_file:
            json.dump(metadata, json_file, indent=4)
        return True


def load_existing_metadata(file_path):
    try:
        # Check if the file exists
        if os.path.exists(file_path):
            # Open and read the JSON file if it exists
            with open(file_path) as file:
                data = json.load(file)
        else:
            # If the file does not exist, start with an empty dictionary
            data = []
        return data
    except Exception as err:
        err.add_note("error while generating metadata")
        raise err


def is_table_exist(table_name, data):
    for tab in data:
        if table_name == tab["table_name"]:
            return True, tab

    return False, {}


def generate(configurations, schema, table_names=None):
    # Set up your OpenAI API key
    openai_api_key = config("OPENAI_API_KEY")
    conn = None

    llm = ChatOpenAI(openai_api_key=openai_api_key, model_name="gpt-4o-mini")
    try:
        host = configurations["hostname"]
        dbname = configurations["database_name"]
        password = quote_plus(configurations["password"])
        user = configurations["username"]
        port = configurations["port"]
        connection_string = (
            f"postgresql+psycopg2://{user}:{password}@{host}:{port}/{dbname}"
        )

        # Create the engine
        engine = create_engine(
            connection_string, connect_args={"options": f"-csearch_path={schema}"}
        )
        # conn = engine.connect()

        if table_names is None:
            table_names = get_schema_tabs(schema, engine)
        if not table_names:
            f"No table found for schema {schema}"
            return False
        if not isinstance(table_names, list | tuple):
            raise TypeError("tables_names must be sequence of table names")

        for table_name in table_names:
            file_name = f"{schema}_table_descriptions.json"
            pwd = os.getcwd()
            if "dataset" in pwd:
                base_dir = "metadata"
            else:
                base_dir = "dataset/metadata"
            if not os.path.exists(base_dir):
                os.mkdir(base_dir)
            file_path = os.path.join(base_dir, file_name)

            metadata = get_table_info(
                table_name=table_name, schema=schema, engine=engine
            )
            existing_metadata = load_existing_metadata(file_path=file_path)

            # Check if metadata exists for table
            exist, tab = is_table_exist(table_name, existing_metadata)
            if exist:
                print(f"found existing metadata for table {table_name}. Skipping")
                metadata_with_descriptions = tab
            else:
                metadata_with_descriptions = generate_descriptions(metadata, llm)
                status = save_metadata_to_json(
                    metadata=existing_metadata,
                    updates=metadata_with_descriptions,
                    file_path=file_path,
                )
                if status:
                    print(f"Metadata with descriptions has been saved to {file_path}")
        # return metadata_with_descriptions
    except Exception as error:
        error.add_note("Error while generating metadata")
        raise error

    finally:
        if conn is not None:
            conn.close()


if __name__ == "__main__":
    dbconfig = {
        "hostname": config("POSTGRES_ADDRESS"),
        "database_name": config("POSTGRES_DBNAME"),
        "password": config("POSTGRES_PASSWORD"),
        "username": config("POSTGRES_USERNAME"),
        "port": config("POSTGRES_PORT"),
    }
    generate(
        configurations=dbconfig, schema="sherlock", table_names=["ovs_dim_security"]
    )
