"""Author: <PERSON>
Created At:  2024-06-27
Description: This contains utility funtions for sherlock ai core
"""

import io
import json
import logging
import os
import time
from pathlib import Path
from typing import Annotated, TypedDict

from langgraph.graph.message import add_messages
from llama_index.core import (
    PromptTemplate,
    SQLDatabase,
    StorageContext,
    VectorStoreIndex,
    load_index_from_storage,
)
from llama_index.core.llms import ChatResponse
from llama_index.core.objects import ObjectIndex, SQLTableNodeMapping, SQLTableSchema
from llama_index.core.prompts.default_prompts import DEFAULT_TEXT_TO_SQL_PROMPT
from llama_index.core.query_pipeline import FnComponent, InputComponent
from llama_index.core.query_pipeline import QueryPipeline as QP
from llama_index.core.schema import TextNode
from llama_index.llms.openai import OpenAI as LlamaIndexOpenAI
from llama_index.postprocessor.rankgpt_rerank import RankGPTRerank
from PIL import Image
from sqlalchemy import MetaData, Table
from sqlalchemy.sql import select

metadata = MetaData()

# Get the current working directory
current_dir = os.getcwd()

# Create the path for the 'data' directory
base_dir = os.path.join(current_dir, "ai/metadata")


# Create the 'data' directory if it doesn't exist
os.makedirs(base_dir, exist_ok=True)

# Configure the logging settings
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(current_dir, "ai", "sherlock.log")),
        logging.StreamHandler(),
    ],
)


def log_event(message, err=True):
    """Logs the events of sherlock AI Core"""

    if not err:
        logging.info(message)
    else:
        logging.error(message)


def load_sherlock_summary():
    """Loads the base line sherlock table descriptions"""
    schema_summary_path = os.path.join(base_dir, "sherlock_table_summary.json")
    try:
        with open(schema_summary_path, encoding="utf-8") as dc:
            sherlock_summary = json.load(dc)
            return sherlock_summary
    except FileNotFoundError as fe:
        fe.add_note(
            "Make sure the sherlock_table_summary.json file is on the right folder"
        )
        raise fe


def load_full_definition():
    """Loads the A full schema metadata used used by the TTSQL Pipeline for RAG"""
    full_summary_path = os.path.join(base_dir, "full_schema_definition.json")
    with open(full_summary_path, encoding="utf-8") as fp:
        tables_summary = json.load(fp)
    return tables_summary


def save_graph_chart(graph):
    """Saves the graph structure to an image"""
    graph_path = os.path.join(base_dir, "chat_graph.png")
    img = graph.get_graph().draw_mermaid_png()
    img_obj = io.BytesIO(img)
    image = Image.open(img_obj)
    image.save(graph_path)


def duration(func):
    """A decorator to calculate the time taken for execution"""

    def calc_time(*args, **kwargs):
        """calculate time taken for execution"""
        start = time.time()
        res = func(*args, **kwargs)
        stop = time.time()
        log_event(f"Execution Time: {stop-start} seconds", err=False)
        return res

    return calc_time


def generate_full_desc(engine, current_prompt, sherlock_summary, sherlock_tabs):
    """Generate the full schema metadata for data persistency"""
    table_index_dir = "table_index_dir"
    abs_path = os.path.join(base_dir, table_index_dir)
    if not Path(abs_path).exists():
        log_event(
            "Table indices not found!!! This will take a while to complete", err=False
        )
        os.makedirs(abs_path)

    sql_database = SQLDatabase(engine)
    vector_index_dict = {}
    table_schema_objs = [
        (SQLTableSchema(table_name=tab_name, context_str=sherlock_summary[tab_name]))
        for tab_name in sherlock_tabs
    ]
    for table_name in sql_database.get_usable_table_names():
        log_event(f"Indexing rows in table: {table_name}", err=False)
        table_index_path = f"{table_index_dir}/{table_name}"
        tab_index_abs_path = os.path.join(base_dir, table_index_path)
        if not os.path.exists(tab_index_abs_path):
            # get all rows from table

            table = Table(table_name, metadata)
            query = select(table).limit(1000)

            with engine.connect() as conn:
                cursor = conn.execute(query)
                result = cursor.fetchall()
                row_tups = []
                for row in result:
                    row_tups.append(tuple(row))

            # index each row, put into vector store index
            nodes = [TextNode(text=str(t)) for t in row_tups]

            # put into vector store index (use OpenAIEmbeddings by default)
            index = VectorStoreIndex(nodes)

            # save index
            index.set_index_id("vector_index")
            index.storage_context.persist(tab_index_abs_path)
        else:
            # rebuild storage context
            storage_context = StorageContext.from_defaults(
                persist_dir=tab_index_abs_path
            )
            # load index
            index = load_index_from_storage(storage_context, index_id="vector_index")
        vector_index_dict[table_name] = index
    summary = {}
    for table_schema_obj in table_schema_objs:
        table_opt_context = " The table description is: "
        table_opt_context += sherlock_summary[table_schema_obj.table_name]
        table_info = table_opt_context
        table_row_context = "\nHere are some relevant example rows (values in the same order as columns above)\n"
        table_info += "\nIt contains the following columns:\n"
        table_info += sql_database.get_single_table_info(table_schema_obj.table_name)
        vector_store = vector_index_dict[table_schema_obj.table_name]
        vector_retriever = vector_store.as_retriever(similarity_top_k=2)
        relevant_nodes = vector_retriever.retrieve(current_prompt)

        for node in relevant_nodes:
            table_row_context += str(node.get_content()) + "\n"
        table_info += table_row_context
        summary[table_schema_obj.table_name] = table_info
    schema_def_path = os.path.join(base_dir, "full_schema_definition.json")

    try:
        with open(schema_def_path, "w", encoding="utf-8") as td:
            json.dump(summary, td, indent=4)
        log_event(f"schema description saved to {schema_def_path}", err=False)
    except Exception as er:
        er.add_note("Failed to generate full schema metadata")
        log_event(str(er))
        raise er


class State(TypedDict):
    """Creates the State Object for the conversation. This makes tracking of the conversation state"""

    messages: Annotated[list, add_messages]


class Text2SQL_V2:
    """Text to SQL class
    Parameters:
    ----------
        dbengine (sqlalchemy engine): engine to connect to the database
        sql_database (llama_index.core.SQLDatabase): A query engine binded to the sqlalchemy engine
        schema_tabs (list): List of all tables in the sherlock schema
        llm (llama_index.llms.openai.OpenAI): an instance of the llama_index.llms.OpenAI
    Attributes:
    -----------
        obj_retriever:
            The retirever for the vectore store holding the metadata
        correct_sql_prompt (llama_index.core.PromptTemplate):
            System prompt for the correction query pipeline
        inc_sql (str):
            incorrect sql query string. This is set when the calling the cqp
        error_msg (str):
            error message gotten when the inc_sql was executed
        qp (llama_index.core.query_pipeline.QueryPipeline):
            The query pipeline for generating sql statements
        cqp (llama_index.core.query_pipeline.QueryPipeline):
            Correction query pipeline for correcting incorrect sql queries

    Methods:
    --------
        _get_object_retriever():
            Creates the obj_retirever
        _parse_response_to_sql(self):
            extracts the SQL statements from the model response in the qp
        _parse_corrected_response_to_sql(self):
            Extract SQL statements from the model response in the qcp
        _init_txt2sql_prompt(self):
            Creates the txt2sql_prompt
        _init_correct_sql_prompt(self):
            Creates the correct_sql_prompt
        _set_correction_vars():
            This sets the inc_sql and error_msg attributes for the class
        _parse_corr_input():
            This parses the prompt for the cqp
        _get_table_context_and_rows_str():
            Gets the tables and row context to be used for generating sql query by the RAG
        build_pipeline()
            builds the qp
         build_correction_pipeline():
            builds the cqp
        run(query):
            Executes the qp to generate sql queries
        correct(self, query, incorrect_sql, error):
            Executes the cqp
    """

    def __init__(self, dbengine, schema_tabs, llm=None):
        self.dbengine = dbengine
        self.sql_database = SQLDatabase(self.dbengine)
        self.schema_tabs = schema_tabs
        self.sherlock_summary = load_sherlock_summary()
        self.llm = llm
        self.obj_retriever = None
        self.correct_sql_prompt = None
        self.inc_sql = None
        self.error_msg = None
        self.qp = None
        self.cqp = None

    def _get_object_retriever(self):
        """Gets the obj_retriever"""
        table_node_mapping = SQLTableNodeMapping(self.sql_database)
        table_schema_objs = [
            (
                SQLTableSchema(
                    table_name=tab_name, context_str=self.sherlock_summary[tab_name]
                )
            )
            for tab_name in self.schema_tabs
        ]  # add a SQLTableSchema for each table

        obj_index = ObjectIndex.from_objects(
            table_schema_objs,
            table_node_mapping,
            VectorStoreIndex,
        )
        reranker_top_n = 3
        reranker = RankGPTRerank(
            llm=LlamaIndexOpenAI(
                model="gpt-4o-mini",
                temperature=0.0,
            ),
            top_n=reranker_top_n,
            verbose=True,
        )

        obj_retriever = obj_index.as_retriever(
            similarity_top_k=10, node_postprocessors=[reranker]
        )
        self.obj_retriever = obj_retriever
        return obj_retriever

    def _parse_response_to_sql(self, response: ChatResponse) -> str:
        """Parse response to SQL."""
        response = response.message.content
        sql_query_start = response.find("SQLQuery:")
        if sql_query_start != -1:
            response = response[sql_query_start:]
            if response.startswith("SQLQuery:"):
                response = response[len("SQLQuery:") :]
        sql_result_start = response.find("SQLResult:")
        if sql_result_start != -1:
            response = response[:sql_result_start]
        sql_query = response.strip().strip("```").strip()
        return sql_query

    def _parse_corrected_response_to_sql(self, response: ChatResponse) -> str:
        """Parse corrected response to SQL."""
        response = response.message.content
        sql_query_start = response.find("Corrected SQLQuery:")
        if sql_query_start != -1:
            response = response[sql_query_start:]
            if response.startswith("Corrected SQLQuery:"):
                response = response[len("Corrected SQLQuery:") :]
        sql_result_start = response.find("SQLResult:")
        if sql_result_start != -1:
            response = response[:sql_result_start]
        sql_query = response.strip().strip("```").strip()
        return sql_query

    def _init_txt2sql_prompt(
        self,
    ):
        """Creates the txt2sql system prompt for the qp"""

        DEFAULT_TEXT_TO_SQL_PROMPT.template = """
        Given an input question, first create a syntactically correct {dialect} query to run, then look at the results of the query and return the answer. You can order the results by a relevant column to return the most interesting examples in the database.

        Never query for all the columns from a specific table, only ask for a few relevant columns given the question.

        Pay attention to use only the column names that you can see in the schema description. Be careful to not query for columns that do not exist. Pay attention to which column is in which table. Also, qualify column names with the table name when needed. Pay attention to currency when calculating aggregates relating to price
        You are required to use the following format, each taking one line:

        Question: Question here
        SQLQuery: SQL Query to run

        Only use tables listed below.
        {schema}

        Question: {query_str}
        SQLQuery:

        Note:
        When writing queries DO NOT use '=' to compare text values, instead use 'ILIKE'.

        e.g SELECT * FROM table WHERE column ILIKE '%value%'

        Except in the case of boolean or numerical values then:
            SELECT * FROM table WHERE column is TRUE
            SELECT * FROM table WHERE column = num_value
\


        """

        text2sql_prompt = DEFAULT_TEXT_TO_SQL_PROMPT.partial_format(
            dialect=self.dbengine.dialect.name
        )

        return text2sql_prompt

    def _init_correct_sql_prompt(self):
        """Creates the txt2sql correction prompt for the cqp"""
        CORRECT_SQL_PROMPT = DEFAULT_TEXT_TO_SQL_PROMPT
        CORRECT_SQL_PROMPT.template = """
        Given an incrorrectly generated {dialect} SQL query, examine and correct the query so it run succesfully..

        You are provided with the initial user question, the incorrect SQL query and the error returned from the database.

        Never query for all the columns from a specific table, only ask for a few relevant columns given the question.

        Pay attention to use only the column names that you can see in the schema description. Be careful to not query for columns that do not exist. Pay attention to which column is in which table. Also, qualify column names with the table name when needed. You are required to use the following format, each taking one line:

        Question: Question here
        Corrected SQLQuery: SQL Query to run


        Only use table information listed below.
        {schema}

        {cqp_input}
        Corrected SQLQuery:

        """
        CORRECT_SQL_PROMPT.template_vars = ["dialect", "schema", "cqp_input"]
        correct_sql_prompt = CORRECT_SQL_PROMPT.partial_format(
            dialect=self.dbengine.dialect.name,
            # incorrect_query=incorrect_query,
            # error=error
        )

        self.correct_sql_prompt = correct_sql_prompt
        return correct_sql_prompt

    def _set_correction_vars(self, inc_sql, error):
        if inc_sql and error:
            self.inc_sql = inc_sql
            self.error_msg = error
            return True
        return False

    def _parse_corr_input(self, query):
        cqpinput = PromptTemplate("""
        Initial Question: {query_str}
        Incorrect SQLQuery: {incorrect_query}\n
        Error Returned: ```{error_str}```
        """).format(
            incorrect_query=self.inc_sql, error_str=self.error_msg, query_str=query
        )
        return cqpinput

    def _get_table_context_and_rows_str(
        self, query_str: str, table_schema_objs: list[SQLTableSchema]
    ):
        """Get table context string."""
        try:
            tables_summary = load_full_definition()
        except FileNotFoundError:
            log_event(
                "full_schema_defination.json not found!!! \n generating a new file..",
                err=False,
            )
            generate_full_desc(
                self.dbengine, query_str, self.sherlock_summary, self.schema_tabs
            )
            tables_summary = load_full_definition()
        context_strs = []
        for table_schema_obj in table_schema_objs:
            context_strs.append(tables_summary[table_schema_obj.table_name])
        return "\n\n".join(context_strs)

    def build_pipeline(self):
        """Builds the qp"""
        sql_parser_component = FnComponent(fn=self._parse_response_to_sql)
        table_parser_component = FnComponent(fn=self._get_table_context_and_rows_str)
        text2sql_prompt = self._init_txt2sql_prompt()
        obj_retriever = self._get_object_retriever()

        if self.llm:
            llm = self.llm
        else:
            llm = LlamaIndexOpenAI(model="gpt-4o")
        qp = QP(
            modules={
                "input": InputComponent(),
                "table_retriever": obj_retriever,
                "table_output_parser": table_parser_component,
                "text2sql_prompt": text2sql_prompt,
                "text2sql_llm": llm,
                "sql_output_parser": sql_parser_component,
            },
            #    verbose=True,
        )

        qp.add_link("input", "table_retriever")
        qp.add_link("input", "table_output_parser", dest_key="query_str")
        qp.add_link(
            "table_retriever", "table_output_parser", dest_key="table_schema_objs"
        )
        qp.add_link("input", "text2sql_prompt", dest_key="query_str")
        qp.add_link("table_output_parser", "text2sql_prompt", dest_key="schema")
        qp.add_chain(["text2sql_prompt", "text2sql_llm", "sql_output_parser"])
        self.qp = qp
        return self

    def build_correction_pipeline(self):
        """Builds the cqp"""
        corrected_sql_parser_component = FnComponent(
            fn=self._parse_corrected_response_to_sql
        )
        table_parser_component = FnComponent(fn=self._get_table_context_and_rows_str)
        correct_sql_prompt = self._init_correct_sql_prompt()
        llm = LlamaIndexOpenAI(model="gpt-4o-mini")

        correction_qp = QP(
            modules={
                "input": InputComponent(),
                "table_retriever": self.obj_retriever,
                "table_output_parser": table_parser_component,
                "text2sql_prompt": correct_sql_prompt,
                "text2sql_llm": llm,
                "sql_output_parser": corrected_sql_parser_component,
            },
            #   verbose=True,
        )

        correction_qp.add_link("input", "table_retriever")
        correction_qp.add_link("input", "table_output_parser", dest_key="query_str")
        correction_qp.add_link(
            "table_retriever", "table_output_parser", dest_key="table_schema_objs"
        )
        correction_qp.add_link("input", "text2sql_prompt", dest_key="cqp_input")

        correction_qp.add_link(
            "table_output_parser", "text2sql_prompt", dest_key="schema"
        )
        correction_qp.add_chain(
            ["text2sql_prompt", "text2sql_llm", "sql_output_parser"]
        )

        self.cqp = correction_qp
        # log_event("SQL Correction Pipeline Created!!!", err=False)
        return correction_qp

    @duration
    def run(self, query):
        """This function executes the qp RAG pipeline to generate SQL
        Parameters
        ----------
            query: The prompt to generate sql from
        Returns
        --------
            return sql query"""
        response = self.qp.run(query=query)
        log_event(f"Initiatial Query: {response}")
        return response.strip().lstrip("sql")

    @duration
    def correct(self, query, incorrect_sql, error):
        """Executes the sql correction pipeline.
        This function is called when the query pipline generates an incorrect SQL query
        Parameters
        ----------
            query: User prompt
            incorrect_sql: The incorrect_sql to be corrected
            error: The error message when the incorrect sql was exected against the db"""
        status = self._set_correction_vars(inc_sql=incorrect_sql, error=error)
        if status:
            cqpinput = self._parse_corr_input(query)
            response = self.cqp.run(query=cqpinput)
            return response.strip().lstrip("sql")
        else:
            raise ValueError(
                "Correction Variables not set. Please provide correction variables to correct the generated query"
            )
