import re

from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _


class MinimumCharacterTypeValidator:
    def validate(self, password, user=None):
        if not re.search("[A-Z]", password):
            raise ValidationError(
                _("Password must contain at least one uppercase letter, A-Z."),
                code="password_no_upper_case",
            )

        if not re.search("[a-z]", password):
            raise ValidationError(
                _("Password must contain at least one lowercase letter, a-z."),
                code="password_no_lower_case",
            )

        if not re.search("[~!@#$%^&*()_+{}\\:\\[\\]]", password):
            raise ValidationError(
                _("Password must contain at least one symbol"),
                code="password_no_symbol",
            )

        if not re.search(r"\d", password):
            raise ValidationError(
                _("Password must contain at least one digit, 0-9."),
                code="password_no_digit",
            )

        return None

    def get_help_text(self):
        return _(
            "Your password must contain at least one uppercase letter, one lowercase, one digit and one non-alphanumeric character."
        )
