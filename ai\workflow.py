"""
Script Name: TextToSQL Workflow
Author: <PERSON>
Created At: 2024-08-26
Description: This script contains implementation for text to sql workflow
"""

from typing import Annotated, TypedDict

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.output_parsers import JsonOutputParser
from langgraph.graph.message import add_messages
from llama_index.core import PromptTemplate, Settings, VectorStoreIndex
from llama_index.core.llms import ChatResponse
from llama_index.core.prompts.default_prompts import DEFAULT_TEXT_TO_SQL_PROMPT
from llama_index.core.query_pipeline import FnComponent, InputComponent
from llama_index.core.query_pipeline import Query<PERSON><PERSON>eline as QP
from llama_index.core.schema import NodeWithScore
from llama_index.vector_stores.supabase import SupabaseVectorStore
from pydantic import BaseModel, Field

from ai.prompts import SQL_CORRECTION_PROMPT, TEXT_TO_SQL_PROMPT
from ai.settings import SherlockSettings
from ai.utils import duration, generate_query_hash
from utils.cache_utils import get_from_cache, set_cache

Settings.llm = SherlockSettings.ragllm
Settings.embed_model = SherlockSettings.embed_model


class State(TypedDict):
    """Creates the State Object for the conversation. This allows tracking of the conversation state"""

    messages: Annotated[list, add_messages]
    data: dict | list
    current_message: HumanMessage
    sql_queries: str | tuple[str, str]
    further_questions: str
    system_initialized: bool
    tool_calls: list[AIMessage]
    metadata: dict


class Explain(BaseModel):
    insight: str = Field(description="Provide explanation and analysis of the result")
    follow_up: str = Field(description="Suggest a further question")


class Planner:
    def __init__(self, llm, prompt):
        self.llm = llm
        self.prompt = prompt
        self.planner = self.prompt | self.llm | JsonOutputParser()

    def plan(self, question):
        return self.planner.invoke(question)

    def make_tool_func(self):
        def plan_func(prompt):
            """Generates a plan for the given prompt using the planner.
            This is used to generate a plan for the SQL query."""

            query_plan = self.plan(query=prompt)
            SherlockSettings.logger.info(f"Planner Input: {prompt}")
            return query_plan

        return plan_func


class Text2SQL_V3:
    """Text to SQL class
    Parameters:
    ----------
        dbengine (sqlalchemy engine): engine to connect to the database
        sql_database (llama_index.core.SQLDatabase): A query engine binded to the sqlalchemy engine
        schema_tabs (list): List of all tables in the sherlock schema
        llm (llama_index.llms.openai.OpenAI): an instance of the llama_index.llms.OpenAI
    Attributes:
    -----------
        obj_retriever:
            The retirever for the vectore store holding the metadata
        correct_sql_prompt (llama_index.core.PromptTemplate):
            System prompt for the correction query pipeline
        inc_sql (str):
            incorrect sql query string. This is set when the calling the cqp
        error_msg (str):
            error message gotten when the inc_sql was executed
        qp (llama_index.core.query_pipeline.QueryPipeline):
            The query pipeline for generating sql statements
        cqp (llama_index.core.query_pipeline.QueryPipeline):
            Correction query pipeline for correcting incorrect sql queries

    Methods:
    --------
        _get_object_retriever():
            Creates the obj_retirever
        _parse_response_to_sql(self):
            extracts the SQL statements from the model response in the qp
        _parse_corrected_response_to_sql(self):
            Extract SQL statements from the model response in the qcp
        _init_txt2sql_prompt(self):
            Creates the txt2sql_prompt
        _init_correct_sql_prompt(self):
            Creates the correct_sql_prompt
        _set_correction_vars():
            This sets the inc_sql and error_msg attributes for the class
        _parse_corr_input():
            This parses the prompt for the cqp
        _get_table_context_and_rows_str():
            Gets the tables and row context to be used for generating sql query by the RAG
        build_pipeline()
            builds the qp
         build_correction_pipeline():
            builds the cqp
        run(query):
            Executes the qp to generate sql queries
        correct(self, query, incorrect_sql, error):
            Executes the cqp
    """

    def __init__(self, llm=None, dbengine=None):
        self.llm = llm
        self.obj_retriever = None
        self.correct_sql_prompt = None
        self.inc_sql = None
        self.error_msg = None
        self.qp = None
        self.cqp = None
        self.dbengine = dbengine
        self.user_msg = None

    def _get_object_retriever(self):
        """Gets the obj_retriever"""

        vector_store = SupabaseVectorStore(
            collection_name=SherlockSettings.COLLECTION_NAME,
            postgres_connection_string=SherlockSettings.connection_string,
        )
        stored_index = VectorStoreIndex.from_vector_store(vector_store)

        # reranker_top_n = 3
        # reranker = RankGPTRerank(
        #     llm=LlamaIndexOpenAI(
        #         model="gpt-4o-mini",
        #         temperature=0.0,
        #     ),
        #     top_n=reranker_top_n,
        #     verbose=True,
        # )

        retriever = stored_index.as_retriever(
            similarity_top_k=5
            # node_postprocessors=[reranker]
        )
        self.obj_retriever = retriever
        return retriever

    def _parse_response_to_sql(self, response: ChatResponse) -> str:
        """Parse response to SQL."""
        response = response.message.content
        sql_query_start = response.find("SQLQuery:")
        if sql_query_start != -1:
            response = response[sql_query_start:]
            if response.startswith("SQLQuery:"):
                response = response[len("SQLQuery:") :]
        sql_result_start = response.find("SQLResult:")
        if sql_result_start != -1:
            response = response[:sql_result_start]
        sql_query = response.strip().strip("```").strip()
        return sql_query

    def _parse_corrected_response_to_sql(self, response: ChatResponse) -> str:
        """Parse corrected response to SQL."""
        response = response.message.content

        sql_query_start = response.find("Corrected SQLQuery:")
        if sql_query_start != -1:
            response = response[sql_query_start:]
            if response.startswith("Corrected SQLQuery:"):
                response = response[len("Corrected SQLQuery:") :]

        sql_result_start = response.find("SQLResult:")
        if sql_result_start != -1:
            response = response[:sql_result_start]

        sql_query = response.strip().strip("```").strip()
        return sql_query

    def _init_txt2sql_prompt(
        self,
    ):
        """Creates the txt2sql system prompt for the qp"""

        DEFAULT_TEXT_TO_SQL_PROMPT.template = TEXT_TO_SQL_PROMPT
        text2sql_prompt = DEFAULT_TEXT_TO_SQL_PROMPT.partial_format(
            dialect=self.dbengine.dialect.name
        )

        return text2sql_prompt

    def _init_correct_sql_prompt(self):
        """Creates the txt2sql correction prompt for the cqp"""
        CORRECT_SQL_PROMPT = DEFAULT_TEXT_TO_SQL_PROMPT
        CORRECT_SQL_PROMPT.template = SQL_CORRECTION_PROMPT
        CORRECT_SQL_PROMPT.template_vars = ["dialect", "schema", "cqp_input"]
        correct_sql_prompt = CORRECT_SQL_PROMPT.partial_format(
            dialect=self.dbengine.dialect.name
        )

        self.correct_sql_prompt = correct_sql_prompt
        return correct_sql_prompt

    def _set_correction_vars(self, inc_sql, error):
        if inc_sql and error:
            self.inc_sql = inc_sql
            self.error_msg = error
            return True
        return False

    def _parse_corr_input(self, query):
        cqpinput = PromptTemplate("""
        Initial Question: {query_str}
        Incorrect SQLQuery: {incorrect_query}\n
        Error Returned: ```{error_str}```
        """).format(
            incorrect_query=self.inc_sql, error_str=self.error_msg, query_str=query
        )
        return cqpinput

    def _parse_table_node_context_str(
        self,
        table_schema_nodes: list[NodeWithScore],  # Change this
    ):
        """Get table context string."""

        context_strs = []
        for table_schema_node in table_schema_nodes:
            context_strs.append(table_schema_node.text)
        return "\n\n".join(context_strs)

    def build_pipeline(self):
        """Builds the qp"""
        sql_parser_component = FnComponent(fn=self._parse_response_to_sql)
        table_parser_component = FnComponent(fn=self._parse_table_node_context_str)
        text2sql_prompt = self._init_txt2sql_prompt()
        obj_retriever = self._get_object_retriever()

        llm = SherlockSettings.ragllm

        qp = QP(
            modules={
                "input": InputComponent(),
                "table_retriever": obj_retriever,
                "table_output_parser": table_parser_component,
                "text2sql_prompt": text2sql_prompt,
                "text2sql_llm": llm,
                "sql_output_parser": sql_parser_component,
            },
            #    verbose=True,
        )

        qp.add_link("input", "table_retriever")
        qp.add_link(
            "table_retriever", "table_output_parser", dest_key="table_schema_nodes"
        )
        qp.add_link("input", "text2sql_prompt", dest_key="query_str")
        qp.add_link("table_output_parser", "text2sql_prompt", dest_key="schema")
        qp.add_chain(["text2sql_prompt", "text2sql_llm", "sql_output_parser"])
        self.qp = qp
        return self

    def build_correction_pipeline(self):
        """Builds the cqp"""
        corrected_sql_parser_component = FnComponent(
            fn=self._parse_corrected_response_to_sql
        )
        table_parser_component = FnComponent(fn=self._parse_table_node_context_str)
        correct_sql_prompt = self._init_correct_sql_prompt()

        llm = SherlockSettings.review_llm

        correction_qp = QP(
            modules={
                "input": InputComponent(),
                "table_retriever": self.obj_retriever,
                "table_output_parser": table_parser_component,
                "text2sql_prompt": correct_sql_prompt,
                "text2sql_llm": llm,
                "sql_output_parser": corrected_sql_parser_component,
            },
            #   verbose=True,
        )

        correction_qp.add_link("input", "table_retriever")
        correction_qp.add_link(
            "table_retriever", "table_output_parser", dest_key="table_schema_nodes"
        )
        correction_qp.add_link("input", "text2sql_prompt", dest_key="cqp_input")

        correction_qp.add_link(
            "table_output_parser", "text2sql_prompt", dest_key="schema"
        )
        correction_qp.add_chain(
            ["text2sql_prompt", "text2sql_llm", "sql_output_parser"]
        )

        self.cqp = correction_qp
        return correction_qp

    @duration
    def run(self, query):
        """This function executes the qp RAG pipeline to generate SQL
        Parameters
        ----------
            query: The prompt to generate sql from
        Returns
        --------
            return sql query"""

        user_query_hash = generate_query_hash(query)
        cached_query = get_from_cache(user_query_hash)
        if cached_query:
            SherlockSettings.logger.info("Fetched cached Query")
            self.user_msg = query
            response = cached_query
        else:
            self.user_msg = query
            response = self.qp.run(query=query)

            # Cache response

            set_cache(user_query_hash, response)
            # SherlockSettings.logger.info("Initiatial Query: %s " % (response))
        return response.strip().lstrip("sql")

    @duration
    def correct(self, incorrect_sql, error):
        """Executes the sql correction pipeline.
        This function is called when the query pipline generates an incorrect SQL query
        Parameters
        ----------
            query: User prompt
            incorrect_sql: The incorrect_sql to be corrected
            error: The error message when the incorrect sql was exected against the db"""
        status = self._set_correction_vars(inc_sql=incorrect_sql, error=error)
        if status:
            cqpinput = self._parse_corr_input(self.user_msg)
            response = self.cqp.run(query=cqpinput)

            user_query_hash = generate_query_hash(self.user_msg)
            set_cache(user_query_hash, response)

            return response.strip().lstrip("sql")
        else:
            raise ValueError(
                "Correction Variables not set. Please provide correction variables to correct the generated query"
            )

    def make_tool_func(self, planner=None):
        if planner:

            def gen_sql(prompt):
                """returns the sql query generated the TTSQL"""
                SherlockSettings.logger.info(f"TTSQL Input: {prompt}")
                plan = planner.plan(prompt)
                sql_query = self.run(query=plan)
                return sql_query
        else:

            def gen_sql(prompt):
                """returns the sql query generated the TTSQL"""
                # query = get_from_cache(prompt)
                # if query:
                #     return query
                SherlockSettings.logger.info(f"TTSQL Input: {prompt}")
                sql_query = self.run(query=prompt)

                # set_cache(prompt, sql_query)
                return sql_query

        return gen_sql
