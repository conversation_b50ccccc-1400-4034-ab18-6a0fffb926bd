import uuid

from asgiref.sync import sync_to_async
from django.contrib.auth.models import AbstractUser, Group
from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from django.db import models

from base.choices import Category
from base.constants import AFEX_EMAIL
from base.models import BaseModel

from .managers import CustomUserManager


class User(AbstractUser, BaseModel):
    username = None
    email = models.EmailField(
        verbose_name="email address",
        max_length=255,
        unique=True,
    )
    identifier = models.UUIDField(default=uuid.uuid4, editable=False, db_index=True)
    first_name = models.CharField(max_length=255, default="", db_index=True)
    last_name = models.CharField(max_length=255, default="", db_index=True)
    designation = models.CharField(max_length=255, blank=True, null=True)
    photo = models.ImageField(
        max_length=255, upload_to="profile pic", null=True, blank=True
    )
    tribe = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    email_aliases = models.JSONField(default=dict, null=True, blank=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)

    # 2fa
    is_two_factor_auth_enabled = models.BooleanField(default=True)
    has_set_up_two_factor_auth = models.BooleanField(default=False)
    two_factor_auth_secret = models.CharField(max_length=50, blank=True, null=True)
    is_post_login = models.BooleanField(default=False)

    is_locked_out = models.BooleanField(default=False)
    locked_out_till = models.DateTimeField(blank=True, null=True)

    is_admin = models.BooleanField(default=False)
    last_suspended_at = models.DateTimeField(null=True, blank=True)
    USERNAME_FIELD = "email"

    REQUIRED_FIELDS = []  # type: ignore

    objects = CustomUserManager()

    class Meta:
        permissions = (("can_view_user_groups", "Can view user groups"),)

    def __str__(self):
        return self.email

    def deactivate(self):
        self.is_active = False
        self.save(update_fields=["is_active"])

    async def adeactivate(self):
        return await sync_to_async(self.deactivate)()

    def activate(self):
        self.is_active = True
        self.save(update_fields=["is_active"])

    async def a_activate(self):
        return await sync_to_async(self.activate)()

    def set_two_factor_auth(self):
        self.has_set_up_two_factor_auth = True
        self.save(update_fields=["has_set_up_two_factor_auth"])

    def update_post_login(self, val: bool):
        self.is_post_login = val
        self.save(update_fields=["is_post_login"])

    def is_afex_staff(self):
        return self.email.endswith(AFEX_EMAIL)

    def user_group_list(self):
        return self.groups.values_list("id", flat=True)


class UserManagedModel(BaseModel):
    created_by = models.ForeignKey(
        User, on_delete=models.DO_NOTHING, related_name="%(class)s_created_by"
    )
    last_updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="%(class)s_updated_by"
    )

    class Meta:
        abstract = True

    @property
    def get_created_by(self):
        return self.created_by.get_full_name().title()


class UserLockOut(BaseModel):
    user = models.ForeignKey(User, on_delete=models.DO_NOTHING)
    request_data = models.JSONField(encoder=DjangoJSONEncoder)
    is_unlocked = models.BooleanField(default=False)
    lock_out_duration = models.IntegerField()

    def __str__(self):
        return self.user.email


class Notification(BaseModel):
    message = models.TextField()
    user = models.ForeignKey(
        User, on_delete=models.DO_NOTHING, related_name="notifications"
    )
    is_read = models.BooleanField(default=False)
    category = models.CharField(max_length=50, choices=Category.choices)

    def __str__(self):
        return self.message


class Role(Group, UserManagedModel):
    def __str__(self):
        return self.name
