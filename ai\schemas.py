from ninja import ModelSchema, Schema

from .models import (
    Alert,
    AlertTypes,
    Conversation,
    Feedback,
    PromptLog,
    UserPrompt,
)


class UserPromptInput(Schema):
    prompt: str | None = None
    conversation_id: str | None = None


class UserPromptOutput(ModelSchema):
    class Meta:
        model = UserPrompt
        fields = [
            "id",
            "sql",
            "data",
            "comment",
            "prompt",
            "rating",
            "user_comment",
            "model_version",
            "created_at",
            "fetched_at",
        ]


def formatUserPrompt(prompt: UserPrompt) -> dict:
    """
    Not a schema because pandas dataframe does not convert int keys to string
    which causes errors
    """

    data = {}
    for key in UserPromptOutput.Meta.fields:
        data[key] = getattr(prompt, key)

    data["conversation_id"] = prompt.conversation.id
    data["conversation_name"] = prompt.conversation.name

    return data


class ConvoPromptSchema(ModelSchema):
    conversation_name: str | None = None

    class Meta:
        model = UserPrompt
        fields = "__all__"


class RateUserPromptSchema(Schema):
    rating: UserPrompt.RatingChoices
    comment: str | None = None


class UserPromptFeedbackSchema(ModelSchema):
    class Meta:
        model = UserPrompt
        fields = ["id", "prompt", "rating", "user_comment", "sql", "data"]


class ConversationSchema(ModelSchema):
    class Meta:
        model = Conversation
        fields = ["id", "name", "created_at", "updated_at"]


class ConversationUpdateSchema(Schema):
    name: str | None = None


class AlertSchema(ModelSchema):
    class Meta:
        model = Alert
        fields = [
            "id",
            "config",
            "description",
            "is_active",
            "alert_type",
            "user_prompt",
            "day",
            "hour",
        ]


class AlertCreateSchema(Schema):
    prompt_id: int
    type: AlertTypes
    config: str
    description: str | None = None
    hour: int | None = None
    day: int | None = None


class AlertUpdateSchema(Schema):
    type: AlertTypes | None = None
    is_active: bool | None = None
    config: str | None = None
    description: str | None = None
    hour: int | None = None
    day: int | None = None


class PromptLogSchema(ModelSchema):
    prompt: str

    class Meta:
        model = PromptLog
        fields = [
            "tokens",
            "cost",
            "created_at",
        ]

    @staticmethod
    def resolve_prompt(obj):
        return obj.prompt.prompt


class FeedbackInputSchema(Schema):
    text: str


class FeedbackSchema(ModelSchema):
    class Meta:
        model = Feedback
        fields = ["id", "user", "text", "created_at"]
