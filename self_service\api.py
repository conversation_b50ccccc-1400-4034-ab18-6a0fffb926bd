import json
from uuid import UUID

import pandas as pd
import psycopg2
from asgiref.sync import sync_to_async
from django.db.models import F, OuterRef, Subquery
from ninja import Router
from ninja.responses import codes_4xx
from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError

from accounts.auth import Auth<PERSON><PERSON><PERSON>, async_auth_bearer
from ai.credentials import db_connect
from ai.models import UserPrompt
from base.choices import Category, RecipientTypes, ShareOptions
from base.constants import get_updated_fields
from base.messages import ResponseMessages
from base.perm_constants import permissions
from dataset.helpers import ConnectDB as connect_db
from dataset.helpers import (
    get_applied_rules,
    get_filtered_columns,
    get_filtered_columns_list,
    get_report_filters,
)
from dataset.models import DataSource, Rule, Table
from dataset.schemas import ColumnSchema
from sherlock.permissions import has_permissions
from sherlock.schemas import Error, ResponseWithData, Success
from utils.aes import aes_gcm_cipher

from .helpers import (
    create_shared_report,
    get_all_reports_async,
    get_charts_async,
    get_collections_async,
    get_report_details,
    get_reports_async,
    get_user_or_public_reports_async,
    validate_recipients,
)
from .models import (
    Chart,
    Collection,
    Recipient,
    Report,
    ReportColumn,
    ScheduleReport,
    SharedReportUser,
)
from .schemas import (
    ChartCreateSchema,
    ChartDataSchema,
    ChartDisplaySchema,
    ChartPreviewSchema,
    ChartSchema,
    ChartUpdateSchema,
    CollectionCreateSchema,
    CollectionSchema,
    CollectionUpdateSchema,
    PreviewSchema,
    ReportChartRespSchema,
    ReportCreateSchema,
    ReportPreviewSchema,
    ReportRespSchema,
    ReportSchema,
    ReportUpdateSchema,
    ScheduleListSchema,
    ScheduleReportResp,
    ScheduleReportSchema,
    SharedReportSchema,
)
from .utils import (
    async_serialize_queryset,
    async_serialize_single_obj,
    create_periodic_task,
    edit_periodic_task,
    serialize_single_obj,
    update_recipients,
)

source_engine, _ = db_connect()

router = Router(auth=async_auth_bearer, tags=["self-service"])


@router.get("/public-collections", response={200: ResponseWithData})
@has_permissions(permissions.self_service.collection.view)
async def get_public_collections(request):
    """endpoint for getting public collections"""
    collections = await get_collections_async(is_private=False)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=CollectionSchema, qs=collections),
    }


@router.get("/collections", response={200: ResponseWithData})
@has_permissions(permissions.self_service.collection.view)
async def get_user_collections(request):
    """endpoint for getting user collections"""
    collections = await get_collections_async(user=request.auth)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=CollectionSchema, qs=collections),
    }


@router.get(
    "/collections/{identifier}", response={200: ResponseWithData, codes_4xx: Error}
)
@has_permissions(permissions.self_service.collection.view)
async def get_collection(request, identifier: UUID):
    """endpoint for get a single collection"""
    collection = await Collection.active_objects.filter(identifier=identifier).afirst()
    if not collection:
        return 404, {"error": "Collection does not exist"}
    return {
        "message": "Success",
        "data": serialize_single_obj(schema=CollectionSchema, qs=collection),
    }


@router.post("/collections", response={200: ResponseWithData})
@has_permissions(permissions.self_service.collection.create)
async def create_collection(request, data: CollectionCreateSchema):
    payload = data.dict()
    chart_identifiers = payload.pop("chart_identifiers")

    collection = await Collection.objects.acreate(
        **payload,
        user=request.auth,
    )
    if chart_identifiers:
        await Chart.active_objects.filter(identifier__in=chart_identifiers).aupdate(
            collection=collection
        )
    return {
        "message": "New Collection added successfully",
        "data": serialize_single_obj(schema=CollectionSchema, qs=collection),
    }


@router.patch(
    "/collections/{identifier}/update",
    response={200: ResponseWithData, codes_4xx: Error},
)
@has_permissions(permissions.self_service.collection.edit)
async def update_collection(request, identifier: UUID, data: CollectionUpdateSchema):
    """
    - update collection
    - checks if collection owner is user
    """
    collection = await Collection.active_objects.filter(identifier=identifier).afirst()
    if collection is None:
        return 404, {"error": "Collection not found"}

    payload = data.dict(exclude_unset=True)
    field_names = get_updated_fields()  # list of updated fields
    for field_name, value in payload.items():
        setattr(collection, field_name, value)
        field_names.append(field_name)

    await collection.asave(update_fields=field_names)
    return {
        "message": "Collection updated successfully",
        "data": serialize_single_obj(schema=CollectionSchema, qs=collection),
    }


@router.delete(
    "/collections/{identifier}/delete", response={200: Success, codes_4xx: Error}
)
@has_permissions(permissions.self_service.collection.delete)
async def delete_collection(request, identifier: UUID):
    """
    Delete collection
    """
    collection = await Collection.active_objects.filter(
        identifier=identifier, user=request.auth
    ).afirst()
    if not collection:
        return 404, {"error": "Collection does not exist"}

    # check if there's chart in a collection
    if await collection.charts.acount() != 0:
        return 400, {
            "error": "Collection has active charts. Collection cannot be deleted."
        }

    await collection.asoft_delete()
    return {"message": "Collection deleted successfully"}


# CHARTS


@router.get("/public-charts", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions(
    permissions.self_service.reports.view
)  # charts are tied to permissions
async def get_public_charts(request):
    """get all public charts"""
    charts = await get_charts_async(is_private=False)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=ChartSchema, qs=charts),
    }


@router.get("/charts", response={200: ResponseWithData})
@has_permissions(permissions.self_service.reports.view)
async def get_user_charts(request):
    """endpoint for getting user charts"""
    charts = await get_charts_async(user=request.auth)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=ChartSchema, qs=charts),
    }


@router.get("/charts/{identifier}", response={200: ResponseWithData, 404: Error})
@has_permissions(permissions.self_service.reports.view)
async def get_chart(request, identifier: UUID):
    chart = (
        await Chart.active_objects.filter(identifier=identifier)
        .select_related("prompt")
        .afirst()
    )
    if not chart:
        return 404, {"error": "Chart does not exist"}
    return {
        "message": "Success",
        "data": await async_serialize_single_obj(schema=ChartDisplaySchema, qs=chart),
    }


@router.post("/charts", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions(permissions.self_service.reports.create)
async def create_chart(request, payload: ChartCreateSchema):
    """endpoint to create a chart with report or pin a chart"""
    if (
        payload.prompt_id
        and not await UserPrompt.active_objects.filter(id=payload.prompt_id).aexists()
    ):
        return 400, {"error": "Prompt does not exist"}

    if (
        payload.report_id
        and not await Report.active_objects.filter(id=payload.report_id).aexists()
    ):
        return 400, {"error": "Report does not exist"}

    chart = await Chart.objects.acreate(**payload.dict(), user=request.auth)
    return {
        "message": "Chart created successfully",
        "data": serialize_single_obj(schema=ChartSchema, qs=chart),
    }


@router.get(
    "/report-charts/{identifier}",
    response={200: ReportChartRespSchema, codes_4xx: Error},
    auth=AuthBearer(),
)
def get_report_chart(request, identifier: UUID):
    """endpoint to get report chart"""
    chart = (
        Chart.active_objects.filter(identifier=identifier)
        .select_related("report")
        .first()
    )
    if not chart:
        return 400, {"error": "Chart does not exist"}
    report = chart.report
    # get chart columns, filters from report id
    table = report.table
    schema_name = table.schema_name
    datasource = DataSource.active_objects.filter(dataset__name=schema_name).first()
    report_filters = get_report_filters(
        table.id, user=request.auth, filters=report.filters
    )
    # return SELECT x_axis, SUM(column[y]) from {report.table} GROUP_BY column(x)
    columns = chart.data_config.get("columns")
    x_axis, y_axis = columns["x"], columns["y"]
    dynamic_columns = [{"formula": y_axis, "alias": "total"}]
    group_by_list = [x_axis]
    try:
        db_info = connect_db(
            db_name=datasource.name,
            hostname=datasource.address,
            username=datasource.username,
            password=aes_gcm_cipher.decrypt(datasource.password),
            port=datasource.port,
        )

        result_query = db_info.construct_query(
            table_name=table.name,
            schema=schema_name,
            limit=None,
            offset=None,
            column_list=group_by_list,
            filters=report_filters,
            dynamic_columns=dynamic_columns,
            group_by=group_by_list,
        )
        result = db_info.execute_query(result_query)

    except psycopg2.Error as e:
        err = e.get("error").split("\n")[0]
        return 400, {"error": str(err)}

    db_info.disconnect()

    return {"chart": chart, "result": result}


@router.get(
    "/reports/{report_id}/charts",
    response={200: ReportChartRespSchema, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions(permissions.self_service.reports.view)
def get_report_charts(request, report_id: int):
    """get all charts in a report, limited to one chart for now
    to generate this query SELECT x-axis, SUM(y_axis) from {report.table} GROUP_BY column(x-axis)"""
    # if not await Report.active_objects.filter(id=report_id).aexists():
    #     return 400, {"error": "Report does not exist"}
    # charts = await get_charts_async(report_id=report_id)
    # return {
    #     "message": "Success",
    #     "data": await async_serialize_queryset(schema=ChartSchema, qs=charts),
    # }

    chart = (
        Chart.active_objects.filter(report_id=report_id)
        .select_related("report")
        .first()
    )
    # return empty array
    if not chart:
        return 200, {"chart": chart, "result": []}
    report = chart.report
    # get chart columns, dynamic_columns from data config, filters from report id
    table = report.table
    schema_name = table.schema_name
    datasource = DataSource.active_objects.filter(dataset__name=schema_name).first()
    report_filters = get_report_filters(
        table.id, user=request.auth, filters=report.filters
    )

    columns = chart.data_config.get("columns")
    x_axis, y_axis = columns["x"], columns["y"]
    dynamic_columns = [{"formula": y_axis, "alias": "total"}]
    group_by = [x_axis]
    try:
        db_info = connect_db(
            db_name=datasource.name,
            hostname=datasource.address,
            username=datasource.username,
            password=aes_gcm_cipher.decrypt(datasource.password),
            port=datasource.port,
        )

        result_query = db_info.construct_query(
            table_name=table.name,
            schema=schema_name,
            limit=None,
            offset=None,
            column_list=[x_axis],
            filters=report_filters,
            dynamic_columns=dynamic_columns,
            group_by=group_by,
        )
        result = db_info.execute_query(result_query)

    except psycopg2.Error as e:
        return 400, {"error": str(e)}

    db_info.disconnect()

    return {"chart": chart, "result": result}


@router.get(
    "/collections/{identifier}/charts",
    response={200: ResponseWithData, codes_4xx: Error},
)
@has_permissions(permissions.self_service.reports.view)
async def get_collection_charts(request, identifier: UUID):
    """
    - gets collection charts if collection owner is user
    """
    if not Collection.active_objects.filter(identifier=identifier).aexists:
        return 404, {"error": "Collection not found"}

    charts = await get_charts_async(
        collection__identifier=identifier, collection__user_id=request.auth.id
    )
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=ChartSchema, qs=charts),
    }


@router.post(
    "/collections/{identifier}/charts",
    response={200: ResponseWithData, codes_4xx: Error},
)
@has_permissions(permissions.self_service.reports.create)
async def create_chart_for_collection(
    request, identifier: UUID, data: ChartCreateSchema
):
    """
    - create chart for collection if collection owner is user
    """
    collection = await Collection.active_objects.filter(
        identifier=identifier, user=request.auth
    ).afirst()
    if not collection:
        return 404, {"error": "Collection not found"}

    chart = await Chart.objects.acreate(
        collection=collection,
        title=data.title,
        data_config=data.data_config,
        user=request.auth,
    )
    return {
        "message": "Collection Chart created successfully",
        "data": serialize_single_obj(schema=ChartSchema, qs=chart),
    }


@router.patch(
    "/charts/{identifier}/update", response={200: ResponseWithData, codes_4xx: Error}
)
@has_permissions(permissions.self_service.reports.edit)
async def update_chart(request, identifier: UUID, data: ChartUpdateSchema):
    """
    - update chart if collection owner is user
    """
    chart = (
        await Chart.active_objects.filter(identifier=identifier, user=request.auth)
        .select_related("user")
        .afirst()
    )
    if chart is None:
        return 404, {"error": "Chart not found"}

    payload = data.dict(exclude_unset=True)
    field_names = get_updated_fields()
    for field_name, value in payload.items():
        setattr(chart, field_name, value)
        field_names.append(field_name)

    await chart.asave(update_fields=field_names)
    return {
        "message": "Chart updated successfully",
        "data": serialize_single_obj(schema=ChartSchema, qs=chart),
    }


@router.delete("/charts/{identifier}/delete", response={200: Success, codes_4xx: Error})
@has_permissions(permissions.self_service.reports.delete)
async def delete_chart(request, identifier: UUID):
    """
    Delete chart
    """
    chart = await Chart.active_objects.filter(
        identifier=identifier, user=request.auth
    ).afirst()
    if not chart:
        return 404, {"error": "Chart does not exist"}

    await chart.asoft_delete()
    return {"message": "Chart deleted successfully"}


@router.post("/charts-data", response={200: ChartDataSchema, codes_4xx: Error})
@has_permissions(permissions.self_service.reports.view)
async def get_chart_data(
    request, payload: ChartPreviewSchema, limit: str | None = None
):
    """
    endpoint that takes sqlquery and
    select dataset and columns from a dataset to return a preview of what the data would look like
    optional Limit as a query param. Defaults to None
    """
    query = payload.dict().get("query")
    try:
        if limit:
            limit_ = int(limit)
    except ValueError:
        limit_ = None
    try:
        query = f"{query} LIMIT {limit_};" if limit else query

        df = pd.DataFrame(source_engine.connect().execute(text(query)))
        result = json.loads(df.to_json(orient="records"))
    except ProgrammingError as e:
        print(f"Programming error: {e}")
        return 400, {"error": "Incorrect query"}

    return 200, {"data": result}


@router.get(
    "/tables/{table_id}/report-columns",
    response={200: list[ColumnSchema]},
    auth=AuthBearer(),
)
# reports columns should only be visible for those who can create report
@has_permissions(permissions.self_service.reports.create)
def report_columns(request, table_id):
    """get list of report columns for a user"""
    if not Rule.active_objects.filter(table_id=table_id).exists():
        # return all columns
        return get_filtered_columns(
            order_by_fields=["name"], filters={"table_id": table_id}
        )
    columns = []
    rule = get_applied_rules(
        table_id=table_id, user=request.auth
    )  # get current user's rule access
    if rule:
        rule_columns = rule.columns.get("columns")
        columns = get_filtered_columns(
            order_by_fields=["name"], filters={"id__in": rule_columns}
        )
    # return empty columns if user not part of assigned users
    return columns


@router.post("/reports", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions(permissions.self_service.reports.create)
async def create_report(request, payload: ReportCreateSchema):
    """create report"""
    table = await Table.active_objects.filter(id=payload.table_id).afirst()
    if not table:
        return 400, {"error": "Table does not exist"}

    payload = payload.dict()
    columns = payload.pop("columns")

    if len(columns) == 0:
        return 400, {"error": "Report has no column"}

    report = await Report.objects.acreate(
        **payload, table=table, created_by=request.auth
    )
    data = [ReportColumn(report=report, column_id=col) for col in columns]
    await ReportColumn.objects.abulk_create(data)

    return {
        "message": "Report created successfully",
        "data": serialize_single_obj(schema=ReportSchema, qs=report),
    }


@router.post(
    "/preview-reports/{table_id}",
    response={200: ReportPreviewSchema, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions(permissions.self_service.reports.create)
def preview_report(
    request, table_id: int, payload: PreviewSchema, page: int, page_size: int
):
    """Endpoint to preview report with filters.
    Return sample data of non senstive columns in a table
    """
    table = Table.active_objects.filter(id=table_id).select_related("dataset").first()
    if not table:
        return 400, {"error": "Table does not exist"}

    schema_name = table.schema_name
    datasource = DataSource.active_objects.filter(dataset__name=schema_name).first()
    payload = payload.dict()
    filters = payload.get("filters")
    # connector for access_config groups and filters defaulted to AND
    report_filters = get_report_filters(table_id, user=request.auth, filters=filters)
    column_list = get_filtered_columns_list(
        order_by_fields=["name"], filters={"table_id": table_id}
    )
    limit = page_size if page_size else None
    offset = page - 1 if page > 0 else 0
    try:
        db_info = connect_db(
            db_name=datasource.name,
            hostname=datasource.address,
            username=datasource.username,
            password=aes_gcm_cipher.decrypt(datasource.password),
            port=datasource.port,
        )
        # select rows in the table for preview
        record_count_query = db_info.construct_count_query(
            table_name=table.name,
            schema=schema_name,
            alias="total_records",
            filters=report_filters,
        )
        total_records = db_info.execute_query(record_count_query)

        result_query = db_info.construct_query(
            table_name=table.name,
            schema=schema_name,
            limit=limit,
            offset=offset,
            filters=report_filters,
            column_list=column_list,
            dynamic_columns=payload["dynamic_columns"],
        )
        result = db_info.execute_query(result_query)

    except psycopg2.Error as e:
        err = e.get("error").split("\n")[0]
        return 400, {"error": str(err)}

    db_info.disconnect()

    return {"total_records": total_records, "result": result}


@router.get(
    "/preview-reports/{table_id}",
    response={200: ReportPreviewSchema, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions(permissions.self_service.reports.create)
def get_preview_report(request, table_id: int, page: int, page_size: int):
    """Endpoint to preview report without filters.
    # Return sample data of columns in a table
    page=limit, pagesize=offset
    """
    table = (
        Table.active_objects.filter(id=table_id).select_related("dataset").first()
    )  # dataset is needed to get schema_name
    if not table:
        return 400, {"error": "Table does not exist"}

    schema_name = table.schema_name
    datasource = DataSource.active_objects.filter(dataset__name=schema_name).first()
    report_filters = get_report_filters(table_id=table_id, user=request.auth)
    column_list = get_filtered_columns_list(
        order_by_fields=["name"], filters={"table_id": table_id}
    )
    limit = page_size if page_size else None
    offset = page - 1 if page > 0 else 0
    try:
        db_info = connect_db(
            db_name=datasource.name,
            hostname=datasource.address,
            username=datasource.username,
            password=aes_gcm_cipher.decrypt(datasource.password),
            port=datasource.port,
        )
        # select rows in the table for preview
        record_count_query = db_info.construct_count_query(
            table_name=str(table.name),
            schema=schema_name,
            alias="total_records",
            filters=report_filters,
        )
        total_records = db_info.execute_query(record_count_query)

        result_query = db_info.construct_query(
            table_name=str(table.name),
            schema=schema_name,
            limit=limit,
            offset=offset,
            filters=report_filters,
            column_list=column_list,
        )
        result = db_info.execute_query(result_query)

    except psycopg2.Error as e:
        err = e.get("error").split("\n")[0]
        return 400, {"error": str(err)}

    db_info.disconnect()

    return {
        "total_records": total_records,
        "result": result,
        "table_description": table.description,
    }


# to be refactored to async
@router.get(
    "/reports/{id}",
    response={200: ReportRespSchema, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions(permissions.self_service.reports.view)
def get_single_report(request, id: int, page: int, page_size: int):
    """get single report
    page=limit, pagesize=offset
    #"""

    report = (
        Report.active_objects.filter(id=id)
        .select_related("table")
        .annotate(
            access_level=Subquery(
                SharedReportUser.active_objects.only("access_level")
                .filter(user=request.auth, report_id=OuterRef("pk"))
                .values("access_level")[:1]
            )
        )
        .first()
    )
    if not report:
        return 400, {"error": "Report does not exist"}
    limit = page_size if page_size else None
    offset = page - 1 if page > 0 else 0
    # get columns from report column
    report_columns = (
        ReportColumn.active_objects.filter(report=report)
        .select_related("column")
        .order_by("id")
    )
    # get the columns obj and column name, seperated by a comma
    columns_obj = [item.column for item in report_columns]
    result, error = get_report_details(
        report, user=request.auth, limit=limit, offset=offset
    )
    if error:
        err = error.get("error").split("\n")[0]
        return 400, {"error": str(err)}
    resp = {
        "filters": report.filters,
        "dynamic_columns": report.dynamic_columns,
        "access_level": report.access_level,
        "columns": columns_obj,
        "created_at": report.created_at,
        "updated_at": report.updated_at,
    }
    return {**result, **resp}


@router.patch("/reports/{id}", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions(permissions.self_service.reports.edit)
async def edit_report(request, id: int, data: ReportUpdateSchema):
    """edit report"""
    report = await Report.active_objects.filter(id=id).afirst()
    if not report:
        return 400, {"error": "Report does not exist"}

    # check if current user is report owner or has access to edit
    if report.created_by_id != request.auth.id and not await report.has_edit_access(
        request.auth
    ):
        return 400, {"error": "You do not have permission to edit this report"}

    payload = data.dict(exclude_unset=True)
    if payload.get("columns"):
        columns = payload.pop("columns")
        # update new columns by creating new ones
        column_data = await ReportColumn.objects.abulk_create(
            [ReportColumn(column_id=col_id) for col_id in columns]
        )
        # await ReportColumn.objects.abulk_create(column_data)
        await report.report_columns.aset(column_data)

    if "filters" in payload:
        filters = payload.pop("filters")
        await Report.active_objects.filter(id=id).aupdate(filters=filters)

    if "dynamic_columns" in payload:
        dynamic_columns = payload.pop("dynamic_columns")
        await Report.active_objects.filter(id=id).aupdate(
            dynamic_columns=dynamic_columns
        )

    field_names = get_updated_fields()
    for field_name, value in payload.items():
        setattr(report, field_name, value)
        field_names.append(field_name)
    await report.asave(update_fields=field_names)

    return {
        "message": "Report updated successfully",
        "data": serialize_single_obj(schema=ReportUpdateSchema, qs=report),
    }


@router.delete("/reports/{id}/delete", response={200: Success, codes_4xx: Error})
@has_permissions(permissions.self_service.reports.delete)
async def delete_report(request, id: int):
    """delete report"""
    if not await Report.active_objects.filter(id=id, created_by=request.auth).aexists():
        return 400, {"error": "You are not allowed to perform this action"}

    await Report.active_objects.filter(id=id).asoft_delete()
    return {"message": "Report deleted successfully."}


@router.get("/reports", response={200: ResponseWithData})
@has_permissions(permissions.self_service.reports.view)
async def get_all_reports(request):
    """returns all the public report + reports created by current user"""
    reports = await get_all_reports_async(is_private=False, user=request.auth)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=ReportSchema, qs=reports),
    }


@router.get("/recent-reports", response={200: ResponseWithData})
@has_permissions(permissions.self_service.reports.view)
async def get_recent_reports(request):
    """returns 5 recent report by current user"""
    qs = Report.active_objects.filter(created_by=request.auth).order_by("-created_at")[
        :5
    ]
    reports = await sync_to_async(list)(qs)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=ReportSchema, qs=reports),
    }


@router.get("/public-reports", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions(permissions.self_service.reports.view)
async def get_public_reports(request):
    """get all public reports"""
    reports = await get_user_or_public_reports_async(is_private=False)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=ReportSchema, qs=reports),
    }


@router.get("/private-reports", response={200: ResponseWithData, codes_4xx: Error})
@has_permissions(permissions.self_service.reports.view)
async def get_private_reports(request):
    """get all private reports"""
    reports = await get_reports_async(request.auth, is_private=True)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=ReportSchema, qs=reports),
    }


@router.get("/individual-reports", response={200: ResponseWithData})
@has_permissions(permissions.self_service.reports.view)
async def get_user_reports(request):
    """endpoint for getting user reports"""
    reports = await get_user_or_public_reports_async(created_by=request.auth)
    return {
        "message": "Success",
        "data": await async_serialize_queryset(schema=ReportSchema, qs=reports),
    }


@router.post(
    "/reports/{id}/share", response={200: Success, codes_4xx: Error}, auth=AuthBearer()
)
@has_permissions(permissions.self_service.reports.create)
def share_report(request, id: int, payload: SharedReportSchema):
    """endpoint to share report to individual use or everyone"""
    report = Report.active_objects.filter(id=id).first()
    if not report:
        return 400, {"error": "Report does not exist"}

    if request.auth.id != report.created_by_id:
        return 400, {"error": "You are not allowed to perform this action"}

    if payload.is_shared_to_everyone:
        users_id = User.active_objects.exclude(id=report.created_by_id).values_list(
            "id", flat=True
        )  # exclude creator of report as part of everyone
        create_shared_report(report, ShareOptions.EVERYONE, users_id)
        # notify report owner
        create_notification(
            message=ResponseMessages.SHARED_REPORT_OWNER_MSG % (report.name),
            user=request.auth,
            category=Category.shared_report,
        )

    else:
        if not len(payload.users) > 0:
            return 400, {"error": "No user was selected. Select at least one user"}
        users_details = payload.dict().get("users")
        create_shared_report(report, ShareOptions.INDIVIDUALS, users_details)

    return {"message": "Report shared successfully."}


@router.get("/shared-reports", response={200: list[ReportSchema]}, auth=AuthBearer())
def get_report_shared_with_user(request):
    return (
        Report.active_objects.filter(sharedreportuser__user=request.auth)
        .annotate(access_level=F("sharedreportuser__access_level"))
        .all()
    )


@router.get("reports/{report_id}/shared-users", response={200: list[dict]})
def get_shared_user(request, report_id: int):
    """endpoint to get shared users on a report"""
    users = SharedReportUser.active_objects.filter(report_id=report_id).values_list(
        "user__first_name", "user__last_name", "user__id"
    )
    users = [{"first_name": row[0], "last_name": row[1], "id": row[2]} for row in users]
    return users


@router.post(
    "/schedule-report",
    response={200: ScheduleReportResp, 400: Error},
    auth=AuthBearer(),
)
def schedule_report(request, payload: ScheduleReportSchema):
    report = Report.active_objects.filter(id=payload.report_id).first()
    if not report:
        return 400, {"error": "Report does not exist"}

    if request.auth.id != report.created_by_id:
        return 400, {"error": "You are not allowed to perform this action"}

    payload = validate_recipients(payload)
    to = payload.pop("to", [])
    bcc = payload.pop("bcc", [])
    cc = payload.pop("cc", [])

    schedule_report = ScheduleReport.objects.create(
        **payload,
        created_by_id=request.auth.id,
    )

    Recipient.objects.bulk_create(
        [
            Recipient(
                user_id=id,
                schedule_report=schedule_report,
                recipient_type=RecipientTypes.to,
            )
            for id in to
        ]
    )

    if len(bcc) > 0:
        Recipient.objects.bulk_create(
            [
                Recipient(
                    user_id=id,
                    schedule_report=schedule_report,
                    recipient_type=RecipientTypes.bcc,
                )
                for id in bcc
            ]
        )

    if len(cc) > 0:
        Recipient.objects.bulk_create(
            [
                Recipient(
                    user_id=id,
                    schedule_report=schedule_report,
                    recipient_type=RecipientTypes.cc,
                )
                for id in cc
            ]
        )

    task = create_periodic_task(schedule_report)
    schedule_report.task = task
    schedule_report.save()
    _ = create_notification(
        message=f"You have successfully scheduled a delivery for {report.name} report",
        user=request.auth,
        category=Category.schedule,
    )
    return schedule_report


@router.get(
    "/schedules/{report_id}",
    response={200: list[ScheduleListSchema], codes_4xx: Error},
    auth=AuthBearer(),
)
def get_schedules_list(request, report_id: int):
    schedules = ScheduleReport.active_objects.filter(
        report_id=report_id
    ).select_related("task", "created_by")
    return schedules


@router.patch(
    "/toggle-schedule/{id}",
    response={200: ScheduleReportResp, codes_4xx: Error},
    auth=AuthBearer(),
)
def toggle_schedule(request, id: int):
    """this endpoint is used for pausing and enabling a sche"""
    schedule = (
        ScheduleReport.active_objects.filter(id=id).select_related("task").first()
    )
    if not schedule:
        return 400, {"error": "Schedule does not exist"}

    schedule.task.enabled = not schedule.task.enabled
    schedule.task.save(update_fields=["enabled"])
    return schedule


@router.patch(
    "/schedules/{id}/edit", response={200: ScheduleReportResp, codes_4xx: Error}
)
def edit_schedule(request, id: int, payload: ScheduleReportSchema):
    schedule = ScheduleReport.active_objects.filter(
        id=id, created_by=request.auth
    ).first()
    if not schedule:
        return 400, {"error": "You are not allowed to perform this action"}

    payload = validate_recipients(payload)

    payload.pop("report_id")  # uneditable

    recipients = [
        (RecipientTypes.to, payload.pop("to", [])),
        (RecipientTypes.bcc, payload.pop("bcc", [])),
        (RecipientTypes.cc, payload.pop("cc", [])),
    ]
    update_recipients(recipients, schedule)

    field_names = get_updated_fields()
    for field_name, value in payload.items():
        setattr(schedule, field_name, value)
        field_names.append(field_name)

    schedule.last_updated_by = request.auth
    field_names.append("last_updated_by")
    schedule.save(update_fields=field_names)

    edit_periodic_task(schedule)

    return schedule


@router.delete("/schedules/{id}/delete", response={200: Success, codes_4xx: Error})
@has_permissions(permissions.self_service.reports.delete)
async def delete_schedule(request, id: int):
    """delete report"""
    if not await ScheduleReport.active_objects.filter(
        id=id, created_by=request.auth
    ).aexists():
        return 400, {"error": "You are not allowed to perform this action"}

    await ScheduleReport.active_objects.filter(id=id).asoft_delete()
    return {"message": "Schedule deleted successfully."}
