# import json
#
# from channels.generic.websocket import AsyncWebsocketConsumer
#
#
# class ChartPreviewConsumer(AsyncWebsocketConsumer):
#     async def connect(self):
#         await self.channel_layer.group_add(
#             group='chart_preview',
#             channel=self.channel_name
#         )
#         await self.accept()
#
#         await self.send(text_data=json.dumps({
#             'type': 'connection_established',
#             'data': 'You are now connected'
#         }))
#
#     async def disconnect(self, close_code):
#         await self.channel_layer.group_discard(
#             'chart_preview',
#             self.channel_name
#         )
#
#     async def receive(self, text_data=None, bytes_data=None):
#         text_data_json = json.loads(text_data)
#         data = text_data_json['data']
#         await self.channel_layer.group_send(
#             'chart_preview',
#             {
#                 'type': 'chart.preview',
#                 'data': data
#             }
#         )
#
#     async def chart_preview(self, event):
#         data = event['data']
#         await self.send(text_data=json.dumps(
#             {
#                 "type": "Chart Preview",
#                 'data': data
#             }
#         ))
