from datetime import timedelta

from celery import shared_task
from celery.utils.log import get_task_logger
from django.utils import timezone

from accounts.models import Notification
from ai.models import Alert, AlertTypes

logger = get_task_logger(__name__)


@shared_task()
def check_alerts():
    alerts = Alert.objects.filter(is_active=True)

    for alert in alerts:
        check_alert(alert)

    return f"Processed {alerts.count()} alerts"


def check_alert(alert: Alert):
    """
    - Checks if alert can be triggered
        - CONDITIONAL: checks if fields specified meets requirement
        - PERIODIC: check if time specified has passed
    """

    if alert.alert_type == AlertTypes.PERIODIC:
        last_triggered = alert.last_triggered
        if not alert.last_triggered:
            last_triggered = alert.created_at

        data = parsePeriodicAlert(alert)
        if not data:
            logger.error(f"Invalid alert with id {alert.id} config {alert.config}")
            return

        date = timedelta(**data)
        current_time = timezone.now()
        if last_triggered + date < current_time:
            alert.last_triggered = current_time
            err = alert.save()
            if err:
                logger.error(err)

            send_alert_notification(alert)

    elif alert.alert_type == AlertTypes.CONDITIONAL:
        condition_met = checkConditionalAlert(alert)
        if condition_met is None:
            logger.error(f"Invalid alert with id {alert.id} config {alert.config}")
            return

        if condition_met:
            send_alert_notification(alert)


def checkConditionalAlert(alert: Alert):
    """
    alert.config (example):
        count.0 > 4000
    returns:
        True if condition is met

        False if condition is not met

        None if alert.config is invalid
    """

    config = alert.config.split(" ")
    if len(config) != 3:
        return None

    keys = config[0].split(".")
    prompt = alert.user_prompt
    data = prompt.data

    for key in keys:
        data = data.get(key, None)
        if not data:
            return None

    operator = config[1]

    try:
        value = int(config[2])
    except ValueError:
        return None

    match operator:
        case ">":
            return data > value
        case "<":
            return data < value
        case ">=":
            return data >= value
        case _:
            return None


def parsePeriodicAlert(alert: Alert) -> dict | None:
    """
    converts alert.config:
        weeks=0 days=0 hours=0
    to:
        {"weeks": 0, "days": 0, "hours": 0}
    """

    if not alert.config:
        return None

    data = alert.config.split(" ")

    try:
        fields = [x.split("=") for x in data]
        data_dict = {}
        for field in fields:
            data_dict[field[0]] = int(field[1])
    except Exception:
        return None

    return {
        "weeks": data_dict.get("weeks", 0),
        "days": data_dict.get("days", 0),
        "hours": data_dict.get("hours", 0),
    }


def send_alert_notification(alert: Alert):
    message = "Notification: %s" % alert.description or alert

    Notification.objects.create(user=alert.user, message=message)
