import re
from datetime import datetime
from typing import Any
from uuid import UUID

from ninja import ModelSchema, Schema
from pydantic import Field, field_validator

from ai.schemas import UserPromptOutput
from base.choices import AccessLevel, Frequency
from dataset.schemas import ColumnSchema, FilterSchema
from self_service.models import Chart, Collection, Report, ScheduleReport


class ChartSchema(ModelSchema):
    class Meta:
        model = Chart
        exclude = ["id"]


class ChartDisplaySchema(Schema):
    prompt: UserPromptOutput | None = None
    identifier: UUID
    title: str
    data_config: dict | None = None
    is_private: bool | None = None
    user_id: int
    collection_id: int | None = None


class ChartCreateSchema(Schema):
    title: str
    data_config: dict | None = None
    prompt_id: int | None = None
    report_id: int | None = None


class ChartUpdateSchema(Schema):
    title: str | None = None
    data_config: dict | None = None
    is_private: bool | None = None


class CollectionSchema(ModelSchema):
    class Meta:
        model = Collection
        fields = [
            "identifier",
            "name",
            "description",
            "created_at",
            "updated_at",
            "is_private",
        ]


class CollectionCreateSchema(Schema):
    name: str
    description: str | None = None
    chart_identifiers: list[UUID] | None = None


class CollectionUpdateSchema(Schema):
    name: str | None = None
    description: str | None = None
    is_private: bool | None = None


class ChartPreviewSchema(Schema):
    query: str

    @field_validator("query")  # field_validator to validate one schema field
    def check_query(cls, query):
        # check if query contains commands that can modify the data in the database
        if re.match(r"^\s*(drop|alter|truncate|delete|insert|update)\s", query, re.I):
            raise ValueError("Wrong Query")
        if query.endswith(";"):
            query = query[:-1]  # remove semicolon

        # check if query has Limit in it
        match = re.search(r"\bLIMIT\s+(\d+)\b", query, re.I)
        if match:
            # remove the word limit"
            query = re.sub(r"\bLIMIT\s+(\d+)\b", "", query, re.IGNORECASE)
            return query

        return query


class ChartDataSchema(Schema):
    data: list | dict | None = None


class DynamicColumnSchema(Schema):
    formula: str
    alias: str


class ReportCreateSchema(Schema):
    name: str
    table_id: int
    columns: list[int]
    filters: list[FilterSchema] | None = None
    dynamic_columns: list[DynamicColumnSchema] | None = None
    folder_name: str | None = None
    description: str | None = None
    is_private: bool = True


class ReportRespSchema(Schema):
    name: str
    total_records: Any
    columns: list[ColumnSchema] | None = None
    filters: list[FilterSchema] | None = None
    description: str | None = None
    dynamic_columns: list[DynamicColumnSchema] | None = None
    result: list | dict | None = None
    access_level: str
    created_at: datetime
    updated_at: datetime

    @staticmethod
    def resolve_access_level(obj):
        # Returns the string label for access_level
        if obj["access_level"] in AccessLevel.values:  # obj comes as a dict
            return AccessLevel(obj["access_level"]).label
        # return access level as owner
        else:
            return AccessLevel(0).label


class PreviewSchema(Schema):
    dynamic_columns: list[DynamicColumnSchema] | None = None
    filters: list[FilterSchema] | None = None


class ReportPreviewSchema(Schema):
    total_records: Any
    result: list | dict | None = None
    table_description: str | None = None


class ReportSchema(ModelSchema):
    get_created_by: str
    access_level: str = AccessLevel(0).label

    class Meta:
        model = Report
        exclude = ["created_by"]

    @staticmethod
    def resolve_access_level(obj):
        # Returns the string label for access_level
        if obj.access_level in AccessLevel.values:
            return AccessLevel(obj.access_level).label
        # return access level as owner
        return AccessLevel(0).label


class ReportUpdateSchema(Schema):
    name: str | None = None
    table_id: int | None = None
    columns: list[int] | None = None
    filters: list[FilterSchema] | None = None
    dynamic_columns: list[DynamicColumnSchema] | None = None
    folder_name: str | None = None
    description: str | None = None
    is_private: bool | None = None


class ReportChartSchema(Schema):
    title: str
    data_config: dict | None = None  # {columns, dynamic_columns, chart_type}
    report_id: int
    identifier: UUID | None = None


class ReportChartRespSchema(Schema):
    chart: ReportChartSchema | None = None
    result: list | dict | None = None


class PasswordSchema(Schema):
    password: str | None = None


class SharedSettingSchema(Schema):
    user_id: int
    access_level: str

    @field_validator("access_level")
    def check_access_level(cls, access_level):
        if access_level.lower() not in AccessLevel.labels:
            raise ValueError("Invalid access level.")
        return access_level.lower()


class SharedReportSchema(Schema):
    users: list[SharedSettingSchema] = []
    is_shared_to_everyone: bool = False


class ScheduleReportSchema(Schema):
    report_id: int
    frequency: str
    subject: str
    to: list[int]
    cc: list[int] = []
    bcc: list[int] = []
    day_of_week: str = Field(
        "*", description="from 0-6, where Sunday = '0' and Saturday = '6'"
    )
    day_of_month: str = Field("*", description="from 1-31,")
    month_of_year: str = "*"
    start_time: datetime | None = None
    scheduled_datetime: datetime | None = None
    minute: str = "0"
    hour: str = "*"

    @field_validator("frequency")
    def check_frequency(cls, frequency):
        if frequency.capitalize() not in Frequency.labels:
            raise ValueError(f"Invalid Frequency: {frequency} not found in Frequency")
        return Frequency(frequency.capitalize()).value


class ScheduleReportResp(ModelSchema):
    is_enabled: bool

    class Meta:
        model = ScheduleReport
        fields = "__all__"

    @staticmethod
    def resolve_is_enabled(obj):
        return obj.task.enabled


class ScheduleListSchema(Schema):
    id: int
    subject: str
    get_created_by: str
    created_at: datetime
    frequency: str
    to: list[dict] | None = None
    cc: list[dict] | None = None
    bcc: list[dict] | None = None
    start_time: datetime | None = None
    minute: str
    hour: str
    is_enabled: bool
    day_of_week: str
    day_of_month: str
    month_of_year: str
