from secrets import compare_digest

from AFEX_SSO import SSO  # type: ignore
from django.conf import settings
from django.contrib.auth import logout
from django.contrib.auth.models import Group, Permission, update_last_login
from django.contrib.auth.password_validation import validate_password
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.shortcuts import redirect
from django.utils import timezone
from ninja import Form, Query, Router, UploadedFile
from ninja.pagination import paginate
from ninja.responses import codes_4xx

from accounts.utils import (
    check_reset_password_token,
    construct_query_param,
    create_bulk_notification,
    create_notification,
    get_user_from_encoded_id,
    lockout_user,
    unlock_user,
)
from base.choices import Category
from base.constants import (
    ALLOWED_PHOTO_EXTENSION,
    REFRL,
    SUCCESS,
    get_updated_fields,
)
from base.messages import ResponseMessages
from base.perm_constants import CAN_VIEW_USER_GROUPS, permissions
from sherlock.pagination import Pagination
from sherlock.permissions import has_permissions
from sherlock.schemas import Error, Success
from utils.email_utils import Email<PERSON>ender

from .auth import Auth<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, async_auth_bearer
from .cache_utils import get_password_retry_key
from .helpers import validate_ids, validate_perm_ids
from .models import Notification, Role, User, UserLockOut
from .schemas import (
    MakeUserAdminSchema,
    NotificationSchema,
    PermissionListSchema,
    RoleCreateSchema,
    RoleDisplaySchema,
    RoleSchema,
    RoleUpdateSchema,
    SetPasswordSchema,
    TokenSchema,
    TwoFactorSetUpSchema,
    UserCreateSchema,
    UserDisplaySchema,
    UserFilterSchema,
    UserListSchema,
    UserSchema,
    UserUpdateSchema,
    ValidateEmailSchema,
    ValidLinkRespSchema,
    VerifyOTPSchema,
)
from .two_factor_auth import TwoFactorAuth

sso = SSO()
router = Router(tags=["User management"])


@router.get("/azure-sso")
def generate_token_azure_sso(request):
    """
    - Callback from django-auth-adfs
    - logout user from django session auth (django-auth-adfs logs in user automatically)
    - generate jwt tokens and redirect to frontend with tokens as query params
    - add user to AFEX AZURE SSO group
    """
    if hasattr(request, "user") and request.user.is_authenticated:
        user = request.user
    else:
        user = None

    redirect_url = request.session.get(REFRL) or settings.FRONTEND_SERVER_AZURE_CALLBACK

    if not user:
        return redirect(
            redirect_url + construct_query_param("error", "Could not login")
        )

    # if not user.is_admin:
    #     return redirect(
    #         settings.FRONTEND_SERVER_AZURE_CALLBACK
    #         + construct_query_param("error", "Access denied")
    #     )

    # django_auth_adfs automatically logs in user we don't need that
    logout(request)

    # user_group, _ = Group.objects.get_or_create(name="USER")
    # user_group.user_set.add(user)

    access, refresh = JWTAuth(user).generate_token_pair()
    return redirect(redirect_url + f"?access={access}&refresh={refresh}")


@router.post("/sso", response={200: TokenSchema, 400: Error})
def generate_token_sso(request, session_identifier: str):
    """
    - Uses afex SSO library to get user info
    - Creates or updates user info
    - adds user to AFEX SSO group
    - return jwt tokens
    """
    afex_sso_group, _ = Group.objects.get_or_create(name="AFEX SSO")

    res = sso.check_credentials(session_identifier)

    # if type(res) is not dict:
    if not isinstance(res, dict):
        return 400, {"error": "An error has occurred", "detail": res}

    data = res.get("data")
    if not data:
        return 400, {
            "error": "An error has occurred",
            "detail": res.get("error", None),
        }

    user_data = data.get("user", None)
    # if not user_data and type(user_data) is not dict:
    if not user_data and not isinstance(user_data, dict):
        print("! Invalid response from sso %s" % res)
        return 400, {
            "error": "An error has occurred",
            "detail": "Could not fetch user data, please contact an admin",
        }

    email = user_data.get("email")
    user = User.objects.filter(email=email).first()
    if not user:
        user = User.objects.create(
            email=email,
            first_name=user_data.get("first_name"),
            last_name=user_data.get("last_name"),
            photo=user_data.get("photo"),
            tribe=user_data.get("tribe"),
            designation=user_data.get("designation"),
            email_aliases=user_data.get("email_aliases"),
        )
    else:
        user.email = email
        user.first_name = user_data.get("first_name")
        user.last_name = user_data.get("last_name")
        user.photo = user_data.get("photo")
        user.tribe = user_data.get("tribe")
        user.designation = user_data.get("designation")
        user.email_aliases = user_data.get("email_aliases")
        user.save()

    afex_sso_group.user_set.add(user)
    afex_sso_group.save()

    access, refresh = JWTAuth(user).generate_token_pair()
    return {"access": access, "refresh": refresh}


@router.post("/token", response={200: TokenSchema | TwoFactorSetUpSchema, 400: Error})
def generate_token(request, user: UserSchema):
    """
    login api
    """
    user_obj = User.active_objects.filter(email=user.email).first()
    if not user_obj:
        return 400, {"error": ResponseMessages.WRONG_CREDENTIALS_MSG}

    if not user_obj.is_active:
        return 400, {"error": ResponseMessages.SUSPENDED_ACCOUNT_MSG}

    if user_obj.is_locked_out and (
        settings.IS_LOCKOUT_PERMANENT
        or user_obj.locked_out_till
        and timezone.now() < user_obj.locked_out_till
    ):
        return 400, {"error": ResponseMessages.ACCOUNT_LOCKED_OUT}

    if not user_obj.check_password(user.password):
        cache_key = get_password_retry_key(user.email)

        cache.get_or_set(cache_key, 0)
        trials = cache.incr(cache_key)
        if trials < settings.PASSWORD_RETRY_MAX_COUNT:
            no_of_attempts_left = settings.PASSWORD_RETRY_MAX_COUNT - trials
            if no_of_attempts_left <= 2:
                return 400, {
                    "error": f"{ResponseMessages.WRONG_CREDENTIALS_MSG}. You have {no_of_attempts_left} attempt(s) left to login before account lockout"
                }
        else:
            if settings.IS_LOCKOUT_PERMANENT:
                # lock user out permanently
                user_obj.is_locked_out = True
                user_obj.locked_out_till = None
                user_obj.save(update_fields=["is_locked_out", "locked_out_till"])
                return 400, {"error": ResponseMessages.ACCOUNT_LOCKED_OUT}
            else:
                # user has exceeded password retry max count
                last_lockout = (
                    UserLockOut.objects.only("id", "is_unlocked", "lock_out_duration")
                    .filter(user=user_obj)
                    .last()
                )
                if last_lockout and not last_lockout.is_unlocked:
                    # if user last lockout is still locked, step up their cache timeout and lock out duration
                    timeout = (
                        last_lockout.lock_out_duration
                        + settings.INCORRECT_PASSWORD_LOCKOUT_TIME_STEP
                    )
                    cache.set(cache_key, trials, timeout=timeout)
                    lockout_user(request, user_obj, timeout)
                    return 400, {"error": ResponseMessages.ACCOUNT_LOCKED_OUT}

                else:
                    # the first lockout after 5 failed attempts
                    cache.set(
                        cache_key, 0, timeout=settings.INCORRECT_PASSWORD_LOCKOUT_TIME
                    )
                    user_obj.is_locked_out = True
                    user_obj.save(update_fields=["is_locked_out"])
                    timeout = settings.INCORRECT_PASSWORD_LOCKOUT_TIME
                    lockout_user(request, user_obj, timeout)
                    return 400, {"error": ResponseMessages.ACCOUNT_LOCKED_OUT}

        return 400, {"error": ResponseMessages.WRONG_CREDENTIALS_MSG}

    else:
        # user finally has correct password
        cache_key = get_password_retry_key(user.email)
        cache.delete(cache_key)
        unlock_user(user_obj)

    if not user_obj.is_two_factor_auth_enabled:
        access, refresh = JWTAuth(user_obj).generate_token_pair()
        update_last_login(None, user_obj)
        return 200, {"access": access, "refresh": refresh}

    qr_code_url = None
    if not user_obj.has_set_up_two_factor_auth:
        two_factor_auth = TwoFactorAuth(user_obj)
        qr_code_url = two_factor_auth.get_qr_code_url()

    user_obj.update_post_login(True)
    return 200, {
        "email": user_obj.email,
        "set_up_2fa": qr_code_url,
        "two_factor_enabled": user_obj.is_two_factor_auth_enabled,
    }


@router.post("/verify/two-factor-otp", response={200: TokenSchema, 400: Error})
def verify_two_factor_auth(request, payload: VerifyOTPSchema):
    """
    endpoint to set up two-factor authentication and verify otp code
    """
    user = User.active_objects.filter(email=payload.email).first()
    if not user:
        return 400, {
            "error": ResponseMessages.VALIDATION_ERROR,
            "detail": ResponseMessages.WRONG_CREDENTIALS_MSG,
        }
    if not user.is_two_factor_auth_enabled:
        return 400, {
            "error": ResponseMessages.VALIDATION_ERROR,
            "detail": ResponseMessages.TWO_FACTOR_DISABLED_MSG,
        }

    two_factor_auth = TwoFactorAuth(user)
    valid_otp = two_factor_auth.verify_user_otp(payload.otp)
    if not valid_otp:
        return 400, {
            "error": ResponseMessages.VALIDATION_ERROR,
            "detail": ResponseMessages.INVALID_OTP_MSG,
        }

    if not user.has_set_up_two_factor_auth:
        # update has_set_up_two_factor_auth field
        user.set_two_factor_auth()

    if user.is_post_login:
        user.update_post_login(False)
    access, refresh = JWTAuth(user).generate_token_pair()
    update_last_login(None, user)
    return 200, {"access": access, "refresh": refresh}


@router.post("/token/refresh", response={200: TokenSchema, codes_4xx: Error})
def refresh_token(request, token: str):
    """
    refresh access token
    """

    payload = JWTAuth().decode_refresh_token(token)
    if not payload.get("ref") and not payload.get("ref"):
        return 400, {"error": ResponseMessages.INVALID_TOKEN_MSG}

    email = payload["user"]["email"]
    user_obj = User.objects.filter(email=email).first()
    if not user_obj:
        return 400, {"error": ResponseMessages.WRONG_CREDENTIALS_MSG}

    access = JWTAuth(user_obj).generate_access_token()
    return {"access": access}


@router.get(
    "/notifications", response={200: list[NotificationSchema]}, auth=AuthBearer()
)
def get_user_notifications(request):
    return request.auth.notifications.all()


@router.patch("/notifications/mark-all", response={200: Success}, auth=AuthBearer())
def mark_all_notification(request):
    _ = Notification.active_objects.filter(user=request.auth, is_read=False).update(
        is_read=True
    )
    return SUCCESS


@router.delete("notifications/clear-all", response={200: Success}, auth=AuthBearer())
def clear_notifications(request):
    Notification.objects.filter(user=request.auth).delete()
    return SUCCESS


@router.patch(
    "/notifications/{id}/read",
    response={200: Success, codes_4xx: Error},
    auth=AuthBearer(),
)
def mark_notification_as_read(request, id: int):
    notif = Notification.objects.filter(id=id).first()
    if not notif:
        return 400, {"error": "Notification not found"}

    notif.is_read = True
    notif.save(update_fields=["is_read", "updated_at"])
    return SUCCESS


@router.get("/groups/users", response={200: list[RoleSchema]}, auth=AuthBearer())
@has_permissions([CAN_VIEW_USER_GROUPS])
def get_users_by_groups(request):
    return Group.objects.all()


@router.post(
    "/roles", response={200: RoleDisplaySchema, codes_4xx: Error}, auth=AuthBearer()
)
@has_permissions([permissions.user_mgt.roles.create])
def create_role(request, payload: RoleCreateSchema):
    data = payload.dict()
    role_permissions = data.pop("permissions", [])
    user_ids = data.pop("user_ids", [])

    if Role.active_objects.filter(name=payload.name).exists():
        return 400, {"error": f"Role with {payload.name} already exists"}

    role = Role.objects.create(name=payload.name, created_by=request.auth)
    perms_id = validate_perm_ids(Permission, role_permissions)
    role.permissions.set(perms_id)

    users = validate_ids(User, user_ids)
    role.user_set.set(users)
    return role


@router.get("/roles", response={200: list[RoleDisplaySchema]}, auth=AuthBearer())
@has_permissions([permissions.user_mgt.roles.view])
def get_roles(request):
    return Role.active_objects.only("id", "name", "permissions").prefetch_related(
        "permissions"
    )


@router.get(
    "roles/{id}", response={200: RoleSchema, codes_4xx: Error}, auth=AuthBearer()
)
@has_permissions([permissions.user_mgt.roles.view])
def get_single_role(request, id: int):
    role = (
        Role.active_objects.filter(id=id)
        .select_related("created_by", "last_updated_by", "group_ptr")
        .prefetch_related("group_ptr__user_set")
        .first()
    )

    if not role:
        return 400, {"error": "Role does not exist"}

    return role


@router.patch(
    "/roles/{id}/update", response={200: RoleDisplaySchema}, auth=AuthBearer()
)
@has_permissions([permissions.user_mgt.roles.edit])
def edit_role(request, id: int, payload: RoleUpdateSchema):
    """endpoint to edit roles"""
    data = payload.dict(exclude_unset=True)
    role = Role.active_objects.filter(id=id).first()

    if "user_ids" in data:
        user_ids = data.pop("user_ids")
        users = validate_ids(User, user_ids)
        role.user_set.set(users)

    if "permissions" in data:
        role_permissions = data.pop("permissions")
        # validate if ids in role_permissions is contained in Permissions
        perms_id = validate_perm_ids(Permission, role_permissions)
        role.permissions.set(perms_id)
        # update users in this role of permission update
        user_list = role.user_set.values_list("id", flat=True)
        create_bulk_notification(
            message=ResponseMessages.ACCOUNT_UPDATE,
            users_id=user_list,
            category=Category.account_update,
        )

    field_names = get_updated_fields()
    for field_name, value in data.items():
        setattr(role, field_name, value)
        field_names.append(field_name)

    role.last_updated_by = request.auth
    field_names.append("last_updated_by")
    role.save(update_fields=field_names)

    return role


@router.delete(
    "roles/{id}/delete", response={200: Success, codes_4xx: Error}, auth=AuthBearer()
)
@has_permissions([permissions.user_mgt.roles.delete])
def delete_role(request, id: int):
    """delete role"""
    if not Role.active_objects.filter(id=id).exists():
        return 400, {"error": "Role does not exist"}

    Role.active_objects.filter(id=id).soft_delete()
    return {"message": "Role deleted successfully."}


@router.post(
    "/user/create",
    response={200: UserDisplaySchema, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions([permissions.user_mgt.users.create])
def create_user(
    request, payload: UserCreateSchema = Form(...), photo: UploadedFile = None
):
    if photo and not photo.name.endswith(ALLOWED_PHOTO_EXTENSION):
        return 400, {"error": ResponseMessages.INVALID_FILE_EXTENSION}
    data = payload.dict()

    if User.objects.filter(email=payload.email).exists():
        return 400, {"error": ResponseMessages.DUPLICATE_EMAIL}
    if data.get("phone_number") and User.objects.filter(
        phone_number=data.get("phone_number")
    ):
        return 400, {"error": ResponseMessages.DUPLICATE_PHONENUMBER}

    permissions = data.pop("user_permissions", [])
    roles = data.pop("roles", [])

    user = User.objects.create(**data, photo=photo)
    if len(permissions) != 0:
        perms_id = validate_perm_ids(Permission, permissions)
        user.user_permissions.set(perms_id)
    if len(roles) != 0:
        role_ids = validate_ids(Role, roles)
        user.groups.add(*role_ids)
    EmailSender.confirmation_mail(user)
    return user


@router.get(
    "/users/{user_id}",
    response={200: UserDisplaySchema | None, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions([permissions.user_mgt.users.view])
def get_user(request, user_id: int):
    user = (
        (
            User.active_objects.only(
                "email", "first_name", "last_name", "photo"
            ).prefetch_related("user_permissions", "groups")
        )
        .filter(id=user_id)
        .first()
    )

    if user is None:
        return 404, {"error": "User not found"}
    return user


@router.patch(
    "/users/{user_id}/update",
    response={200: UserDisplaySchema, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions([permissions.user_mgt.users.edit])
def update_user(
    request,
    user_id: int,
    payload: UserUpdateSchema = Form(...),
    photo: UploadedFile = None,
):
    user = User.active_objects.filter(id=user_id).first()
    if user is None:
        return 404, {"error": "User not found"}

    data = payload.dict()

    if user.is_afex_staff() and (
        data["first_name"] != user.first_name or data["last_name"] != user.last_name
    ):
        return 400, {"error": "You cannot edit your first name or last name"}

    if "roles" in data:
        role_ids = validate_ids(Role, ids=data.pop("roles"))
        user.groups.set(role_ids)

    if "user_permissions" in data:
        perms_list = data.pop("user_permissions")
        perms = validate_perm_ids(Permission, perms_list)
        user.user_permissions.set(perms)
        _ = create_notification(
            message=ResponseMessages.ACCOUNT_UPDATE,
            user=user,
            category=Category.account_update,
        )

    field_names = get_updated_fields()
    for attr, value in data.items():
        setattr(user, attr, value)
        field_names.append(attr)

    if photo:
        user.photo = photo
        field_names.append("photo")

    user.save(update_fields=field_names)
    return user


@router.get("/user/info", response={200: UserDisplaySchema | None}, auth=AuthBearer())
def get_user_information(request):
    if hasattr(request, "auth"):
        user = request.auth
    else:
        user = None
    return user


@router.get("/users", response={200: list[UserListSchema]}, auth=AuthBearer())
@has_permissions([permissions.user_mgt.users.view])
@paginate(Pagination)
def get_all_users(request, filter: UserFilterSchema = Query(...)):
    q = filter.get_filter_expression()
    users = (
        User.active_objects.only("email", "first_name", "last_name", "photo")
        .prefetch_related("user_permissions", "groups")
        .filter(q)
        .order_by("-date_joined")
    )
    return users


@router.get(
    "/permissions", response={200: list[PermissionListSchema]}, auth=AuthBearer()
)
def get_permissions(request):
    perm = (
        Permission.objects.filter(
            content_type__app_label=User._meta.app_label,
            content_type__model=User._meta.model_name,
        )
        .exclude(codename__in=["add_user", "delete_user", "change_user", "view_user"])
        .select_related("content_type")
    )
    return 200, perm


@router.delete(
    "/users/{user_id}/delete",
    response={200: Success, codes_4xx: Error},
    auth=async_auth_bearer,
)
@has_permissions([permissions.user_mgt.users.delete])
async def delete_user(request, user_id: int):
    user = await User.active_objects.filter(id=user_id).afirst()
    if not user:
        return 400, {"error": "User not found"}
    await user.asoft_delete()
    return {"message": "User deleted successfully"}


@router.patch(
    "/users/{id}/toggle-suspend",
    response={200: UserDisplaySchema, codes_4xx: Error},
    auth=AuthBearer(),
)
@has_permissions([permissions.user_mgt.users.edit])
def suspend_user(request, id: int):
    """endpoint to suspend and reactivate staff"""
    user = User.active_objects.filter(id=id).first()
    if not user:
        return 400, {"error": "User not found"}

    if user.is_active:
        user.last_suspended_at = timezone.now()

    user.is_active = not user.is_active
    user.save(update_fields=["is_active", "last_suspended_at"])
    return user


@router.patch(
    "/users/{id}/make-admin",
    response={200: UserDisplaySchema, codes_4xx: Error},
    auth=AuthBearer(),
)
def make_user_admin(request, id: int, payload: MakeUserAdminSchema):
    """make user admin"""
    user = User.active_objects.filter(id=id).first()
    if not user:
        return 400, {"error": "User not found"}
    user.is_admin = payload.make_admin
    user.save(update_fields=["is_admin"])
    create_notification(
        message=ResponseMessages.ADMIN_ACCESS,
        user=user,
        category=Category.account_update,
    )
    return user


@router.post("/validate-email", response={200: Success})
def validate_email(request, payload: ValidateEmailSchema):
    """takes email of user for validation"""
    user = User.objects.filter(email=payload.email).first()
    if not user:
        return 200, {"message": ResponseMessages.FORGOT_PASSWORD_RESP_MSG}

    # send email of frontend url to reset password page
    EmailSender.reset_password_mail(user)
    return 200, {"message": ResponseMessages.FORGOT_PASSWORD_RESP_MSG}


@router.get(
    "set-password/{uidb64}/{token}",
    response={200: ValidLinkRespSchema, codes_4xx: Error},
)
def validate_link(request, uidb64: str, token: str):
    user = get_user_from_encoded_id(uidb64)
    if not user:
        return 400, {"error": ResponseMessages.INVALID_LINK_MSG}

    valid_token = check_reset_password_token(user, token)
    if not valid_token:
        return 200, {"error": ResponseMessages.INVALID_LINK_MSG}
    return 200, {
        "password_requirement_text": ResponseMessages.PASSWORD_REQUIREMENT_TEXT
    }


@router.post("set-password/{uidb64}/{token}", response={200: Success, codes_4xx: Error})
def set_password(request, uidb64: str, token: str, payload: SetPasswordSchema):
    user = get_user_from_encoded_id(uidb64)
    if not user:
        return 400, {"error": ResponseMessages.INVALID_LINK_MSG}

    valid_token = check_reset_password_token(user, token)
    if not valid_token:
        return 400, {"error": ResponseMessages.INVALID_LINK_MSG}

    data = payload.dict()
    password = data.get("password")
    repeat_password = data.get("repeat_password")
    if not compare_digest(password, repeat_password):
        return 400, {"error": ResponseMessages.MISMATCHED_PASSWORD_MSG}

    try:
        validate_password(password, user)
    except ValidationError as e:
        return 400, {"error": "\n".join(e.messages)}

    user.set_password(password)
    user.save(update_fields=["password"])
    return {"message": "Password set successfully"}
