"""
Script Name: Sherlock-Core
Author: <PERSON><PERSON><PERSON><PERSON>
Created At: 2023-09-26

Modified By: <PERSON> King <PERSON>
Modified At: 2024-06-27
Description: This script is the core of Sherlock 🤣 IFYKYK.
"""

import ast
import os
import sqlite3
from datetime import datetime
from pathlib import Path

import langchain_core
import langchain_core.messages
import langchain_core.messages.system
import pandas as pd
import sqlalchemy
from asgiref.sync import sync_to_async
from langchain.agents import Tool
from langchain.prompts import PromptTemplate
from langchain_anthropic import ChatAnthropic as LangchainChatAnthropic
from langchain_core.messages import AIMessage, SystemMessage

# langchain
from langchain_openai import ChatOpenAI as LangchainOpenAI
from langgraph.checkpoint.sqlite import SqliteSaver

# langgraph
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode, tools_condition

# llama_index
from sqlalchemy import text

from ai.credentials import db_connect
from ai.observe import start_instrument
from ai.prompts import EXPLAINER_PROMPT, SYSTEM_PROMPT_V2
from ai.v1.utils import State, Text2SQL_V2, duration, log_event, save_graph_chart
from dataset.helpers import get_sensitive_columns
from utils.exceptions import MaximumRetriesError, TTSQLPipelineConnectError

dir_path = os.path.dirname(os.path.realpath("__file__"))
source_engine, conn_source = db_connect()

db_path = Path("ai/db")
if not db_path.exists():
    os.mkdir(db_path)
db_file = os.path.join(db_path, "memory.sqlite3")
memconn = sqlite3.connect(db_file, check_same_thread=False)


class Sherlock:
    """
    - Makes two requests to chatGPT (for now hopefully will go down to one in the near future)
        - One to generate table names relevant to user prompt
        - The other to generate the sql

    - runs the generated sql against the workbench datawarehouse

    - filters out personal information from data like names, phone numbers and addresses
    """

    current_date = datetime.now()
    model_version = current_date.strftime("%d %B %Y")  # Date the model was released

    max_query_length = 500
    core_exception_message = (
        "Your question appears to be unrelated to the knowledge I've been trained on."
    )
    filtered_fields_message = "The following fields ```{}``` have been removed from this response for privacy reasons."

    def __init__(self):
        self.source_engine, self.conn_source = source_engine, conn_source

    def filter_restricted_columns(self, df: pd.DataFrame):
        """Filtes restricted column from extracted data"""

        if isinstance(df, pd.DataFrame):
            sensitive_columns = set(get_sensitive_columns())
            filter_message = ""
            is_filtered = False
            is_sensitive = len(sensitive_columns) > 0

            if is_sensitive:
                drop_columns = [col for col in sensitive_columns if col in df.columns]
                is_filtered = len(drop_columns) > 0
                filter_message = self.filtered_fields_message.format(
                    ", ".join(drop_columns)
                )
                df = df.drop(drop_columns, axis=1)
            return self.df_to_dict(df), is_filtered, filter_message

    def df_to_dict(self, df: pd.DataFrame):
        """Converts dataframe to dictionary"""
        return df.to_dict()


sherlock = Sherlock()
start_instrument()
# new changes and classes are below


class AIChain:
    """AI chain. Implementation of the workflow and RAG chain"""

    def __init__(self, db_engine) -> None:
        self.engine = db_engine
        self.source_query = None
        self.txt2sqlprompt = None
        self.query_engine = None
        self.memory = SqliteSaver(conn=memconn)
        self.system_message = SystemMessage(SYSTEM_PROMPT_V2)

    def _initialize_system_prompt(self, state):
        """Initializes the system message. The system message gives the Sherlock it's Identity and how it should behave with users"""
        if isinstance(
            state["messages"][0], langchain_core.messages.system.SystemMessage
        ):
            pass
        else:
            state["messages"].insert(0, self.system_message)

        return state

    def initialize_llm_anthropic(self):
        """Initializes Anthropic Claude-3"""
        return LangchainChatAnthropic(model="claude-3-haiku-20240307")

    def initialize_llm_gpt4o(self, mini=False):
        """Initializes OpenAi GPT-4o"""
        return LangchainOpenAI(
            model_name="gpt-4o-mini" if mini else "gpt-4o",
            max_tokens=500,
            streaming=False,
        )

    def build_text2sql_pipeline(self):
        """Builds the text to SQL (TTSQL) pipeline"""
        log_event("Initiating Text-to-SQL Piepline...", err=False)
        try:
            with self.engine.connect() as conn:
                query = """
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = :schema;
                """
                params = {"schema": "sherlock"}
                results = conn.execute(text(query), params)
                sherlock_tabs = []
                for res in results:
                    # if not res[0].startswith('ov'):
                    sherlock_tabs.append(res[0])

            sherlockqp = Text2SQL_V2(dbengine=source_engine, schema_tabs=sherlock_tabs)

            # Build the TTSQL pipiline
            sherlockqp.build_pipeline()

            # Create a Correction Pipeline
            sherlockqp.build_correction_pipeline()

            self.query_engine = sherlockqp

            return self
        except Exception as err:
            log_event(f"Error occured {str(err)}")
            raise TTSQLPipelineConnectError from err

    def _gen_sql(self, prompt):
        """returns the sql query generated the TTSQL"""
        log_event(f"TTSQL Input: {prompt}", err=False)
        sql_query = self.query_engine.run(query=prompt)
        self.source_query = sql_query
        self.txt2sqlprompt = prompt
        return sql_query

    def build_converse_graph(self, llm=None):
        """Builds the conversation workflow API"""

        if llm is None:
            llm = self.initialize_llm_gpt4o(mini=True)
        if not isinstance(llm, LangchainOpenAI):
            raise AssertionError(f"""Type mismatched!! Expected llm instance of
            {type(LangchainChatAnthropic)} or {type(LangchainOpenAI)} but got {type(llm)}""")

        graph_builder = StateGraph(State)
        memory = self.memory
        # build text_2_sql chain
        self.build_text2sql_pipeline()
        tools = [
            Tool.from_function(
                self._gen_sql,
                name="txt2sql",
                description="""Use this tool to generate sql query.
                Provide a concise instruction as an input.
                Modify the user prompt to form a contextual natural question when neccessary""",
            )
        ]

        # llm = ChatAnthropic(model="claude-3-haiku-20240307")
        llm_with_tools = llm.bind_tools(tools)

        def chatbot(state: State):
            log_event(f"Entry Message {state['messages'][-1].content}", err=False)
            state = self._initialize_system_prompt(state)
            return {"messages": llm_with_tools.invoke(state["messages"])}

        graph_builder.add_node("sherlock", chatbot)

        tool_node = ToolNode(tools=tools)

        graph_builder.add_node("tools", tool_node)

        graph_builder.add_conditional_edges("sherlock", tools_condition)

        graph_builder.set_entry_point("sherlock")

        graph_builder.set_finish_point("tools")
        graph = graph_builder.compile(checkpointer=memory)
        save_graph_chart(graph=graph)

        return graph

    def clear_chat_memory(self, thread_id):
        """clears a conversation history
        Parameters:
            thread_id: conversation id
        """
        # clear memory
        try:
            q = (
                "DELETE FROM checkpoints WHERE thread_id = %(thread_id)s",
                {"thread_id": thread_id},
            )
            memconn.execute(q)
            memconn.close()
            log_event(f"Memory: memory history cleared {thread_id}", err=False)
            return True
        except Exception as err:
            log_event(f"Error Clearing memory for thread {thread_id}: {str(err)}")
            memconn.close()
            raise ValueError("Failed to clear memory") from err


class AICore:
    """This the Core class of the sherlock model
    Attributes:
    ----------
    model_chain: AIChain
    chat_graph: conversation graph workflow build from the AIChain
    source_engine: sqlalchemy.engine: connection engine to the source database
    utility_llm: This the llm used for other less intensive tasks such as explain

    Methods:
    -------
    _explainer(user_query, sql_query, result):
        explains the result of the sql query as well as the sql query
    """

    def __init__(self) -> None:
        self.model_chain = AIChain(source_engine)
        self.chat_graph = self.model_chain.build_converse_graph()
        self.source_engine = source_engine
        self.utility_llm = self.model_chain.initialize_llm_gpt4o(mini=True)

    @duration
    def _explainer(self, user_query, sql_query, result):
        """Explains the result returned from the database
        Parameters:
        -----------
        user_query: The question the user asks
        sql_query: The sql_query generated
        result: The result of the sql_query
        """
        template = EXPLAINER_PROMPT

        input_temp = PromptTemplate(
            template=template, input_variables=["user_question", "sql_query", "data"]
        )

        return self.utility_llm.invoke(
            input_temp.format(
                user_question=user_query, sql_query=sql_query, data=result
            )
        )

    def format_message(self, ai_response, explain):
        """
        This function is used to format the messages.
        Takes t

        Parameters:
        ai_response (str): Response from the AI.
        explain (str):
        """

        if explain is not None:
            try:
                explain_obj = ast.literal_eval(explain)
                insight = explain_obj["Insight"]
                further_question = ["Further Question"]
                condesned_response = AIMessage(f"{insight}")
            except SyntaxError:
                # log_event("Failed to format")
                return None

        else:
            condesned_response = f"{ai_response}"
            further_question = ""
        return condesned_response, further_question

    @duration
    def execute_query(self, query, return_query=False):
        """Execuutes the sql query
        Parameters:
        ----------
        query: sql query
        return_query: bool: if true, returns the result and the sql query
        """

        with self.source_engine.connect() as con:
            res = con.execute(text(query))
            data = res.fetchall()
        df = pd.DataFrame(data)
        # drop columns that are tags as sensitive

        self.model_chain.source_query = None
        log_event(f"SQL Executed: {query}", err=False)
        if return_query:
            return df, query
        return df

    @duration
    def handle_sql_exception(self, query, e, question, retries=2):
        """
        Handles error that arises when executing the sql query. This invokes the cqp of the TTSQL
        Parameters:
        ----------
        query: (str)
            The incorrect query that was generated
        e: (str)
            Error returned when the query was initially executed
        question: (str)
            User question


        Returns:

        """
        log_event("Invalid Query Detetected!!! Entering new retry chain", err=False)
        sql_query = self.model_chain.query_engine.correct(
            query=question, incorrect_sql=query, error=e
        )
        try:
            if sql_query and retries > 0:
                return self.execute_query(sql_query, return_query=True)
            raise MaximumRetriesError()

        except sqlalchemy.exc.ProgrammingError as pgrr:
            e = str(pgrr)

            query = sql_query
            retries -= 1
            res = self.handle_sql_exception(
                query=query, e=e, question=question, retries=retries
            )
            return res

    def chat(self, user_prompt, converse_id="x01"):
        """This is the chat method of the AICore Class
        prompt: (str) The prompt from the user. This should be single prompt input from the user
        converse_id: (str) This a thread_id that is used by the model to track a conversation (thread)


        The output of this function is dcitionary with the follwing keys:
        - response: The actual response from the model (Answer to the question)
        - explain: The explanation of the response: This formatted in json and contains three values
            * Insight of the response
            * Futher question to be asked by the user
        - sql_query: The sql query that was generated
        - The user prompt recieved by the model
        - The returned data from the database

        Parameters:
        -----------
        user_prompt: The user message
        converse_id: The conversational ID to track the conversation thread
        """
        try:
            log_event(f"================={converse_id}=================", err=False)
            state = self.chat_graph.invoke(
                {"messages": user_prompt}, {"configurable": {"thread_id": converse_id}}
            )
            ai_message = state["messages"][-1]

            further_question = None
            sql_query = self.model_chain.source_query
            if self.model_chain.source_query is not None:
                question = self.model_chain.txt2sqlprompt
                try:
                    data = self.execute_query(self.model_chain.source_query)

                except sqlalchemy.exc.ProgrammingError as wqerr:
                    e = str(wqerr)

                    try:
                        data, sql_query = self.handle_sql_exception(
                            self.model_chain.source_query, e, question
                        )

                    except MaximumRetriesError as e:
                        log_event(str(e))
                        ai_message = AIMessage(
                            content="Sorry 😞 I am not able to answer this question. Try rephrasing your question and ask again"
                        )
                        return {
                            "response": ai_message.content,
                            "sql_query": sql_query,
                            "next_question": further_question,
                            "data": None,
                        }

                explanation = self._explainer(sql_query, question, data)

                full_response, further_question = self.format_message(
                    ai_message.content, explanation.content
                )
                state["messages"][-1] = full_response

                data, is_filtered, filter_message = sherlock.filter_restricted_columns(
                    data
                )
                if is_filtered:
                    full_response.content = (
                        full_response.content + f"\n\nNote: {filter_message}"
                    )
                    log_event(filter_message, err=False)
                return {
                    "response": full_response.content,
                    "sql_query": sql_query,
                    "next_question": further_question,
                    "data": data,
                }

            return {
                "response": ai_message.content,
                "sql_query": None,
                "next_question": further_question,
                "data": None,
            }

        except sqlalchemy.exc.OperationalError as err:
            log_event(str(err))
            data = None

            res = "Oops 😞 !! An error occured. Please try again"
            return {
                "response": res,
                "sql_query": None,
                "next_question": None,
                "data": None,
            }

    async def a_chat(self, **kwargs):
        """Converts the AICore.chat method to asynchronous function"""
        return await sync_to_async(self.chat)(**kwargs)

    @duration
    def generate_prompt_prediction(self, question):
        """Names a conversation
        Parameters:
        ----------
        question: (str)
            The first user input
        """
        prompt_template = """
        Given a user message a predict precise and short meaningful title  for the conversation:

        You should return only the title

        e.g
        User: What is total number of farmers?
        AI: Number of Farmers

        User:{question}
        AI:

        """

        prompt = PromptTemplate.from_template(template=prompt_template)
        prompt_formatted_str = prompt.format(question=question)

        prediction = self.utility_llm.invoke(prompt_formatted_str)
        title = prediction.content
        log_event(f"New Conversation {question}: Title: {title}", err=False)
        return title.split(":")[-1]

    async def a_generate_prompt_prediction(self, **kwargs):
        """Converts the AICore.chat method to asynchronous function"""
        return await sync_to_async(self.generate_prompt_prediction)(**kwargs)

    async def new_chat(self, user_prompt, converse_id):
        """Executes title prediction and generate response asynchronously"""

        title = await self.a_generate_prompt_prediction(question=user_prompt)
        response = await self.a_chat(user_prompt=user_prompt, converse_id=converse_id)

        return title, response


# if __name__ == "__main__":
#     questions = [
#         "who are you",
#         "how many farmers do i have",
#         "how many of them are in nigeria",
#         "in what states do i have warehouses in nigeria",
#         "what of kenya",
#     ]
#     model_core = AICore()
#     while True:
#         user_input = input("\nquestion : ")
#         # user_prompt = i
#         response = model_core.chat(user_prompt=user_input)
#         ai_res = response["response"]
#         # print(ai_res)

#         EXP = None
#         if response["explain"] is not None:
#             while EXP not in ["y", "n"]:
#                 exp = input("Explain? (y/n): ").strip()
#             if exp == "y":
#                 print("Explanation: ", response["explain"])
#                 print("SQL QUERY:", response["sql_query"])
#             else:
#                 pass
