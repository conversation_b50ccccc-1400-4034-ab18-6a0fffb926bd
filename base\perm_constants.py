CAN_VIEW_FEEDBACK = "ai.view_feedback"
CAN_VIEW_PROMPT_lOG = "ai.view_promptlog"
CAN_VIEW_USER_GROUPS = "accounts.user.can_view_user_groups"


PERMISSION_GROUPS = {
    "self_service": {
        "collection": {
            "create": "can_create_collection",
            "view": "can_view_collection",
            "edit": "can_edit_collection",
            "delete": "can_delete_collection",
        },
        "charts": {
            "create": "can_create_charts",
            "view": "can_view_charts",
            "edit": "can_edit_charts",
            "delete": "can_delete_charts",
        },
        "reports": {
            "create": "can_create_reports",
            "view": "can_view_reports",
            "edit": "can_edit_reports",
            "delete": "can_delete_reports",
        },
    },
    "ai": {
        "prompts": {"access": "can_access_prompts", "view": "can_view_prompts"},
    },
    "user_mgt": {
        "users": {
            "create": "can_register_user",
            "view": "can_view_user",
            "edit": "can_edit_user",
            "delete": "can_delete_user",
        },
        "roles": {
            "create": "can_create_roles",
            "view": "can_view_roles",
            "edit": "can_edit_roles",
            "delete": "can_delete_roles",
        },
        "group": {
            "create": "can_create_group",
            "view": "can_view_group",
            "edit": "can_edit_group",
            "delete": "can_delete_group",
        },
    },
    "dataset_mgt": {
        # connect dataset includes create and view datasource and dataset, edit datasource
        "dataset": {
            "connect": "can_connect_dataset",
            "view": "can_view_dataset",
            "edit": "can_edit_dataset",
            "delete": "can_delete_dataset",
        },
        "rules": {
            "create": "can_create_rule",
            "edit": "can_edit_rule",
            "view": "can_view_rule",
            "delete": "can_delete_rule",
        },
    },
}


class PermissionNamespace:
    """
    A class representing a namespace of permissions.
    It is for representing permissions in a more syntactic sugar way
    """

    def __init__(self, permissions):
        """
        Initialize the PermissionNamespace with a dictionary of permissions.

        Args:
            permissions (dict): A dictionary containing permissions.
        """
        self.permissions = permissions

    def __getattr__(self, name):
        """
        Get the permission with the given name.

        If the permission exists and is a nested dictionary, return a new PermissionNamespace instance.
        If the permission exists and is not a dictionary, return the permission value.
        If the permission does not exist, return an empty string.

        Args:
            name (str): The name of the permission.

        Returns:
            str or PermissionNamespace: The permission value or a nested PermissionNamespace instance.
        """
        if name in self.permissions and isinstance(self.permissions[name], dict):
            return PermissionNamespace(self.permissions[name])
        return f"accounts.{self.permissions.get(name, '')}"


permissions = PermissionNamespace(PERMISSION_GROUPS)
